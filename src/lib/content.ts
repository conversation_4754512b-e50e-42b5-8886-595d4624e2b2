import contentData from "../../content.json";

// Type definitions for better TypeScript support
export interface Company {
  name: string;
  tagline: string;
  description: string;
  logo: string;
  companyLogo: string;
}

export interface ContactInfo {
  email: string;
  phone: string;
  address: string;
  businessHours: {
    weekdays: string;
    saturday: string;
    sunday: string;
  };
  parking: string;
  facilityTours: string;
}

export interface NavigationItem {
  name: string;
  href: string;
}

export interface SocialMedia {
  platform: string;
  href: string;
}

export interface Button {
  text: string;
  href?: string;
  action?: string;
  type: "primary" | "secondary";
}

export interface Product {
  id: number;
  slug: string;
  name: string;
  category: string;
  price: string;
  variant: string;
  description: string;
  longDescription: string;
  features: string[];
  applications: string[];
  image: string;
}

export interface TeamMember {
  name: string;
  position: string;
  image: string;
  description: string;
}

export interface Feature {
  title: string;
  description: string;
  icon: string;
  alt?: string;
  text?: string;
}

export interface Testimonial {
  stars: string;
  quote: string;
  customer: {
    avatar: string;
    name: string;
    position: string;
  };
  companyLogo: string;
}

export interface Stat {
  number: string;
  label: string;
  description: string;
}

// Content access functions
export const getCompanyInfo = (): Company => contentData.company;

export const getContactInfo = (): ContactInfo => contentData.contactInfo;

export const getNavigation = (): NavigationItem[] => contentData.navigation;

export const getSocialMedia = (): SocialMedia[] => contentData.socialMedia;

// Home page content
export const getHomeHero = () => contentData.home.hero;

export const getHomeFeatures = (): Feature[] => contentData.home.features;

export const getHomeServices = () => contentData.home.services;

export const getHomeTestimonial = (): Testimonial =>
  contentData.home.testimonial;

// Products page content
export const getProductsHero = () => contentData.products.hero;

export const getProductsCatalog = (): Product[] => contentData.products.catalog;

export const getProductsSpecs = () => contentData.products.specifications;

export const getProductsCTA = () => contentData.products.cta;

// About page content
export const getAboutHero = () => contentData.about.hero;

export const getAboutStory = () => contentData.about.story;

export const getAboutValues = (): Feature[] => contentData.about.values;

export const getAboutStats = (): Stat[] => contentData.about.stats;

export const getAboutTeam = (): TeamMember[] => contentData.about.team;

// Contact page content
export const getContactHero = () => contentData.contact.hero;

export const getContactMethods = () => contentData.contact.methods;

export const getContactLocation = () => contentData.contact.location;

// Footer content
export const getFooterLinks = () => contentData.footer;

// Metadata
export const getMetadata = () => contentData.metadata;

export const getPageMetadata = (
  page: "home" | "products" | "about" | "contact"
) => contentData.metadata.pages[page];

// Helper function to get product by slug
export const getProductBySlug = (slug: string): Product | undefined =>
  contentData.products.catalog.find((product) => product.slug === slug);

// Helper function to add new product
export const addProduct = (product: Omit<Product, "id">): Product => {
  const newId = Math.max(...contentData.products.catalog.map((p) => p.id)) + 1;
  const newProduct = { ...product, id: newId };
  contentData.products.catalog.push(newProduct);
  return newProduct;
};

// Export the entire content data for direct access if needed
export default contentData;
