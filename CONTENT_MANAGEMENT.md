# Content Management System

This document explains how to use the centralized content management system for your Spring Solutions website.

## Overview

All website content is now centralized in `content.json` and can be accessed through utility functions in `src/lib/content.ts`. This allows you to:

- Edit all content in one place
- Easily add new products
- Maintain consistency across pages
- Update content without touching component code

## File Structure

```
├── content.json                 # Main content file
├── src/lib/content.ts          # Utility functions to access content
├── example-usage.tsx           # Examples of how to use the content system
└── CONTENT_MANAGEMENT.md       # This documentation
```

## Content Structure

The `content.json` file is organized into the following sections:

### 1. Company Information

```json
{
  "company": {
    "name": "Spring Solutions",
    "tagline": "Precision Springs Manufacturing",
    "description": "...",
    "logo": "/logo.png",
    "companyLogo": "/company-logo.svg"
  }
}
```

### 2. Contact Information

```json
{
  "contactInfo": {
    "email": "<EMAIL>",
    "phone": "+****************",
    "address": "456 Spring Ave, Sydney NSW 2000 AU",
    "businessHours": { ... },
    "parking": "...",
    "facilityTours": "..."
  }
}
```

### 3. Navigation & Social Media

```json
{
  "navigation": [...],
  "socialMedia": [...]
}
```

### 4. Page Content

- `home`: Hero section, features, services, testimonials
- `products`: Hero, catalog, specifications, CTA
- `about`: Hero, story, values, stats, team
- `contact`: Hero, contact methods, location

### 5. Footer & Metadata

- Footer links and metadata for SEO

## How to Use

### 1. Import Content Functions

```typescript
import {
  getHomeHero,
  getProductsCatalog,
  getAboutTeam,
  getContactInfo,
} from "@/lib/content";
```

### 2. Use in Components

```typescript
export const MyComponent = () => {
  const heroContent = getHomeHero();
  const products = getProductsCatalog();

  return (
    <div>
      <h1>{heroContent.title}</h1>
      <p>{heroContent.description}</p>
      {products.map((product) => (
        <div key={product.id}>{product.name}</div>
      ))}
    </div>
  );
};
```

### 3. Available Functions

#### Company & Contact

- `getCompanyInfo()` - Company details
- `getContactInfo()` - Contact information
- `getNavigation()` - Navigation menu items
- `getSocialMedia()` - Social media links

#### Home Page

- `getHomeHero()` - Hero section content
- `getHomeFeatures()` - Feature cards
- `getHomeServices()` - Services section
- `getHomeTestimonial()` - Customer testimonial

#### Products Page

- `getProductsHero()` - Products hero section
- `getProductsCatalog()` - All products
- `getProductsSpecs()` - Technical specifications
- `getProductsCTA()` - Call-to-action section
- `getProductBySlug(slug)` - Single product by slug

#### About Page

- `getAboutHero()` - About hero section
- `getAboutStory()` - Company story
- `getAboutValues()` - Company values
- `getAboutStats()` - Company statistics
- `getAboutTeam()` - Team members

#### Contact Page

- `getContactHero()` - Contact hero section
- `getContactMethods()` - Contact methods
- `getContactLocation()` - Location information

#### Other

- `getFooterLinks()` - Footer navigation
- `getMetadata()` - SEO metadata
- `getPageMetadata(page)` - Page-specific metadata

## Adding New Products

### Method 1: Edit content.json directly

Add a new product to the `products.catalog` array:

```json
{
  "id": 4,
  "slug": "wire-forms",
  "name": "Wire Forms",
  "category": "Custom Solutions",
  "price": "$55",
  "variant": "Custom",
  "description": "Custom wire forms for specialized applications.",
  "longDescription": "...",
  "features": [...],
  "applications": [...],
  "image": "path/to/image.jpg"
}
```

### Method 2: Use the helper function

```typescript
import { addProduct } from '@/lib/content';

const newProduct = addProduct({
  slug: "wire-forms",
  name: "Wire Forms",
  category: "Custom Solutions",
  price: "$55",
  variant: "Custom",
  description: "Custom wire forms for specialized applications.",
  longDescription: "...",
  features: [...],
  applications: [...],
  image: "path/to/image.jpg"
});
```

## Updating Existing Components

To update your existing components to use the centralized content:

1. Import the relevant content functions
2. Replace hardcoded content with function calls
3. Update the component to use the data structure

See `example-usage.tsx` for detailed examples.

## Best Practices

1. **Always use the utility functions** instead of importing the JSON directly
2. **Update content.json** for content changes, not component files
3. **Test thoroughly** after adding new products or changing structure
4. **Keep consistent naming** for new content fields
5. **Use TypeScript types** provided in `content.ts` for better development experience

## Content Updates

To update content:

1. Edit `content.json`
2. Save the file
3. The changes will be reflected immediately in your application

No need to restart the development server or rebuild components!

## Troubleshooting

### Common Issues

1. **Missing content**: Check that the property exists in `content.json`
2. **Type errors**: Ensure the data structure matches the TypeScript interfaces
3. **Images not loading**: Verify image paths are correct

### Getting Help

If you need to add new content types or modify the structure, refer to the TypeScript interfaces in `src/lib/content.ts` for guidance on the expected data format.
