{"version": 3, "sources": ["../../src/cli/next-start.ts"], "sourcesContent": ["#!/usr/bin/env node\n\nimport '../server/lib/cpu-profile'\nimport { startServer } from '../server/lib/start-server'\nimport { printAndExit } from '../server/lib/utils'\nimport { getProjectDir } from '../lib/get-project-dir'\nimport {\n  getReservedPortExplanation,\n  isPortIsReserved,\n} from '../lib/helpers/get-reserved-port'\n\nexport type NextStartOptions = {\n  port: number\n  hostname?: string\n  keepAliveTimeout?: number\n  turbo?: boolean\n  turbopack?: boolean\n}\n\n/**\n * Start the Next.js server\n *\n * @param options The options for the start command\n * @param directory The directory to start the server in\n */\nconst nextStart = async (options: NextStartOptions, directory?: string) => {\n  const dir = getProjectDir(directory)\n  const hostname = options.hostname\n  const port = options.port\n  const keepAliveTimeout = options.keepAliveTimeout\n\n  if (isPortIsReserved(port)) {\n    printAndExit(getReservedPortExplanation(port), 1)\n  }\n\n  await startServer({\n    dir,\n    isDev: false,\n    hostname,\n    port,\n    keepAliveTimeout,\n  })\n}\n\nexport { nextStart }\n"], "names": ["nextStart", "options", "directory", "dir", "getProjectDir", "hostname", "port", "keepAliveTimeout", "isPortIsReserved", "printAndExit", "getReservedPortExplanation", "startServer", "isDev"], "mappings": ";;;;;+BA4CS<PERSON>;;;eAAAA;;;QA1CF;6BACqB;uBACC;+BACC;iCAIvB;AAUP;;;;;CAKC,GACD,MAAMA,YAAY,OAAOC,SAA2BC;IAClD,MAAMC,MAAMC,IAAAA,4BAAa,EAACF;IAC1B,MAAMG,WAAWJ,QAAQI,QAAQ;IACjC,MAAMC,OAAOL,QAAQK,IAAI;IACzB,MAAMC,mBAAmBN,QAAQM,gBAAgB;IAEjD,IAAIC,IAAAA,iCAAgB,EAACF,OAAO;QAC1BG,IAAAA,mBAAY,EAACC,IAAAA,2CAA0B,EAACJ,OAAO;IACjD;IAEA,MAAMK,IAAAA,wBAAW,EAAC;QAChBR;QACAS,OAAO;QACPP;QACAC;QACAC;IACF;AACF"}