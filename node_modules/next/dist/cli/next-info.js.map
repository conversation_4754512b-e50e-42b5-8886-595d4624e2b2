{"version": 3, "sources": ["../../src/cli/next-info.ts"], "sourcesContent": ["#!/usr/bin/env node\n\nimport os from 'os'\nimport childProcess from 'child_process'\n\nimport { bold, cyan, yellow } from '../lib/picocolors'\nimport { PHASE_INFO } from '../shared/lib/constants'\nimport loadConfig from '../server/config'\nimport { getRegistry } from '../lib/helpers/get-registry'\nimport { parseVersionInfo } from '../server/dev/parse-version-info'\nimport { getStaleness } from '../client/components/react-dev-overlay/ui/components/version-staleness-info/version-staleness-info'\nimport { warn } from '../build/output/log'\n\nexport type NextInfoOptions = {\n  verbose?: boolean\n}\n\ntype TaskResult = {\n  // Additional messages to notify to the users, i.e certain script cannot be run due to missing xyz.\n  messages?: string | undefined\n  // Output of the script, either fails or success. This'll be printed to stdout or written into a file.\n  output?: string | undefined\n  result: 'pass' | 'fail' | 'skipped'\n}\n\ntype TaskScript = () => Promise<TaskResult>\n\ntype PlatformTaskScript =\n  | {\n      win32: TaskScript\n      linux?: TaskScript\n      darwin?: TaskScript\n      default?: TaskScript\n    }\n  | {\n      linux: TaskScript\n      win32?: TaskScript\n      darwin?: TaskScript\n      default?: TaskScript\n    }\n  | {\n      darwin: TaskScript\n      win32?: TaskScript\n      linux?: TaskScript\n      default?: TaskScript\n    }\n  | {\n      // A common task script if task does not need to be platform specific.\n      default: TaskScript\n      win32?: TaskScript\n      linux?: TaskScript\n      darwin?: TaskScript\n    }\n\nfunction getPackageVersion(packageName: string) {\n  try {\n    return require(`${packageName}/package.json`).version\n  } catch {\n    return 'N/A'\n  }\n}\n\nasync function getNextConfig() {\n  const config = await loadConfig(PHASE_INFO, process.cwd())\n\n  return {\n    output: config.output ?? 'N/A',\n    experimental: {\n      useWasmBinary: config.experimental?.useWasmBinary,\n    },\n  }\n}\n\n/**\n * Returns the version of the specified binary, by supplying `--version` argument.\n * N/A if it fails to run the binary.\n */\nfunction getBinaryVersion(binaryName: string) {\n  try {\n    return childProcess\n      .execFileSync(binaryName, ['--version'])\n      .toString()\n      .trim()\n  } catch {\n    try {\n      return childProcess.execSync(`${binaryName} --version`).toString().trim()\n    } catch {\n      return 'N/A'\n    }\n  }\n}\n\n/**\n * Collect basic next.js installation information and print it to stdout.\n */\nasync function printInfo() {\n  const installedRelease = getPackageVersion('next')\n  const nextConfig = await getNextConfig()\n\n  let stalenessWithTitle = ''\n  let title = ''\n  let versionInfo\n\n  try {\n    const registry = getRegistry()\n    const res = await fetch(`${registry}-/package/next/dist-tags`)\n    const tags = await res.json()\n\n    versionInfo = parseVersionInfo({\n      installed: installedRelease,\n      latest: tags.latest,\n      canary: tags.canary,\n    })\n\n    title = getStaleness(versionInfo).title\n\n    if (title) {\n      stalenessWithTitle = ` // ${title}`\n    }\n  } catch (e) {\n    console.warn(\n      `${yellow(\n        bold('warn')\n      )}  - Failed to fetch latest canary version. (Reason: ${\n        (e as Error).message\n      }.)\n      Detected \"${installedRelease}\". Visit https://github.com/vercel/next.js/releases.\n      Make sure to try the latest canary version (eg.: \\`npm install next@canary\\`) to confirm the issue still exists before creating a new issue.\n      \\nLearn more: ${cyan(\n        'https://nextjs.org/docs/messages/opening-an-issue'\n      )}`\n    )\n  }\n\n  const cpuCores = os.cpus().length\n  let relevantPackages = `  next: ${installedRelease}${stalenessWithTitle}\n  eslint-config-next: ${getPackageVersion('eslint-config-next')}\n  react: ${getPackageVersion('react')}\n  react-dom: ${getPackageVersion('react-dom')}\n  typescript: ${getPackageVersion('typescript')}`\n\n  if (process.env.NEXT_RSPACK) {\n    relevantPackages += `\n  next-rspack: ${getPackageVersion('next-rspack')}`\n  }\n\n  console.log(`\nOperating System:\n  Platform: ${os.platform()}\n  Arch: ${os.arch()}\n  Version: ${os.version()}\n  Available memory (MB): ${Math.ceil(os.totalmem() / 1024 / 1024)}\n  Available CPU cores: ${cpuCores > 0 ? cpuCores : 'N/A'}\nBinaries:\n  Node: ${process.versions.node}\n  npm: ${getBinaryVersion('npm')}\n  Yarn: ${getBinaryVersion('yarn')}\n  pnpm: ${getBinaryVersion('pnpm')}\nRelevant Packages:\n${relevantPackages}\nNext.js Config:\n  output: ${nextConfig.output}`)\n\n  if (versionInfo?.staleness.startsWith('stale')) {\n    warn(`${title}\n   Please try the latest canary version (\\`npm install next@canary\\`) to confirm the issue still exists before creating a new issue.\n   Read more - https://nextjs.org/docs/messages/opening-an-issue`)\n  }\n}\n\n/**\n * Using system-installed tools per each platform, trying to read shared dependencies of next-swc.\n * This is mainly for debugging DLOPEN failure.\n *\n * We don't / can't install these tools by ourselves, will skip the check if we can't find them.\n */\nasync function runSharedDependencyCheck(\n  tools: Array<{ bin: string; checkArgs: Array<string>; args: Array<string> }>,\n  skipMessage: string\n): Promise<TaskResult> {\n  const currentPlatform = os.platform()\n  const spawn = require('next/dist/compiled/cross-spawn')\n  const { getSupportedArchTriples } = require('../build/swc')\n  const triples = getSupportedArchTriples()[currentPlatform]?.[os.arch()] ?? []\n  // First, check if system have a tool installed. We can't install these by our own.\n\n  const availableTools = []\n  for (const tool of tools) {\n    try {\n      const check = spawn.sync(tool.bin, tool.checkArgs)\n      if (check.status === 0) {\n        availableTools.push(tool)\n      }\n    } catch {\n      // ignore if existence check fails\n    }\n  }\n\n  if (availableTools.length === 0) {\n    return {\n      messages: skipMessage,\n      result: 'skipped',\n    }\n  }\n\n  const outputs: Array<string> = []\n  let result: 'pass' | 'fail' = 'fail'\n\n  for (const triple of triples) {\n    const triplePkgName = `@next/swc-${triple.platformArchABI}`\n    let resolved\n    try {\n      resolved = require.resolve(triplePkgName)\n    } catch (e) {\n      return {\n        messages:\n          'Cannot find next-swc installation, skipping dependencies check',\n        result: 'skipped',\n      }\n    }\n\n    for (const tool of availableTools) {\n      const proc = spawn(tool.bin, [...tool.args, resolved])\n      outputs.push(`Running ${tool.bin} ------------- `)\n      // Captures output, doesn't matter if it fails or not since we'll forward both to output.\n      const procPromise = new Promise((resolve) => {\n        proc.stdout.on('data', function (data: string) {\n          outputs.push(data)\n        })\n        proc.stderr.on('data', function (data: string) {\n          outputs.push(data)\n        })\n        proc.on('close', (c: any) => resolve(c))\n      })\n\n      let code = await procPromise\n      if (code === 0) {\n        result = 'pass'\n      }\n    }\n  }\n\n  return {\n    output: outputs.join('\\n'),\n    result,\n  }\n}\n\n/**\n * Collect additional diagnostics information.\n */\nasync function printVerboseInfo() {\n  const fs = require('fs')\n  const currentPlatform = os.platform()\n\n  if (\n    currentPlatform !== 'win32' &&\n    currentPlatform !== 'linux' &&\n    currentPlatform !== 'darwin'\n  ) {\n    console.log(\n      'Unsupported platform, only win32, linux, darwin are supported.'\n    )\n    return\n  }\n\n  // List of tasks to run.\n  const tasks: Array<{\n    title: string\n    // If specified, only run this task on the specified platform.\n    targetPlatform?: string | undefined\n    scripts: PlatformTaskScript\n  }> = [\n    {\n      title: 'Host system information',\n      scripts: {\n        default: async () => {\n          // Node.js diagnostic report contains basic information, i.e OS version, CPU architecture, etc.\n          // Only collect few addtional details here.\n          const isWsl = require('next/dist/compiled/is-wsl')\n          const ciInfo = require('next/dist/compiled/ci-info')\n          const isDocker = require('next/dist/compiled/is-docker')\n\n          const output = `\n  WSL: ${isWsl}\n  Docker: ${isDocker()}\n  CI: ${ciInfo.isCI ? ciInfo.name || 'unknown' : 'false'}\n`\n\n          return {\n            output,\n            result: 'pass',\n          }\n        },\n      },\n    },\n    {\n      title: 'Next.js installation',\n      scripts: {\n        default: async () => {\n          const installedRelease = getPackageVersion('next')\n          const nextConfig = await getNextConfig()\n          const output = `\n  Binaries:\n    Node: ${process.versions.node}\n    npm: ${getBinaryVersion('npm')}\n    Yarn: ${getBinaryVersion('yarn')}\n    pnpm: ${getBinaryVersion('pnpm')}\n  Relevant Packages:\n    next: ${installedRelease}\n    eslint-config-next: ${getPackageVersion('eslint-config-next')}\n    react: ${getPackageVersion('react')}\n    react-dom: ${getPackageVersion('react-dom')}\n    typescript: ${getPackageVersion('typescript')}\n  Next.js Config:\n    output: ${nextConfig.output}\n\n`\n          return {\n            output,\n            result: 'pass',\n          }\n        },\n      },\n    },\n    {\n      title: 'Node.js diagnostic report',\n      scripts: {\n        default: async () => {\n          const report = process.report?.getReport()\n\n          if (!report) {\n            return {\n              messages: 'Node.js diagnostic report is not available.',\n              result: 'fail',\n            }\n          }\n\n          const { header, javascriptHeap, sharedObjects } =\n            report as any as Record<string, any>\n          // Delete some fields potentially containing sensitive information.\n          delete header?.cwd\n          delete header?.commandLine\n          delete header?.host\n          delete header?.cpus\n          delete header?.networkInterfaces\n\n          const reportSummary = {\n            header,\n            javascriptHeap,\n            sharedObjects,\n          }\n\n          return {\n            output: JSON.stringify(reportSummary, null, 2),\n            result: 'pass',\n          }\n        },\n      },\n    },\n    {\n      title: 'next-swc installation',\n      scripts: {\n        default: async () => {\n          const output = [] as any\n\n          // First, try to load next-swc via loadBindings.\n          try {\n            let nextConfig = await getNextConfig()\n            const { loadBindings } = require('../build/swc')\n            const bindings = await loadBindings(\n              nextConfig.experimental?.useWasmBinary\n            )\n            // Run arbitrary function to verify the bindings are loaded correctly.\n            const target = bindings.getTargetTriple()\n\n            // We think next-swc is installed correctly if getTargetTriple returns.\n            return {\n              output: `next-swc is installed correctly for ${target}`,\n              result: 'pass',\n            }\n          } catch (e) {\n            output.push(`loadBindings() failed: ${(e as Error).message}`)\n          }\n\n          const {\n            platformArchTriples,\n          } = require('next/dist/compiled/@napi-rs/triples')\n          const triples = platformArchTriples[currentPlatform]?.[os.arch()]\n\n          if (!triples || triples.length === 0) {\n            return {\n              messages: `No target triples found for ${currentPlatform} / ${os.arch()}`,\n              result: 'fail',\n            }\n          }\n\n          // Trying to manually resolve corresponding target triples to see if bindings are physically located.\n          const path = require('path')\n          let fallbackBindingsDirectory\n          try {\n            const nextPath = path.dirname(require.resolve('next/package.json'))\n            fallbackBindingsDirectory = path.join(nextPath, 'next-swc-fallback')\n          } catch (e) {\n            // Not able to locate next package from current running location, skipping fallback bindings check.\n          }\n\n          const tryResolve = (pkgName: string) => {\n            try {\n              const resolved = require.resolve(pkgName)\n              const fileExists = fs.existsSync(resolved)\n              let loadError\n              let loadSuccess\n\n              try {\n                loadSuccess = !!require(resolved).getTargetTriple()\n              } catch (e) {\n                loadError = (e as Error).message\n              }\n\n              output.push(\n                `${pkgName} exists: ${fileExists} for the triple ${loadSuccess}`\n              )\n              if (loadError) {\n                output.push(`${pkgName} load failed: ${loadError ?? 'unknown'}`)\n              }\n\n              if (loadSuccess) {\n                return true\n              }\n            } catch (e) {\n              output.push(\n                `${pkgName} resolve failed: ${\n                  (e as Error).message ?? 'unknown'\n                }`\n              )\n            }\n            return false\n          }\n\n          for (const triple of triples) {\n            const triplePkgName = `@next/swc-${triple.platformArchABI}`\n            // Check installed optional dependencies. This is the normal way package being installed.\n            // For the targets have multiple triples (gnu / musl), if any of them loads successfully, we consider as installed.\n            if (tryResolve(triplePkgName)) {\n              break\n            }\n\n            // Check if fallback binaries are installed.\n            if (!fallbackBindingsDirectory) {\n              continue\n            }\n\n            tryResolve(path.join(fallbackBindingsDirectory, triplePkgName))\n          }\n\n          return {\n            output: output.join('\\n'),\n            result: 'pass',\n          }\n        },\n      },\n    },\n    {\n      // For the simplicity, we only check the correctly installed optional dependencies -\n      // as this is mainly for checking DLOPEN failure. If user hit MODULE_NOT_FOUND,\n      // expect above next-swc installation would give some hint instead.\n      title: 'next-swc shared object dependencies',\n      scripts: {\n        linux: async () => {\n          const skipMessage =\n            'This diagnostics uses system-installed tools (ldd) to check next-swc dependencies, but it is not found. Skipping dependencies check.'\n\n          return await runSharedDependencyCheck(\n            [\n              {\n                bin: 'ldd',\n                checkArgs: ['--help'],\n                args: ['--verbose'],\n              },\n            ],\n            skipMessage\n          )\n        },\n        win32: async () => {\n          const skipMessage = `This diagnostics uses system-installed tools (dumpbin.exe) to check next-swc dependencies, but it was not found in the path. Skipping dependencies check.\n          dumpbin (https://learn.microsoft.com/en-us/cpp/build/reference/dumpbin-reference) is a part of Microsoft VC toolset,\n          can be installed with Windows SDK, Windows Build tools or Visual Studio.\n\n          Please make sure you have one of them installed and dumpbin.exe is in the path.\n          `\n\n          return await runSharedDependencyCheck(\n            [\n              {\n                bin: 'dumpbin.exe',\n                checkArgs: ['/summary'],\n                args: ['/imports'],\n              },\n            ],\n            skipMessage\n          )\n        },\n        darwin: async () => {\n          const skipMessage =\n            'This diagnostics uses system-installed tools (otools, dyld_info) to check next-swc dependencies, but none of them are found. Skipping dependencies check.'\n\n          return await runSharedDependencyCheck(\n            [\n              {\n                bin: 'otool',\n                checkArgs: ['--version'],\n                args: ['-L'],\n              },\n              {\n                bin: 'dyld_info',\n                checkArgs: [],\n                args: [],\n              },\n            ],\n            skipMessage\n          )\n        },\n      },\n    },\n  ]\n\n  // Collected output after running all tasks.\n  const report: Array<{\n    title: string\n    result: TaskResult\n  }> = []\n\n  console.log('\\n')\n  for (const task of tasks) {\n    if (task.targetPlatform && task.targetPlatform !== currentPlatform) {\n      report.push({\n        title: task.title,\n        result: {\n          messages: undefined,\n          output: `[SKIPPED (${os.platform()} / ${task.targetPlatform})] ${\n            task.title\n          }`,\n          result: 'skipped',\n        },\n      })\n      continue\n    }\n\n    const taskScript = task.scripts[currentPlatform] ?? task.scripts.default!\n    let taskResult: TaskResult\n    try {\n      taskResult = await taskScript()\n    } catch (e) {\n      taskResult = {\n        messages: `Unexpected failure while running diagnostics: ${\n          (e as Error).message\n        }`,\n        result: 'fail',\n      }\n    }\n\n    console.log(`- ${task.title}: ${taskResult.result}`)\n    if (taskResult.messages) {\n      console.log(`  ${taskResult.messages}`)\n    }\n\n    report.push({\n      title: task.title,\n      result: taskResult,\n    })\n  }\n\n  console.log(`\\n${bold('Generated diagnostics report')}`)\n\n  console.log(`\\nPlease copy below report and paste it into your issue.`)\n  for (const { title, result } of report) {\n    console.log(`\\n### ${title}`)\n\n    if (result.messages) {\n      console.log(result.messages)\n    }\n\n    if (result.output) {\n      console.log(result.output)\n    }\n  }\n}\n\n/**\n * Runs few scripts to collect system information to help with debugging next.js installation issues.\n * There are 2 modes, by default it collects basic next.js installation with runtime information. If\n * `--verbose` mode is enabled it'll try to collect, verify more data for next-swc installation and others.\n */\nconst nextInfo = async (options: NextInfoOptions) => {\n  if (options.verbose) {\n    await printVerboseInfo()\n  } else {\n    await printInfo()\n  }\n}\n\nexport { nextInfo }\n"], "names": ["nextInfo", "getPackageVersion", "packageName", "require", "version", "getNextConfig", "config", "loadConfig", "PHASE_INFO", "process", "cwd", "output", "experimental", "useWasmBinary", "getBinaryVersion", "binaryName", "childProcess", "execFileSync", "toString", "trim", "execSync", "printInfo", "installedRelease", "nextConfig", "stalenessWithTitle", "title", "versionInfo", "registry", "getRegistry", "res", "fetch", "tags", "json", "parseVersionInfo", "installed", "latest", "canary", "getStaleness", "e", "console", "warn", "yellow", "bold", "message", "cyan", "cpuCores", "os", "cpus", "length", "relevantPackages", "env", "NEXT_RSPACK", "log", "platform", "arch", "Math", "ceil", "totalmem", "versions", "node", "staleness", "startsWith", "runSharedDependencyCheck", "tools", "skipMessage", "getSupportedArchTriples", "currentPlatform", "spawn", "triples", "availableTools", "tool", "check", "sync", "bin", "checkArgs", "status", "push", "messages", "result", "outputs", "triple", "triplePkgName", "platformArchABI", "resolved", "resolve", "proc", "args", "procPromise", "Promise", "stdout", "on", "data", "stderr", "c", "code", "join", "printVerboseInfo", "fs", "tasks", "scripts", "default", "isWsl", "ciInfo", "is<PERSON>ock<PERSON>", "isCI", "name", "report", "getReport", "header", "javascriptHeap", "sharedObjects", "commandLine", "host", "networkInterfaces", "reportSummary", "JSON", "stringify", "platformArchTriples", "loadBindings", "bindings", "target", "getTargetTriple", "path", "fallbackBindingsDirectory", "nextPath", "dirname", "tryResolve", "pkgName", "fileExists", "existsSync", "loadError", "loadSuccess", "linux", "win32", "darwin", "task", "targetPlatform", "undefined", "taskScript", "taskResult", "options", "verbose"], "mappings": ";;;;;+BA0lBSA;;;eAAAA;;;2DAxlBM;sEACU;4BAEU;2BACR;+DACJ;6BACK;kCACK;sCACJ;qBACR;;;;;;AA2CrB,SAASC,kBAAkBC,WAAmB;IAC5C,IAAI;QACF,OAAOC,QAAQ,GAAGD,YAAY,aAAa,CAAC,EAAEE,OAAO;IACvD,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEA,eAAeC;QAMMC;IALnB,MAAMA,SAAS,MAAMC,IAAAA,eAAU,EAACC,qBAAU,EAAEC,QAAQC,GAAG;IAEvD,OAAO;QACLC,QAAQL,OAAOK,MAAM,IAAI;QACzBC,cAAc;YACZC,aAAa,GAAEP,uBAAAA,OAAOM,YAAY,qBAAnBN,qBAAqBO,aAAa;QACnD;IACF;AACF;AAEA;;;CAGC,GACD,SAASC,iBAAiBC,UAAkB;IAC1C,IAAI;QACF,OAAOC,sBAAY,CAChBC,YAAY,CAACF,YAAY;YAAC;SAAY,EACtCG,QAAQ,GACRC,IAAI;IACT,EAAE,OAAM;QACN,IAAI;YACF,OAAOH,sBAAY,CAACI,QAAQ,CAAC,GAAGL,WAAW,UAAU,CAAC,EAAEG,QAAQ,GAAGC,IAAI;QACzE,EAAE,OAAM;YACN,OAAO;QACT;IACF;AACF;AAEA;;CAEC,GACD,eAAeE;IACb,MAAMC,mBAAmBrB,kBAAkB;IAC3C,MAAMsB,aAAa,MAAMlB;IAEzB,IAAImB,qBAAqB;IACzB,IAAIC,QAAQ;IACZ,IAAIC;IAEJ,IAAI;QACF,MAAMC,WAAWC,IAAAA,wBAAW;QAC5B,MAAMC,MAAM,MAAMC,MAAM,GAAGH,SAAS,wBAAwB,CAAC;QAC7D,MAAMI,OAAO,MAAMF,IAAIG,IAAI;QAE3BN,cAAcO,IAAAA,kCAAgB,EAAC;YAC7BC,WAAWZ;YACXa,QAAQJ,KAAKI,MAAM;YACnBC,QAAQL,KAAKK,MAAM;QACrB;QAEAX,QAAQY,IAAAA,kCAAY,EAACX,aAAaD,KAAK;QAEvC,IAAIA,OAAO;YACTD,qBAAqB,CAAC,IAAI,EAAEC,OAAO;QACrC;IACF,EAAE,OAAOa,GAAG;QACVC,QAAQC,IAAI,CACV,GAAGC,IAAAA,kBAAM,EACPC,IAAAA,gBAAI,EAAC,SACL,oDAAoD,EACpD,AAACJ,EAAYK,OAAO,CACrB;gBACS,EAAErB,iBAAiB;;oBAEf,EAAEsB,IAAAA,gBAAI,EAClB,sDACC;IAEP;IAEA,MAAMC,WAAWC,WAAE,CAACC,IAAI,GAAGC,MAAM;IACjC,IAAIC,mBAAmB,CAAC,QAAQ,EAAE3B,mBAAmBE,mBAAmB;sBACpD,EAAEvB,kBAAkB,sBAAsB;SACvD,EAAEA,kBAAkB,SAAS;aACzB,EAAEA,kBAAkB,aAAa;cAChC,EAAEA,kBAAkB,eAAe;IAE/C,IAAIQ,QAAQyC,GAAG,CAACC,WAAW,EAAE;QAC3BF,oBAAoB,CAAC;eACV,EAAEhD,kBAAkB,gBAAgB;IACjD;IAEAsC,QAAQa,GAAG,CAAC,CAAC;;YAEH,EAAEN,WAAE,CAACO,QAAQ,GAAG;QACpB,EAAEP,WAAE,CAACQ,IAAI,GAAG;WACT,EAAER,WAAE,CAAC1C,OAAO,GAAG;yBACD,EAAEmD,KAAKC,IAAI,CAACV,WAAE,CAACW,QAAQ,KAAK,OAAO,MAAM;uBAC3C,EAAEZ,WAAW,IAAIA,WAAW,MAAM;;QAEjD,EAAEpC,QAAQiD,QAAQ,CAACC,IAAI,CAAC;OACzB,EAAE7C,iBAAiB,OAAO;QACzB,EAAEA,iBAAiB,QAAQ;QAC3B,EAAEA,iBAAiB,QAAQ;;AAEnC,EAAEmC,iBAAiB;;UAET,EAAE1B,WAAWZ,MAAM,EAAE;IAE7B,IAAIe,+BAAAA,YAAakC,SAAS,CAACC,UAAU,CAAC,UAAU;QAC9CrB,IAAAA,SAAI,EAAC,GAAGf,MAAM;;gEAE8C,CAAC;IAC/D;AACF;AAEA;;;;;CAKC,GACD,eAAeqC,yBACbC,KAA4E,EAC5EC,WAAmB;QAKHC;IAHhB,MAAMC,kBAAkBpB,WAAE,CAACO,QAAQ;IACnC,MAAMc,QAAQhE,QAAQ;IACtB,MAAM,EAAE8D,uBAAuB,EAAE,GAAG9D,QAAQ;IAC5C,MAAMiE,UAAUH,EAAAA,2CAAAA,yBAAyB,CAACC,gBAAgB,qBAA1CD,wCAA4C,CAACnB,WAAE,CAACQ,IAAI,GAAG,KAAI,EAAE;IAC7E,mFAAmF;IAEnF,MAAMe,iBAAiB,EAAE;IACzB,KAAK,MAAMC,QAAQP,MAAO;QACxB,IAAI;YACF,MAAMQ,QAAQJ,MAAMK,IAAI,CAACF,KAAKG,GAAG,EAAEH,KAAKI,SAAS;YACjD,IAAIH,MAAMI,MAAM,KAAK,GAAG;gBACtBN,eAAeO,IAAI,CAACN;YACtB;QACF,EAAE,OAAM;QACN,kCAAkC;QACpC;IACF;IAEA,IAAID,eAAerB,MAAM,KAAK,GAAG;QAC/B,OAAO;YACL6B,UAAUb;YACVc,QAAQ;QACV;IACF;IAEA,MAAMC,UAAyB,EAAE;IACjC,IAAID,SAA0B;IAE9B,KAAK,MAAME,UAAUZ,QAAS;QAC5B,MAAMa,gBAAgB,CAAC,UAAU,EAAED,OAAOE,eAAe,EAAE;QAC3D,IAAIC;QACJ,IAAI;YACFA,WAAWhF,QAAQiF,OAAO,CAACH;QAC7B,EAAE,OAAO3C,GAAG;YACV,OAAO;gBACLuC,UACE;gBACFC,QAAQ;YACV;QACF;QAEA,KAAK,MAAMR,QAAQD,eAAgB;YACjC,MAAMgB,OAAOlB,MAAMG,KAAKG,GAAG,EAAE;mBAAIH,KAAKgB,IAAI;gBAAEH;aAAS;YACrDJ,QAAQH,IAAI,CAAC,CAAC,QAAQ,EAAEN,KAAKG,GAAG,CAAC,eAAe,CAAC;YACjD,yFAAyF;YACzF,MAAMc,cAAc,IAAIC,QAAQ,CAACJ;gBAC/BC,KAAKI,MAAM,CAACC,EAAE,CAAC,QAAQ,SAAUC,IAAY;oBAC3CZ,QAAQH,IAAI,CAACe;gBACf;gBACAN,KAAKO,MAAM,CAACF,EAAE,CAAC,QAAQ,SAAUC,IAAY;oBAC3CZ,QAAQH,IAAI,CAACe;gBACf;gBACAN,KAAKK,EAAE,CAAC,SAAS,CAACG,IAAWT,QAAQS;YACvC;YAEA,IAAIC,OAAO,MAAMP;YACjB,IAAIO,SAAS,GAAG;gBACdhB,SAAS;YACX;QACF;IACF;IAEA,OAAO;QACLnE,QAAQoE,QAAQgB,IAAI,CAAC;QACrBjB;IACF;AACF;AAEA;;CAEC,GACD,eAAekB;IACb,MAAMC,KAAK9F,QAAQ;IACnB,MAAM+D,kBAAkBpB,WAAE,CAACO,QAAQ;IAEnC,IACEa,oBAAoB,WACpBA,oBAAoB,WACpBA,oBAAoB,UACpB;QACA3B,QAAQa,GAAG,CACT;QAEF;IACF;IAEA,wBAAwB;IACxB,MAAM8C,QAKD;QACH;YACEzE,OAAO;YACP0E,SAAS;gBACPC,SAAS;oBACP,+FAA+F;oBAC/F,2CAA2C;oBAC3C,MAAMC,QAAQlG,QAAQ;oBACtB,MAAMmG,SAASnG,QAAQ;oBACvB,MAAMoG,WAAWpG,QAAQ;oBAEzB,MAAMQ,SAAS,CAAC;OACnB,EAAE0F,MAAM;UACL,EAAEE,WAAW;MACjB,EAAED,OAAOE,IAAI,GAAGF,OAAOG,IAAI,IAAI,YAAY,QAAQ;AACzD,CAAC;oBAES,OAAO;wBACL9F;wBACAmE,QAAQ;oBACV;gBACF;YACF;QACF;QACA;YACErD,OAAO;YACP0E,SAAS;gBACPC,SAAS;oBACP,MAAM9E,mBAAmBrB,kBAAkB;oBAC3C,MAAMsB,aAAa,MAAMlB;oBACzB,MAAMM,SAAS,CAAC;;UAEhB,EAAEF,QAAQiD,QAAQ,CAACC,IAAI,CAAC;SACzB,EAAE7C,iBAAiB,OAAO;UACzB,EAAEA,iBAAiB,QAAQ;UAC3B,EAAEA,iBAAiB,QAAQ;;UAE3B,EAAEQ,iBAAiB;wBACL,EAAErB,kBAAkB,sBAAsB;WACvD,EAAEA,kBAAkB,SAAS;eACzB,EAAEA,kBAAkB,aAAa;gBAChC,EAAEA,kBAAkB,cAAc;;YAEtC,EAAEsB,WAAWZ,MAAM,CAAC;;AAEhC,CAAC;oBACS,OAAO;wBACLA;wBACAmE,QAAQ;oBACV;gBACF;YACF;QACF;QACA;YACErD,OAAO;YACP0E,SAAS;gBACPC,SAAS;wBACQ3F;oBAAf,MAAMiG,UAASjG,kBAAAA,QAAQiG,MAAM,qBAAdjG,gBAAgBkG,SAAS;oBAExC,IAAI,CAACD,QAAQ;wBACX,OAAO;4BACL7B,UAAU;4BACVC,QAAQ;wBACV;oBACF;oBAEA,MAAM,EAAE8B,MAAM,EAAEC,cAAc,EAAEC,aAAa,EAAE,GAC7CJ;oBACF,mEAAmE;oBAC5DE,+BAAAA,OAAQlG,GAAG;oBACXkG,+BAAAA,OAAQG,WAAW;oBACnBH,+BAAAA,OAAQI,IAAI;oBACZJ,+BAAAA,OAAQ7D,IAAI;oBACZ6D,+BAAAA,OAAQK,iBAAiB;oBAEhC,MAAMC,gBAAgB;wBACpBN;wBACAC;wBACAC;oBACF;oBAEA,OAAO;wBACLnG,QAAQwG,KAAKC,SAAS,CAACF,eAAe,MAAM;wBAC5CpC,QAAQ;oBACV;gBACF;YACF;QACF;QACA;YACErD,OAAO;YACP0E,SAAS;gBACPC,SAAS;wBAyBSiB;oBAxBhB,MAAM1G,SAAS,EAAE;oBAEjB,gDAAgD;oBAChD,IAAI;4BAIAY;wBAHF,IAAIA,aAAa,MAAMlB;wBACvB,MAAM,EAAEiH,YAAY,EAAE,GAAGnH,QAAQ;wBACjC,MAAMoH,WAAW,MAAMD,cACrB/F,2BAAAA,WAAWX,YAAY,qBAAvBW,yBAAyBV,aAAa;wBAExC,sEAAsE;wBACtE,MAAM2G,SAASD,SAASE,eAAe;wBAEvC,uEAAuE;wBACvE,OAAO;4BACL9G,QAAQ,CAAC,oCAAoC,EAAE6G,QAAQ;4BACvD1C,QAAQ;wBACV;oBACF,EAAE,OAAOxC,GAAG;wBACV3B,OAAOiE,IAAI,CAAC,CAAC,uBAAuB,EAAE,AAACtC,EAAYK,OAAO,EAAE;oBAC9D;oBAEA,MAAM,EACJ0E,mBAAmB,EACpB,GAAGlH,QAAQ;oBACZ,MAAMiE,WAAUiD,uCAAAA,mBAAmB,CAACnD,gBAAgB,qBAApCmD,oCAAsC,CAACvE,WAAE,CAACQ,IAAI,GAAG;oBAEjE,IAAI,CAACc,WAAWA,QAAQpB,MAAM,KAAK,GAAG;wBACpC,OAAO;4BACL6B,UAAU,CAAC,4BAA4B,EAAEX,gBAAgB,GAAG,EAAEpB,WAAE,CAACQ,IAAI,IAAI;4BACzEwB,QAAQ;wBACV;oBACF;oBAEA,qGAAqG;oBACrG,MAAM4C,OAAOvH,QAAQ;oBACrB,IAAIwH;oBACJ,IAAI;wBACF,MAAMC,WAAWF,KAAKG,OAAO,CAAC1H,QAAQiF,OAAO,CAAC;wBAC9CuC,4BAA4BD,KAAK3B,IAAI,CAAC6B,UAAU;oBAClD,EAAE,OAAOtF,GAAG;oBACV,mGAAmG;oBACrG;oBAEA,MAAMwF,aAAa,CAACC;wBAClB,IAAI;4BACF,MAAM5C,WAAWhF,QAAQiF,OAAO,CAAC2C;4BACjC,MAAMC,aAAa/B,GAAGgC,UAAU,CAAC9C;4BACjC,IAAI+C;4BACJ,IAAIC;4BAEJ,IAAI;gCACFA,cAAc,CAAC,CAAChI,QAAQgF,UAAUsC,eAAe;4BACnD,EAAE,OAAOnF,GAAG;gCACV4F,YAAY,AAAC5F,EAAYK,OAAO;4BAClC;4BAEAhC,OAAOiE,IAAI,CACT,GAAGmD,QAAQ,SAAS,EAAEC,WAAW,gBAAgB,EAAEG,aAAa;4BAElE,IAAID,WAAW;gCACbvH,OAAOiE,IAAI,CAAC,GAAGmD,QAAQ,cAAc,EAAEG,aAAa,WAAW;4BACjE;4BAEA,IAAIC,aAAa;gCACf,OAAO;4BACT;wBACF,EAAE,OAAO7F,GAAG;4BACV3B,OAAOiE,IAAI,CACT,GAAGmD,QAAQ,iBAAiB,EAC1B,AAACzF,EAAYK,OAAO,IAAI,WACxB;wBAEN;wBACA,OAAO;oBACT;oBAEA,KAAK,MAAMqC,UAAUZ,QAAS;wBAC5B,MAAMa,gBAAgB,CAAC,UAAU,EAAED,OAAOE,eAAe,EAAE;wBAC3D,yFAAyF;wBACzF,mHAAmH;wBACnH,IAAI4C,WAAW7C,gBAAgB;4BAC7B;wBACF;wBAEA,4CAA4C;wBAC5C,IAAI,CAAC0C,2BAA2B;4BAC9B;wBACF;wBAEAG,WAAWJ,KAAK3B,IAAI,CAAC4B,2BAA2B1C;oBAClD;oBAEA,OAAO;wBACLtE,QAAQA,OAAOoF,IAAI,CAAC;wBACpBjB,QAAQ;oBACV;gBACF;YACF;QACF;QACA;YACE,oFAAoF;YACpF,+EAA+E;YAC/E,mEAAmE;YACnErD,OAAO;YACP0E,SAAS;gBACPiC,OAAO;oBACL,MAAMpE,cACJ;oBAEF,OAAO,MAAMF,yBACX;wBACE;4BACEW,KAAK;4BACLC,WAAW;gCAAC;6BAAS;4BACrBY,MAAM;gCAAC;6BAAY;wBACrB;qBACD,EACDtB;gBAEJ;gBACAqE,OAAO;oBACL,MAAMrE,cAAc,CAAC;;;;;UAKrB,CAAC;oBAED,OAAO,MAAMF,yBACX;wBACE;4BACEW,KAAK;4BACLC,WAAW;gCAAC;6BAAW;4BACvBY,MAAM;gCAAC;6BAAW;wBACpB;qBACD,EACDtB;gBAEJ;gBACAsE,QAAQ;oBACN,MAAMtE,cACJ;oBAEF,OAAO,MAAMF,yBACX;wBACE;4BACEW,KAAK;4BACLC,WAAW;gCAAC;6BAAY;4BACxBY,MAAM;gCAAC;6BAAK;wBACd;wBACA;4BACEb,KAAK;4BACLC,WAAW,EAAE;4BACbY,MAAM,EAAE;wBACV;qBACD,EACDtB;gBAEJ;YACF;QACF;KACD;IAED,4CAA4C;IAC5C,MAAM0C,SAGD,EAAE;IAEPnE,QAAQa,GAAG,CAAC;IACZ,KAAK,MAAMmF,QAAQrC,MAAO;QACxB,IAAIqC,KAAKC,cAAc,IAAID,KAAKC,cAAc,KAAKtE,iBAAiB;YAClEwC,OAAO9B,IAAI,CAAC;gBACVnD,OAAO8G,KAAK9G,KAAK;gBACjBqD,QAAQ;oBACND,UAAU4D;oBACV9H,QAAQ,CAAC,UAAU,EAAEmC,WAAE,CAACO,QAAQ,GAAG,GAAG,EAAEkF,KAAKC,cAAc,CAAC,GAAG,EAC7DD,KAAK9G,KAAK,EACV;oBACFqD,QAAQ;gBACV;YACF;YACA;QACF;QAEA,MAAM4D,aAAaH,KAAKpC,OAAO,CAACjC,gBAAgB,IAAIqE,KAAKpC,OAAO,CAACC,OAAO;QACxE,IAAIuC;QACJ,IAAI;YACFA,aAAa,MAAMD;QACrB,EAAE,OAAOpG,GAAG;YACVqG,aAAa;gBACX9D,UAAU,CAAC,8CAA8C,EACvD,AAACvC,EAAYK,OAAO,EACpB;gBACFmC,QAAQ;YACV;QACF;QAEAvC,QAAQa,GAAG,CAAC,CAAC,EAAE,EAAEmF,KAAK9G,KAAK,CAAC,EAAE,EAAEkH,WAAW7D,MAAM,EAAE;QACnD,IAAI6D,WAAW9D,QAAQ,EAAE;YACvBtC,QAAQa,GAAG,CAAC,CAAC,EAAE,EAAEuF,WAAW9D,QAAQ,EAAE;QACxC;QAEA6B,OAAO9B,IAAI,CAAC;YACVnD,OAAO8G,KAAK9G,KAAK;YACjBqD,QAAQ6D;QACV;IACF;IAEApG,QAAQa,GAAG,CAAC,CAAC,EAAE,EAAEV,IAAAA,gBAAI,EAAC,iCAAiC;IAEvDH,QAAQa,GAAG,CAAC,CAAC,wDAAwD,CAAC;IACtE,KAAK,MAAM,EAAE3B,KAAK,EAAEqD,MAAM,EAAE,IAAI4B,OAAQ;QACtCnE,QAAQa,GAAG,CAAC,CAAC,MAAM,EAAE3B,OAAO;QAE5B,IAAIqD,OAAOD,QAAQ,EAAE;YACnBtC,QAAQa,GAAG,CAAC0B,OAAOD,QAAQ;QAC7B;QAEA,IAAIC,OAAOnE,MAAM,EAAE;YACjB4B,QAAQa,GAAG,CAAC0B,OAAOnE,MAAM;QAC3B;IACF;AACF;AAEA;;;;CAIC,GACD,MAAMX,WAAW,OAAO4I;IACtB,IAAIA,QAAQC,OAAO,EAAE;QACnB,MAAM7C;IACR,OAAO;QACL,MAAM3E;IACR;AACF"}