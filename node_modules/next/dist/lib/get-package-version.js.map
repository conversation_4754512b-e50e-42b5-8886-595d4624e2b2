{"version": 3, "sources": ["../../src/lib/get-package-version.ts"], "sourcesContent": ["import { promises as fs } from 'fs'\nimport findUp from 'next/dist/compiled/find-up'\nimport JSON5 from 'next/dist/compiled/json5'\nimport * as path from 'path'\n\ntype PackageJsonDependencies = {\n  dependencies: Record<string, string>\n  devDependencies: Record<string, string>\n}\n\nlet cachedDeps: Promise<PackageJsonDependencies>\n\nexport function getDependencies({\n  cwd,\n}: {\n  cwd: string\n}): Promise<PackageJsonDependencies> {\n  if (cachedDeps) {\n    return cachedDeps\n  }\n\n  return (cachedDeps = (async () => {\n    const configurationPath: string | undefined = await findUp('package.json', {\n      cwd,\n    })\n    if (!configurationPath) {\n      return { dependencies: {}, devDependencies: {} }\n    }\n\n    const content = await fs.readFile(configurationPath, 'utf-8')\n    const packageJson: any = JSON5.parse(content)\n\n    const { dependencies = {}, devDependencies = {} } = packageJson || {}\n    return { dependencies, devDependencies }\n  })())\n}\n\nexport async function getPackageVersion({\n  cwd,\n  name,\n}: {\n  cwd: string\n  name: string\n}): Promise<string | null> {\n  const { dependencies, devDependencies } = await getDependencies({ cwd })\n  if (!(dependencies[name] || devDependencies[name])) {\n    return null\n  }\n\n  const cwd2 =\n    cwd.endsWith(path.posix.sep) || cwd.endsWith(path.win32.sep)\n      ? cwd\n      : `${cwd}/`\n\n  try {\n    const targetPath = require.resolve(`${name}/package.json`, {\n      paths: [cwd2],\n    })\n    const targetContent = await fs.readFile(targetPath, 'utf-8')\n    return JSON5.parse(targetContent).version ?? null\n  } catch {\n    return null\n  }\n}\n"], "names": ["getDependencies", "getPackageVersion", "cachedDeps", "cwd", "configurationPath", "findUp", "dependencies", "devDependencies", "content", "fs", "readFile", "packageJson", "JSON5", "parse", "name", "cwd2", "endsWith", "path", "posix", "sep", "win32", "targetPath", "require", "resolve", "paths", "targetContent", "version"], "mappings": ";;;;;;;;;;;;;;;IAYgBA,eAAe;eAAfA;;IAyBMC,iBAAiB;eAAjBA;;;oBArCS;+DACZ;8DACD;8DACI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB,IAAIC;AAEG,SAASF,gBAAgB,EAC9BG,GAAG,EAGJ;IACC,IAAID,YAAY;QACd,OAAOA;IACT;IAEA,OAAQA,aAAa,AAAC,CAAA;QACpB,MAAME,oBAAwC,MAAMC,IAAAA,eAAM,EAAC,gBAAgB;YACzEF;QACF;QACA,IAAI,CAACC,mBAAmB;YACtB,OAAO;gBAAEE,cAAc,CAAC;gBAAGC,iBAAiB,CAAC;YAAE;QACjD;QAEA,MAAMC,UAAU,MAAMC,YAAE,CAACC,QAAQ,CAACN,mBAAmB;QACrD,MAAMO,cAAmBC,cAAK,CAACC,KAAK,CAACL;QAErC,MAAM,EAAEF,eAAe,CAAC,CAAC,EAAEC,kBAAkB,CAAC,CAAC,EAAE,GAAGI,eAAe,CAAC;QACpE,OAAO;YAAEL;YAAcC;QAAgB;IACzC,CAAA;AACF;AAEO,eAAeN,kBAAkB,EACtCE,GAAG,EACHW,IAAI,EAIL;IACC,MAAM,EAAER,YAAY,EAAEC,eAAe,EAAE,GAAG,MAAMP,gBAAgB;QAAEG;IAAI;IACtE,IAAI,CAAEG,CAAAA,YAAY,CAACQ,KAAK,IAAIP,eAAe,CAACO,KAAK,AAAD,GAAI;QAClD,OAAO;IACT;IAEA,MAAMC,OACJZ,IAAIa,QAAQ,CAACC,MAAKC,KAAK,CAACC,GAAG,KAAKhB,IAAIa,QAAQ,CAACC,MAAKG,KAAK,CAACD,GAAG,IACvDhB,MACA,GAAGA,IAAI,CAAC,CAAC;IAEf,IAAI;QACF,MAAMkB,aAAaC,QAAQC,OAAO,CAAC,GAAGT,KAAK,aAAa,CAAC,EAAE;YACzDU,OAAO;gBAACT;aAAK;QACf;QACA,MAAMU,gBAAgB,MAAMhB,YAAE,CAACC,QAAQ,CAACW,YAAY;QACpD,OAAOT,cAAK,CAACC,KAAK,CAACY,eAAeC,OAAO,IAAI;IAC/C,EAAE,OAAM;QACN,OAAO;IACT;AACF"}