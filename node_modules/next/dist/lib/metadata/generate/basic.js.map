{"version": 3, "sources": ["../../../../src/lib/metadata/generate/basic.tsx"], "sourcesContent": ["import type {\n  ResolvedMetadata,\n  ResolvedViewport,\n  Viewport,\n} from '../types/metadata-interface'\nimport type { ViewportLayout } from '../types/extra-types'\n\nimport { Meta, MetaFilter, MultiMeta } from './meta'\nimport { ViewportMetaKeys } from '../constants'\nimport { getOrigin } from './utils'\n\n// convert viewport object to string for viewport meta tag\nfunction resolveViewportLayout(viewport: Viewport) {\n  let resolved: string | null = null\n\n  if (viewport && typeof viewport === 'object') {\n    resolved = ''\n    for (const viewportKey_ in ViewportMetaKeys) {\n      const viewportKey = viewportKey_ as keyof ViewportLayout\n      if (viewportKey in viewport) {\n        let value = viewport[viewportKey]\n        if (typeof value === 'boolean') {\n          value = value ? 'yes' : 'no'\n        } else if (!value && viewportKey === 'initialScale') {\n          value = undefined\n        }\n        if (value) {\n          if (resolved) resolved += ', '\n          resolved += `${ViewportMetaKeys[viewportKey]}=${value}`\n        }\n      }\n    }\n  }\n  return resolved\n}\n\nexport function ViewportMeta({ viewport }: { viewport: ResolvedViewport }) {\n  return MetaFilter([\n    <meta charSet=\"utf-8\" />,\n    Meta({ name: 'viewport', content: resolveViewportLayout(viewport) }),\n    ...(viewport.themeColor\n      ? viewport.themeColor.map((themeColor) =>\n          Meta({\n            name: 'theme-color',\n            content: themeColor.color,\n            media: themeColor.media,\n          })\n        )\n      : []),\n    Meta({ name: 'color-scheme', content: viewport.colorScheme }),\n  ])\n}\n\nexport function BasicMeta({ metadata }: { metadata: ResolvedMetadata }) {\n  const manifestOrigin = metadata.manifest\n    ? getOrigin(metadata.manifest)\n    : undefined\n\n  return MetaFilter([\n    metadata.title !== null && metadata.title.absolute ? (\n      <title>{metadata.title.absolute}</title>\n    ) : null,\n    Meta({ name: 'description', content: metadata.description }),\n    Meta({ name: 'application-name', content: metadata.applicationName }),\n    ...(metadata.authors\n      ? metadata.authors.map((author) => [\n          author.url ? (\n            <link rel=\"author\" href={author.url.toString()} />\n          ) : null,\n          Meta({ name: 'author', content: author.name }),\n        ])\n      : []),\n    metadata.manifest ? (\n      <link\n        rel=\"manifest\"\n        href={metadata.manifest.toString()}\n        // If it's same origin, and it's a preview deployment,\n        // including credentials for manifest request.\n        crossOrigin={\n          !manifestOrigin && process.env.VERCEL_ENV === 'preview'\n            ? 'use-credentials'\n            : undefined\n        }\n      />\n    ) : null,\n    Meta({ name: 'generator', content: metadata.generator }),\n    Meta({ name: 'keywords', content: metadata.keywords?.join(',') }),\n    Meta({ name: 'referrer', content: metadata.referrer }),\n    Meta({ name: 'creator', content: metadata.creator }),\n    Meta({ name: 'publisher', content: metadata.publisher }),\n    Meta({ name: 'robots', content: metadata.robots?.basic }),\n    Meta({ name: 'googlebot', content: metadata.robots?.googleBot }),\n    Meta({ name: 'abstract', content: metadata.abstract }),\n    ...(metadata.archives\n      ? metadata.archives.map((archive) => (\n          <link rel=\"archives\" href={archive} />\n        ))\n      : []),\n    ...(metadata.assets\n      ? metadata.assets.map((asset) => <link rel=\"assets\" href={asset} />)\n      : []),\n    ...(metadata.bookmarks\n      ? metadata.bookmarks.map((bookmark) => (\n          <link rel=\"bookmarks\" href={bookmark} />\n        ))\n      : []),\n    ...(metadata.pagination\n      ? [\n          metadata.pagination.previous ? (\n            <link rel=\"prev\" href={metadata.pagination.previous} />\n          ) : null,\n          metadata.pagination.next ? (\n            <link rel=\"next\" href={metadata.pagination.next} />\n          ) : null,\n        ]\n      : []),\n    Meta({ name: 'category', content: metadata.category }),\n    Meta({ name: 'classification', content: metadata.classification }),\n    ...(metadata.other\n      ? Object.entries(metadata.other).map(([name, content]) => {\n          if (Array.isArray(content)) {\n            return content.map((contentItem) =>\n              Meta({ name, content: contentItem })\n            )\n          } else {\n            return Meta({ name, content })\n          }\n        })\n      : []),\n  ])\n}\n\nexport function ItunesMeta({ itunes }: { itunes: ResolvedMetadata['itunes'] }) {\n  if (!itunes) return null\n  const { appId, appArgument } = itunes\n  let content = `app-id=${appId}`\n  if (appArgument) {\n    content += `, app-argument=${appArgument}`\n  }\n  return <meta name=\"apple-itunes-app\" content={content} />\n}\n\nexport function FacebookMeta({\n  facebook,\n}: {\n  facebook: ResolvedMetadata['facebook']\n}) {\n  if (!facebook) return null\n\n  const { appId, admins } = facebook\n\n  return MetaFilter([\n    appId ? <meta property=\"fb:app_id\" content={appId} /> : null,\n    ...(admins\n      ? admins.map((admin) => <meta property=\"fb:admins\" content={admin} />)\n      : []),\n  ])\n}\n\nexport function PinterestMeta({\n  pinterest,\n}: {\n  pinterest: ResolvedMetadata['pinterest']\n}) {\n  if (!pinterest || !pinterest.richPin) return null\n\n  const { richPin } = pinterest\n\n  return <meta property=\"pinterest-rich-pin\" content={richPin.toString()} />\n}\n\nconst formatDetectionKeys = [\n  'telephone',\n  'date',\n  'address',\n  'email',\n  'url',\n] as const\nexport function FormatDetectionMeta({\n  formatDetection,\n}: {\n  formatDetection: ResolvedMetadata['formatDetection']\n}) {\n  if (!formatDetection) return null\n  let content = ''\n  for (const key of formatDetectionKeys) {\n    if (key in formatDetection) {\n      if (content) content += ', '\n      content += `${key}=no`\n    }\n  }\n  return <meta name=\"format-detection\" content={content} />\n}\n\nexport function AppleWebAppMeta({\n  appleWebApp,\n}: {\n  appleWebApp: ResolvedMetadata['appleWebApp']\n}) {\n  if (!appleWebApp) return null\n\n  const { capable, title, startupImage, statusBarStyle } = appleWebApp\n\n  return MetaFilter([\n    capable ? Meta({ name: 'mobile-web-app-capable', content: 'yes' }) : null,\n    Meta({ name: 'apple-mobile-web-app-title', content: title }),\n    startupImage\n      ? startupImage.map((image) => (\n          <link\n            href={image.url}\n            media={image.media}\n            rel=\"apple-touch-startup-image\"\n          />\n        ))\n      : null,\n    statusBarStyle\n      ? Meta({\n          name: 'apple-mobile-web-app-status-bar-style',\n          content: statusBarStyle,\n        })\n      : null,\n  ])\n}\n\nexport function VerificationMeta({\n  verification,\n}: {\n  verification: ResolvedMetadata['verification']\n}) {\n  if (!verification) return null\n\n  return MetaFilter([\n    MultiMeta({\n      namePrefix: 'google-site-verification',\n      contents: verification.google,\n    }),\n    MultiMeta({ namePrefix: 'y_key', contents: verification.yahoo }),\n    MultiMeta({\n      namePrefix: 'yandex-verification',\n      contents: verification.yandex,\n    }),\n    MultiMeta({ namePrefix: 'me', contents: verification.me }),\n    ...(verification.other\n      ? Object.entries(verification.other).map(([key, value]) =>\n          MultiMeta({ namePrefix: key, contents: value })\n        )\n      : []),\n  ])\n}\n"], "names": ["AppleWebAppMeta", "BasicMeta", "FacebookMeta", "FormatDetectionMeta", "ItunesMeta", "PinterestMeta", "VerificationMeta", "ViewportMeta", "resolveViewportLayout", "viewport", "resolved", "viewportKey_", "ViewportMetaKeys", "viewportKey", "value", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "meta", "charSet", "Meta", "name", "content", "themeColor", "map", "color", "media", "colorScheme", "metadata", "<PERSON><PERSON><PERSON><PERSON>", "manifest", "<PERSON><PERSON><PERSON><PERSON>", "title", "absolute", "description", "applicationName", "authors", "author", "url", "link", "rel", "href", "toString", "crossOrigin", "process", "env", "VERCEL_ENV", "generator", "keywords", "join", "referrer", "creator", "publisher", "robots", "basic", "googleBot", "abstract", "archives", "archive", "assets", "asset", "bookmarks", "bookmark", "pagination", "previous", "next", "category", "classification", "other", "Object", "entries", "Array", "isArray", "contentItem", "itunes", "appId", "appArgument", "facebook", "admins", "property", "admin", "pinterest", "rich<PERSON>in", "formatDetectionKeys", "formatDetection", "key", "appleWebApp", "capable", "startupImage", "statusBarStyle", "image", "verification", "MultiMeta", "namePrefix", "contents", "google", "yahoo", "yandex", "me"], "mappings": ";;;;;;;;;;;;;;;;;;;;;IAkMgBA,eAAe;eAAfA;;IA7IAC,SAAS;eAATA;;IAyFAC,YAAY;eAAZA;;IAoCAC,mBAAmB;eAAnBA;;IA9CAC,UAAU;eAAVA;;IA2BAC,aAAa;eAAbA;;IAiEAC,gBAAgB;eAAhBA;;IA5LAC,YAAY;eAAZA;;;;sBA7B4B;2BACX;uBACP;AAE1B,0DAA0D;AAC1D,SAASC,sBAAsBC,QAAkB;IAC/C,IAAIC,WAA0B;IAE9B,IAAID,YAAY,OAAOA,aAAa,UAAU;QAC5CC,WAAW;QACX,IAAK,MAAMC,gBAAgBC,2BAAgB,CAAE;YAC3C,MAAMC,cAAcF;YACpB,IAAIE,eAAeJ,UAAU;gBAC3B,IAAIK,QAAQL,QAAQ,CAACI,YAAY;gBACjC,IAAI,OAAOC,UAAU,WAAW;oBAC9BA,QAAQA,QAAQ,QAAQ;gBAC1B,OAAO,IAAI,CAACA,SAASD,gBAAgB,gBAAgB;oBACnDC,QAAQC;gBACV;gBACA,IAAID,OAAO;oBACT,IAAIJ,UAAUA,YAAY;oBAC1BA,YAAY,GAAGE,2BAAgB,CAACC,YAAY,CAAC,CAAC,EAAEC,OAAO;gBACzD;YACF;QACF;IACF;IACA,OAAOJ;AACT;AAEO,SAASH,aAAa,EAAEE,QAAQ,EAAkC;IACvE,OAAOO,IAAAA,gBAAU,EAAC;sBAChB,qBAACC;YAAKC,SAAQ;;QACdC,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAYC,SAASb,sBAAsBC;QAAU;WAC9DA,SAASa,UAAU,GACnBb,SAASa,UAAU,CAACC,GAAG,CAAC,CAACD,aACvBH,IAAAA,UAAI,EAAC;gBACHC,MAAM;gBACNC,SAASC,WAAWE,KAAK;gBACzBC,OAAOH,WAAWG,KAAK;YACzB,MAEF,EAAE;QACNN,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAgBC,SAASZ,SAASiB,WAAW;QAAC;KAC5D;AACH;AAEO,SAASzB,UAAU,EAAE0B,QAAQ,EAAkC;QAiChCA,oBAIFA,kBACGA;IArCrC,MAAMC,iBAAiBD,SAASE,QAAQ,GACpCC,IAAAA,gBAAS,EAACH,SAASE,QAAQ,IAC3Bd;IAEJ,OAAOC,IAAAA,gBAAU,EAAC;QAChBW,SAASI,KAAK,KAAK,QAAQJ,SAASI,KAAK,CAACC,QAAQ,iBAChD,qBAACD;sBAAOJ,SAASI,KAAK,CAACC,QAAQ;aAC7B;QACJb,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAeC,SAASM,SAASM,WAAW;QAAC;QAC1Dd,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAoBC,SAASM,SAASO,eAAe;QAAC;WAC/DP,SAASQ,OAAO,GAChBR,SAASQ,OAAO,CAACZ,GAAG,CAAC,CAACa,SAAW;gBAC/BA,OAAOC,GAAG,iBACR,qBAACC;oBAAKC,KAAI;oBAASC,MAAMJ,OAAOC,GAAG,CAACI,QAAQ;qBAC1C;gBACJtB,IAAAA,UAAI,EAAC;oBAAEC,MAAM;oBAAUC,SAASe,OAAOhB,IAAI;gBAAC;aAC7C,IACD,EAAE;QACNO,SAASE,QAAQ,iBACf,qBAACS;YACCC,KAAI;YACJC,MAAMb,SAASE,QAAQ,CAACY,QAAQ;YAChC,sDAAsD;YACtD,8CAA8C;YAC9CC,aACE,CAACd,kBAAkBe,QAAQC,GAAG,CAACC,UAAU,KAAK,YAC1C,oBACA9B;aAGN;QACJI,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAaC,SAASM,SAASmB,SAAS;QAAC;QACtD3B,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAYC,OAAO,GAAEM,qBAAAA,SAASoB,QAAQ,qBAAjBpB,mBAAmBqB,IAAI,CAAC;QAAK;QAC/D7B,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAYC,SAASM,SAASsB,QAAQ;QAAC;QACpD9B,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAWC,SAASM,SAASuB,OAAO;QAAC;QAClD/B,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAaC,SAASM,SAASwB,SAAS;QAAC;QACtDhC,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAUC,OAAO,GAAEM,mBAAAA,SAASyB,MAAM,qBAAfzB,iBAAiB0B,KAAK;QAAC;QACvDlC,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAaC,OAAO,GAAEM,oBAAAA,SAASyB,MAAM,qBAAfzB,kBAAiB2B,SAAS;QAAC;QAC9DnC,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAYC,SAASM,SAAS4B,QAAQ;QAAC;WAChD5B,SAAS6B,QAAQ,GACjB7B,SAAS6B,QAAQ,CAACjC,GAAG,CAAC,CAACkC,wBACrB,qBAACnB;gBAAKC,KAAI;gBAAWC,MAAMiB;kBAE7B,EAAE;WACF9B,SAAS+B,MAAM,GACf/B,SAAS+B,MAAM,CAACnC,GAAG,CAAC,CAACoC,sBAAU,qBAACrB;gBAAKC,KAAI;gBAASC,MAAMmB;kBACxD,EAAE;WACFhC,SAASiC,SAAS,GAClBjC,SAASiC,SAAS,CAACrC,GAAG,CAAC,CAACsC,yBACtB,qBAACvB;gBAAKC,KAAI;gBAAYC,MAAMqB;kBAE9B,EAAE;WACFlC,SAASmC,UAAU,GACnB;YACEnC,SAASmC,UAAU,CAACC,QAAQ,iBAC1B,qBAACzB;gBAAKC,KAAI;gBAAOC,MAAMb,SAASmC,UAAU,CAACC,QAAQ;iBACjD;YACJpC,SAASmC,UAAU,CAACE,IAAI,iBACtB,qBAAC1B;gBAAKC,KAAI;gBAAOC,MAAMb,SAASmC,UAAU,CAACE,IAAI;iBAC7C;SACL,GACD,EAAE;QACN7C,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAYC,SAASM,SAASsC,QAAQ;QAAC;QACpD9C,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAAkBC,SAASM,SAASuC,cAAc;QAAC;WAC5DvC,SAASwC,KAAK,GACdC,OAAOC,OAAO,CAAC1C,SAASwC,KAAK,EAAE5C,GAAG,CAAC,CAAC,CAACH,MAAMC,QAAQ;YACjD,IAAIiD,MAAMC,OAAO,CAAClD,UAAU;gBAC1B,OAAOA,QAAQE,GAAG,CAAC,CAACiD,cAClBrD,IAAAA,UAAI,EAAC;wBAAEC;wBAAMC,SAASmD;oBAAY;YAEtC,OAAO;gBACL,OAAOrD,IAAAA,UAAI,EAAC;oBAAEC;oBAAMC;gBAAQ;YAC9B;QACF,KACA,EAAE;KACP;AACH;AAEO,SAASjB,WAAW,EAAEqE,MAAM,EAA0C;IAC3E,IAAI,CAACA,QAAQ,OAAO;IACpB,MAAM,EAAEC,KAAK,EAAEC,WAAW,EAAE,GAAGF;IAC/B,IAAIpD,UAAU,CAAC,OAAO,EAAEqD,OAAO;IAC/B,IAAIC,aAAa;QACftD,WAAW,CAAC,eAAe,EAAEsD,aAAa;IAC5C;IACA,qBAAO,qBAAC1D;QAAKG,MAAK;QAAmBC,SAASA;;AAChD;AAEO,SAASnB,aAAa,EAC3B0E,QAAQ,EAGT;IACC,IAAI,CAACA,UAAU,OAAO;IAEtB,MAAM,EAAEF,KAAK,EAAEG,MAAM,EAAE,GAAGD;IAE1B,OAAO5D,IAAAA,gBAAU,EAAC;QAChB0D,sBAAQ,qBAACzD;YAAK6D,UAAS;YAAYzD,SAASqD;aAAY;WACpDG,SACAA,OAAOtD,GAAG,CAAC,CAACwD,sBAAU,qBAAC9D;gBAAK6D,UAAS;gBAAYzD,SAAS0D;kBAC1D,EAAE;KACP;AACH;AAEO,SAAS1E,cAAc,EAC5B2E,SAAS,EAGV;IACC,IAAI,CAACA,aAAa,CAACA,UAAUC,OAAO,EAAE,OAAO;IAE7C,MAAM,EAAEA,OAAO,EAAE,GAAGD;IAEpB,qBAAO,qBAAC/D;QAAK6D,UAAS;QAAqBzD,SAAS4D,QAAQxC,QAAQ;;AACtE;AAEA,MAAMyC,sBAAsB;IAC1B;IACA;IACA;IACA;IACA;CACD;AACM,SAAS/E,oBAAoB,EAClCgF,eAAe,EAGhB;IACC,IAAI,CAACA,iBAAiB,OAAO;IAC7B,IAAI9D,UAAU;IACd,KAAK,MAAM+D,OAAOF,oBAAqB;QACrC,IAAIE,OAAOD,iBAAiB;YAC1B,IAAI9D,SAASA,WAAW;YACxBA,WAAW,GAAG+D,IAAI,GAAG,CAAC;QACxB;IACF;IACA,qBAAO,qBAACnE;QAAKG,MAAK;QAAmBC,SAASA;;AAChD;AAEO,SAASrB,gBAAgB,EAC9BqF,WAAW,EAGZ;IACC,IAAI,CAACA,aAAa,OAAO;IAEzB,MAAM,EAAEC,OAAO,EAAEvD,KAAK,EAAEwD,YAAY,EAAEC,cAAc,EAAE,GAAGH;IAEzD,OAAOrE,IAAAA,gBAAU,EAAC;QAChBsE,UAAUnE,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAA0BC,SAAS;QAAM,KAAK;QACrEF,IAAAA,UAAI,EAAC;YAAEC,MAAM;YAA8BC,SAASU;QAAM;QAC1DwD,eACIA,aAAahE,GAAG,CAAC,CAACkE,sBAChB,qBAACnD;gBACCE,MAAMiD,MAAMpD,GAAG;gBACfZ,OAAOgE,MAAMhE,KAAK;gBAClBc,KAAI;kBAGR;QACJiD,iBACIrE,IAAAA,UAAI,EAAC;YACHC,MAAM;YACNC,SAASmE;QACX,KACA;KACL;AACH;AAEO,SAASlF,iBAAiB,EAC/BoF,YAAY,EAGb;IACC,IAAI,CAACA,cAAc,OAAO;IAE1B,OAAO1E,IAAAA,gBAAU,EAAC;QAChB2E,IAAAA,eAAS,EAAC;YACRC,YAAY;YACZC,UAAUH,aAAaI,MAAM;QAC/B;QACAH,IAAAA,eAAS,EAAC;YAAEC,YAAY;YAASC,UAAUH,aAAaK,KAAK;QAAC;QAC9DJ,IAAAA,eAAS,EAAC;YACRC,YAAY;YACZC,UAAUH,aAAaM,MAAM;QAC/B;QACAL,IAAAA,eAAS,EAAC;YAAEC,YAAY;YAAMC,UAAUH,aAAaO,EAAE;QAAC;WACpDP,aAAavB,KAAK,GAClBC,OAAOC,OAAO,CAACqB,aAAavB,KAAK,EAAE5C,GAAG,CAAC,CAAC,CAAC6D,KAAKtE,MAAM,GAClD6E,IAAAA,eAAS,EAAC;gBAAEC,YAAYR;gBAAKS,UAAU/E;YAAM,MAE/C,EAAE;KACP;AACH"}