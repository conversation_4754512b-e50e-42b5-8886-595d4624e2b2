{"version": 3, "sources": ["../../../src/telemetry/events/error-feedback.ts"], "sourcesContent": ["export const eventNameErrorFeedback = 'NEXT_ERROR_FEEDBACK'\n\nexport type EventErrorFeedback = {\n  errorCode: string\n  wasHelpful: boolean\n}\n\n/**\n * Records telemetry for error feedback.\n *\n * @example\n * ```ts\n * telemetry.record(eventErrorFeedback({\n *   errorCode: 'E1',\n *   wasHelpful: true\n * }))\n * ```\n */\nexport function eventErrorFeedback(event: EventErrorFeedback): {\n  eventName: string\n  payload: EventErrorFeedback\n} {\n  return {\n    eventName: eventNameErrorFeedback,\n    payload: event,\n  }\n}\n"], "names": ["eventErrorFeedback", "eventNameErrorFeedback", "event", "eventName", "payload"], "mappings": ";;;;;;;;;;;;;;;IAkBgBA,kBAAkB;eAAlBA;;IAlBHC,sBAAsB;eAAtBA;;;AAAN,MAAMA,yBAAyB;AAkB/B,SAASD,mBAAmBE,KAAyB;IAI1D,OAAO;QACLC,WAAWF;QACXG,SAASF;IACX;AACF"}