{"version": 3, "sources": ["../../../src/telemetry/events/version.ts"], "sourcesContent": ["import type { NextConfigComplete } from '../../server/config-shared'\nimport path from 'path'\n\nconst EVENT_VERSION = 'NEXT_CLI_SESSION_STARTED'\n\ntype EventCliSessionStarted = {\n  nextVersion: string\n  nodeVersion: string\n  cliCommand: string\n  isSrcDir: boolean | null\n  hasNowJson: boolean\n  isCustomServer: boolean | null\n  hasNextConfig: boolean\n  buildTarget: string\n  hasWebpackConfig: boolean\n  hasBabelConfig: boolean\n  basePathEnabled: boolean\n  i18nEnabled: boolean\n  imageEnabled: boolean\n  imageFutureEnabled: boolean\n  locales: string | null\n  localeDomainsCount: number | null\n  localeDetectionEnabled: boolean | null\n  imageDomainsCount: number | null\n  imageRemotePatternsCount: number | null\n  imageLocalPatternsCount: number | null\n  imageQualities: string | null\n  imageSizes: string | null\n  imageLoader: string | null\n  imageFormats: string | null\n  nextConfigOutput: string | null\n  trailingSlashEnabled: boolean\n  reactStrictMode: boolean\n  webpackVersion: number | null\n  turboFlag: boolean\n  isRspack: boolean\n  appDir: boolean | null\n  pagesDir: boolean | null\n  staticStaleTime: number | null\n  dynamicStaleTime: number | null\n  reactCompiler: boolean\n  reactCompilerCompilationMode: string | null\n  reactCompilerPanicThreshold: string | null\n}\n\nfunction hasBabelConfig(dir: string): boolean {\n  try {\n    const noopFile = path.join(dir, 'noop.js')\n    const res = require('next/dist/compiled/babel/core').loadPartialConfig({\n      cwd: dir,\n      filename: noopFile,\n      sourceFileName: noopFile,\n    }) as any\n    const isForTooling =\n      res.options?.presets?.every(\n        (e: any) => e?.file?.request === 'next/babel'\n      ) && res.options?.plugins?.length === 0\n    return res.hasFilesystemConfig() && !isForTooling\n  } catch {\n    return false\n  }\n}\n\nexport function eventCliSession(\n  dir: string,\n  nextConfig: NextConfigComplete,\n  event: Omit<\n    EventCliSessionStarted,\n    | 'nextVersion'\n    | 'nodeVersion'\n    | 'hasNextConfig'\n    | 'buildTarget'\n    | 'hasWebpackConfig'\n    | 'hasBabelConfig'\n    | 'basePathEnabled'\n    | 'i18nEnabled'\n    | 'imageEnabled'\n    | 'imageFutureEnabled'\n    | 'locales'\n    | 'localeDomainsCount'\n    | 'localeDetectionEnabled'\n    | 'imageDomainsCount'\n    | 'imageRemotePatternsCount'\n    | 'imageLocalPatternsCount'\n    | 'imageQualities'\n    | 'imageSizes'\n    | 'imageLoader'\n    | 'imageFormats'\n    | 'nextConfigOutput'\n    | 'trailingSlashEnabled'\n    | 'reactStrictMode'\n    | 'staticStaleTime'\n    | 'dynamicStaleTime'\n    | 'reactCompiler'\n    | 'reactCompilerCompilationMode'\n    | 'reactCompilerPanicThreshold'\n    | 'isRspack'\n  >\n): { eventName: string; payload: EventCliSessionStarted }[] {\n  // This should be an invariant, if it fails our build tooling is broken.\n  if (typeof process.env.__NEXT_VERSION !== 'string') {\n    return []\n  }\n\n  const { images, i18n } = nextConfig || {}\n\n  const payload: EventCliSessionStarted = {\n    nextVersion: process.env.__NEXT_VERSION,\n    nodeVersion: process.version,\n    cliCommand: event.cliCommand,\n    isSrcDir: event.isSrcDir,\n    hasNowJson: event.hasNowJson,\n    isCustomServer: event.isCustomServer,\n    hasNextConfig: nextConfig.configOrigin !== 'default',\n    buildTarget: 'default',\n    hasWebpackConfig: typeof nextConfig?.webpack === 'function',\n    hasBabelConfig: hasBabelConfig(dir),\n    imageEnabled: !!images,\n    imageFutureEnabled: !!images,\n    basePathEnabled: !!nextConfig?.basePath,\n    i18nEnabled: !!i18n,\n    locales: i18n?.locales ? i18n.locales.join(',') : null,\n    localeDomainsCount: i18n?.domains ? i18n.domains.length : null,\n    localeDetectionEnabled: !i18n ? null : i18n.localeDetection !== false,\n    imageDomainsCount: images?.domains ? images.domains.length : null,\n    imageRemotePatternsCount: images?.remotePatterns\n      ? images.remotePatterns.length\n      : null,\n    imageLocalPatternsCount: images?.localPatterns\n      ? images.localPatterns.length\n      : null,\n    imageSizes: images?.imageSizes ? images.imageSizes.join(',') : null,\n    imageQualities: images?.qualities ? images.qualities.join(',') : null,\n    imageLoader: images?.loader,\n    imageFormats: images?.formats ? images.formats.join(',') : null,\n    nextConfigOutput: nextConfig?.output || null,\n    trailingSlashEnabled: !!nextConfig?.trailingSlash,\n    reactStrictMode: !!nextConfig?.reactStrictMode,\n    webpackVersion: event.webpackVersion || null,\n    turboFlag: event.turboFlag || false,\n    isRspack: process.env.NEXT_RSPACK !== undefined,\n    appDir: event.appDir,\n    pagesDir: event.pagesDir,\n    staticStaleTime: nextConfig.experimental.staleTimes?.static ?? null,\n    dynamicStaleTime: nextConfig.experimental.staleTimes?.dynamic ?? null,\n    reactCompiler: Boolean(nextConfig.experimental.reactCompiler),\n    reactCompilerCompilationMode:\n      typeof nextConfig.experimental.reactCompiler !== 'boolean'\n        ? nextConfig.experimental.reactCompiler?.compilationMode ?? null\n        : null,\n    reactCompilerPanicThreshold:\n      typeof nextConfig.experimental.reactCompiler !== 'boolean'\n        ? nextConfig.experimental.reactCompiler?.panicThreshold ?? null\n        : null,\n  }\n  return [{ eventName: EVENT_VERSION, payload }]\n}\n"], "names": ["eventCliSession", "EVENT_VERSION", "hasBabelConfig", "dir", "res", "noopFile", "path", "join", "require", "loadPartialConfig", "cwd", "filename", "sourceFileName", "isForTooling", "options", "presets", "every", "e", "file", "request", "plugins", "length", "hasFilesystemConfig", "nextConfig", "event", "process", "env", "__NEXT_VERSION", "images", "i18n", "payload", "nextVersion", "nodeVersion", "version", "cliCommand", "isSrcDir", "has<PERSON>ow<PERSON><PERSON>", "isCustomServer", "hasNextConfig", "config<PERSON><PERSON><PERSON>", "buildTarget", "hasWebpackConfig", "webpack", "imageEnabled", "imageFutureEnabled", "basePathEnabled", "basePath", "i18nEnabled", "locales", "localeDomainsCount", "domains", "localeDetectionEnabled", "localeDetection", "imageDomainsCount", "imageRemotePatternsCount", "remotePatterns", "imageLocalPatternsCount", "localPatterns", "imageSizes", "imageQualities", "qualities", "imageLoader", "loader", "imageFormats", "formats", "nextConfigOutput", "output", "trailingSlashEnabled", "trailingSlash", "reactStrictMode", "webpackVersion", "turboFlag", "isRspack", "NEXT_RSPACK", "undefined", "appDir", "pagesDir", "staticStaleTime", "experimental", "staleTimes", "static", "dynamicStaleTime", "dynamic", "reactCompiler", "Boolean", "reactCompilerCompilationMode", "compilationMode", "reactCompilerPanicThreshold", "panicT<PERSON>eshold", "eventName"], "mappings": ";;;;+BA+DgBA;;;eAAAA;;;6DA9DC;;;;;;AAEjB,MAAMC,gBAAgB;AA0CtB,SAASC,eAAeC,GAAW;IACjC,IAAI;YAQAC,sBAAAA,cAEKA,sBAAAA;QATP,MAAMC,WAAWC,aAAI,CAACC,IAAI,CAACJ,KAAK;QAChC,MAAMC,MAAMI,QAAQ,iCAAiCC,iBAAiB,CAAC;YACrEC,KAAKP;YACLQ,UAAUN;YACVO,gBAAgBP;QAClB;QACA,MAAMQ,eACJT,EAAAA,eAAAA,IAAIU,OAAO,sBAAXV,uBAAAA,aAAaW,OAAO,qBAApBX,qBAAsBY,KAAK,CACzB,CAACC;gBAAWA;mBAAAA,CAAAA,sBAAAA,UAAAA,EAAGC,IAAI,qBAAPD,QAASE,OAAO,MAAK;eAC9Bf,EAAAA,gBAAAA,IAAIU,OAAO,sBAAXV,uBAAAA,cAAagB,OAAO,qBAApBhB,qBAAsBiB,MAAM,MAAK;QACxC,OAAOjB,IAAIkB,mBAAmB,MAAM,CAACT;IACvC,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEO,SAASb,gBACdG,GAAW,EACXoB,UAA8B,EAC9BC,KA+BC;QA8CkBD,qCACCA,sCAIZA,wCAIAA;IArDR,wEAAwE;IACxE,IAAI,OAAOE,QAAQC,GAAG,CAACC,cAAc,KAAK,UAAU;QAClD,OAAO,EAAE;IACX;IAEA,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAE,GAAGN,cAAc,CAAC;IAExC,MAAMO,UAAkC;QACtCC,aAAaN,QAAQC,GAAG,CAACC,cAAc;QACvCK,aAAaP,QAAQQ,OAAO;QAC5BC,YAAYV,MAAMU,UAAU;QAC5BC,UAAUX,MAAMW,QAAQ;QACxBC,YAAYZ,MAAMY,UAAU;QAC5BC,gBAAgBb,MAAMa,cAAc;QACpCC,eAAef,WAAWgB,YAAY,KAAK;QAC3CC,aAAa;QACbC,kBAAkB,QAAOlB,8BAAAA,WAAYmB,OAAO,MAAK;QACjDxC,gBAAgBA,eAAeC;QAC/BwC,cAAc,CAAC,CAACf;QAChBgB,oBAAoB,CAAC,CAAChB;QACtBiB,iBAAiB,CAAC,EAACtB,8BAAAA,WAAYuB,QAAQ;QACvCC,aAAa,CAAC,CAAClB;QACfmB,SAASnB,CAAAA,wBAAAA,KAAMmB,OAAO,IAAGnB,KAAKmB,OAAO,CAACzC,IAAI,CAAC,OAAO;QAClD0C,oBAAoBpB,CAAAA,wBAAAA,KAAMqB,OAAO,IAAGrB,KAAKqB,OAAO,CAAC7B,MAAM,GAAG;QAC1D8B,wBAAwB,CAACtB,OAAO,OAAOA,KAAKuB,eAAe,KAAK;QAChEC,mBAAmBzB,CAAAA,0BAAAA,OAAQsB,OAAO,IAAGtB,OAAOsB,OAAO,CAAC7B,MAAM,GAAG;QAC7DiC,0BAA0B1B,CAAAA,0BAAAA,OAAQ2B,cAAc,IAC5C3B,OAAO2B,cAAc,CAAClC,MAAM,GAC5B;QACJmC,yBAAyB5B,CAAAA,0BAAAA,OAAQ6B,aAAa,IAC1C7B,OAAO6B,aAAa,CAACpC,MAAM,GAC3B;QACJqC,YAAY9B,CAAAA,0BAAAA,OAAQ8B,UAAU,IAAG9B,OAAO8B,UAAU,CAACnD,IAAI,CAAC,OAAO;QAC/DoD,gBAAgB/B,CAAAA,0BAAAA,OAAQgC,SAAS,IAAGhC,OAAOgC,SAAS,CAACrD,IAAI,CAAC,OAAO;QACjEsD,WAAW,EAAEjC,0BAAAA,OAAQkC,MAAM;QAC3BC,cAAcnC,CAAAA,0BAAAA,OAAQoC,OAAO,IAAGpC,OAAOoC,OAAO,CAACzD,IAAI,CAAC,OAAO;QAC3D0D,kBAAkB1C,CAAAA,8BAAAA,WAAY2C,MAAM,KAAI;QACxCC,sBAAsB,CAAC,EAAC5C,8BAAAA,WAAY6C,aAAa;QACjDC,iBAAiB,CAAC,EAAC9C,8BAAAA,WAAY8C,eAAe;QAC9CC,gBAAgB9C,MAAM8C,cAAc,IAAI;QACxCC,WAAW/C,MAAM+C,SAAS,IAAI;QAC9BC,UAAU/C,QAAQC,GAAG,CAAC+C,WAAW,KAAKC;QACtCC,QAAQnD,MAAMmD,MAAM;QACpBC,UAAUpD,MAAMoD,QAAQ;QACxBC,iBAAiBtD,EAAAA,sCAAAA,WAAWuD,YAAY,CAACC,UAAU,qBAAlCxD,oCAAoCyD,MAAM,KAAI;QAC/DC,kBAAkB1D,EAAAA,uCAAAA,WAAWuD,YAAY,CAACC,UAAU,qBAAlCxD,qCAAoC2D,OAAO,KAAI;QACjEC,eAAeC,QAAQ7D,WAAWuD,YAAY,CAACK,aAAa;QAC5DE,8BACE,OAAO9D,WAAWuD,YAAY,CAACK,aAAa,KAAK,YAC7C5D,EAAAA,yCAAAA,WAAWuD,YAAY,CAACK,aAAa,qBAArC5D,uCAAuC+D,eAAe,KAAI,OAC1D;QACNC,6BACE,OAAOhE,WAAWuD,YAAY,CAACK,aAAa,KAAK,YAC7C5D,EAAAA,0CAAAA,WAAWuD,YAAY,CAACK,aAAa,qBAArC5D,wCAAuCiE,cAAc,KAAI,OACzD;IACR;IACA,OAAO;QAAC;YAAEC,WAAWxF;YAAe6B;QAAQ;KAAE;AAChD"}