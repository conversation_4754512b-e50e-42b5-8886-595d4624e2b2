{"version": 3, "sources": ["../../../src/telemetry/events/swc-load-failure.ts"], "sourcesContent": ["import { traceGlobals } from '../../trace/shared'\nimport type { Telemetry } from '../storage'\n// @ts-ignore JSON\nimport { version as nextVersion, optionalDependencies } from 'next/package.json'\n\nconst EVENT_PLUGIN_PRESENT = 'NEXT_SWC_LOAD_FAILURE'\nexport type EventSwcLoadFailure = {\n  eventName: string\n  payload: {\n    platform: string\n    arch: string\n    nodeVersion: string\n    nextVersion: string\n    wasm?: 'enabled' | 'fallback' | 'failed'\n    glibcVersion?: string\n    installedSwcPackages?: string\n    nativeBindingsErrorCode?: string\n  }\n}\n\nexport async function eventSwcLoadFailure(\n  event?: EventSwcLoadFailure['payload']\n): Promise<void> {\n  const telemetry: Telemetry | undefined = traceGlobals.get('telemetry')\n  // can't continue if telemetry isn't set\n  if (!telemetry) return\n\n  let glibcVersion\n  let installedSwcPackages\n\n  try {\n    // @ts-ignore\n    glibcVersion = process.report?.getReport().header.glibcVersionRuntime\n  } catch {}\n\n  try {\n    const pkgNames = Object.keys(optionalDependencies || {}).filter((pkg) =>\n      pkg.startsWith('@next/swc')\n    )\n    const installedPkgs = []\n\n    for (const pkg of pkgNames) {\n      try {\n        const { version } = require(`${pkg}/package.json`)\n        installedPkgs.push(`${pkg}@${version}`)\n      } catch {}\n    }\n\n    if (installedPkgs.length > 0) {\n      installedSwcPackages = installedPkgs.sort().join(',')\n    }\n  } catch {}\n\n  telemetry.record({\n    eventName: EVENT_PLUGIN_PRESENT,\n    payload: {\n      nextVersion,\n      glibcVersion,\n      installedSwcPackages,\n      arch: process.arch,\n      platform: process.platform,\n      nodeVersion: process.versions.node,\n      wasm: event?.wasm,\n      nativeBindingsErrorCode: event?.nativeBindingsErrorCode,\n    },\n  })\n  // ensure this event is flushed before process exits\n  await telemetry.flush()\n}\n"], "names": ["eventSwcLoadFailure", "EVENT_PLUGIN_PRESENT", "event", "telemetry", "traceGlobals", "get", "glibcVersion", "installedSwcPackages", "process", "report", "getReport", "header", "glibcVersionRuntime", "pkgNames", "Object", "keys", "optionalDependencies", "filter", "pkg", "startsWith", "installedPkgs", "version", "require", "push", "length", "sort", "join", "record", "eventName", "payload", "nextVersion", "arch", "platform", "nodeVersion", "versions", "node", "wasm", "nativeBindingsErrorCode", "flush"], "mappings": ";;;;+BAoBsBA;;;eAAAA;;;wBApBO;6BAGgC;AAE7D,MAAMC,uBAAuB;AAetB,eAAeD,oBACpBE,KAAsC;IAEtC,MAAMC,YAAmCC,oBAAY,CAACC,GAAG,CAAC;IAC1D,wCAAwC;IACxC,IAAI,CAACF,WAAW;IAEhB,IAAIG;IACJ,IAAIC;IAEJ,IAAI;YAEaC;QADf,aAAa;QACbF,gBAAeE,kBAAAA,QAAQC,MAAM,qBAAdD,gBAAgBE,SAAS,GAAGC,MAAM,CAACC,mBAAmB;IACvE,EAAE,OAAM,CAAC;IAET,IAAI;QACF,MAAMC,WAAWC,OAAOC,IAAI,CAACC,iCAAoB,IAAI,CAAC,GAAGC,MAAM,CAAC,CAACC,MAC/DA,IAAIC,UAAU,CAAC;QAEjB,MAAMC,gBAAgB,EAAE;QAExB,KAAK,MAAMF,OAAOL,SAAU;YAC1B,IAAI;gBACF,MAAM,EAAEQ,OAAO,EAAE,GAAGC,QAAQ,GAAGJ,IAAI,aAAa,CAAC;gBACjDE,cAAcG,IAAI,CAAC,GAAGL,IAAI,CAAC,EAAEG,SAAS;YACxC,EAAE,OAAM,CAAC;QACX;QAEA,IAAID,cAAcI,MAAM,GAAG,GAAG;YAC5BjB,uBAAuBa,cAAcK,IAAI,GAAGC,IAAI,CAAC;QACnD;IACF,EAAE,OAAM,CAAC;IAETvB,UAAUwB,MAAM,CAAC;QACfC,WAAW3B;QACX4B,SAAS;YACPC,aAAAA,oBAAW;YACXxB;YACAC;YACAwB,MAAMvB,QAAQuB,IAAI;YAClBC,UAAUxB,QAAQwB,QAAQ;YAC1BC,aAAazB,QAAQ0B,QAAQ,CAACC,IAAI;YAClCC,IAAI,EAAElC,yBAAAA,MAAOkC,IAAI;YACjBC,uBAAuB,EAAEnC,yBAAAA,MAAOmC,uBAAuB;QACzD;IACF;IACA,oDAAoD;IACpD,MAAMlC,UAAUmC,KAAK;AACvB"}