{"version": 3, "sources": ["../../../../src/experimental/testmode/playwright/next-fixture.ts"], "sourcesContent": ["import type { Page, TestInfo } from '@playwright/test'\nimport type { NextWorkerFixture, FetchHandler } from './next-worker-fixture'\nimport type { NextOptions } from './next-options'\nimport type { FetchHandlerResult } from '../proxy'\nimport { handleRoute } from './page-route'\nimport { reportFetch } from './report'\n\nexport interface NextFixture {\n  onFetch: (handler: <PERSON>tchH<PERSON>ler) => void\n}\n\nclass NextFixtureImpl implements NextFixture {\n  public readonly testId: string\n  private fetchHandlers: FetchHandler[] = []\n\n  constructor(\n    private testInfo: TestInfo,\n    private options: NextOptions,\n    private worker: NextWorkerFixture,\n    private page: Page\n  ) {\n    this.testId = testInfo.testId\n    worker.onFetch(this.testId, this.handleFetch.bind(this))\n  }\n\n  async setup(): Promise<void> {\n    const testHeaders = {\n      'Next-Test-Proxy-Port': String(this.worker.proxyPort),\n      'Next-Test-Data': this.testId,\n    }\n\n    await this.page\n      .context()\n      .route('**', (route) =>\n        handleRoute(route, this.page, testHeaders, this.handleFetch.bind(this))\n      )\n  }\n\n  teardown(): void {\n    this.worker.cleanupTest(this.testId)\n  }\n\n  onFetch(handler: FetchHandler): void {\n    this.fetchHandlers.push(handler)\n  }\n\n  private async handleFetch(request: Request): Promise<FetchHandlerResult> {\n    return reportFetch(this.testInfo, request, async (req) => {\n      for (const handler of this.fetchHandlers.slice().reverse()) {\n        const result = await handler(req.clone())\n        if (result) {\n          return result\n        }\n      }\n      if (this.options.fetchLoopback) {\n        return fetch(req.clone())\n      }\n      return undefined\n    })\n  }\n}\n\nexport async function applyNextFixture(\n  use: (fixture: NextFixture) => Promise<void>,\n  {\n    testInfo,\n    nextOptions,\n    nextWorker,\n    page,\n  }: {\n    testInfo: TestInfo\n    nextOptions: NextOptions\n    nextWorker: NextWorkerFixture\n    page: Page\n  }\n): Promise<void> {\n  const fixture = new NextFixtureImpl(testInfo, nextOptions, nextWorker, page)\n\n  await fixture.setup()\n  // eslint-disable-next-line react-hooks/rules-of-hooks -- not React.use()\n  await use(fixture)\n\n  fixture.teardown()\n}\n"], "names": ["applyNextFixture", "NextFixtureImpl", "constructor", "testInfo", "options", "worker", "page", "fetchHandlers", "testId", "onFetch", "handleFetch", "bind", "setup", "testHeaders", "String", "proxyPort", "context", "route", "handleRoute", "teardown", "cleanupTest", "handler", "push", "request", "reportFetch", "req", "slice", "reverse", "result", "clone", "fetch<PERSON><PERSON><PERSON>", "fetch", "undefined", "use", "nextOptions", "nextWorker", "fixture"], "mappings": ";;;;+BA8DsBA;;;eAAAA;;;2BA1DM;wBACA;AAM5B,MAAMC;IAIJC,YACE,AAAQC,QAAkB,EAC1B,AAAQC,OAAoB,EAC5B,AAAQC,MAAyB,EACjC,AAAQC,IAAU,CAClB;aAJQH,WAAAA;aACAC,UAAAA;aACAC,SAAAA;aACAC,OAAAA;aANFC,gBAAgC,EAAE;QAQxC,IAAI,CAACC,MAAM,GAAGL,SAASK,MAAM;QAC7BH,OAAOI,OAAO,CAAC,IAAI,CAACD,MAAM,EAAE,IAAI,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI;IACxD;IAEA,MAAMC,QAAuB;QAC3B,MAAMC,cAAc;YAClB,wBAAwBC,OAAO,IAAI,CAACT,MAAM,CAACU,SAAS;YACpD,kBAAkB,IAAI,CAACP,MAAM;QAC/B;QAEA,MAAM,IAAI,CAACF,IAAI,CACZU,OAAO,GACPC,KAAK,CAAC,MAAM,CAACA,QACZC,IAAAA,sBAAW,EAACD,OAAO,IAAI,CAACX,IAAI,EAAEO,aAAa,IAAI,CAACH,WAAW,CAACC,IAAI,CAAC,IAAI;IAE3E;IAEAQ,WAAiB;QACf,IAAI,CAACd,MAAM,CAACe,WAAW,CAAC,IAAI,CAACZ,MAAM;IACrC;IAEAC,QAAQY,OAAqB,EAAQ;QACnC,IAAI,CAACd,aAAa,CAACe,IAAI,CAACD;IAC1B;IAEA,MAAcX,YAAYa,OAAgB,EAA+B;QACvE,OAAOC,IAAAA,mBAAW,EAAC,IAAI,CAACrB,QAAQ,EAAEoB,SAAS,OAAOE;YAChD,KAAK,MAAMJ,WAAW,IAAI,CAACd,aAAa,CAACmB,KAAK,GAAGC,OAAO,GAAI;gBAC1D,MAAMC,SAAS,MAAMP,QAAQI,IAAII,KAAK;gBACtC,IAAID,QAAQ;oBACV,OAAOA;gBACT;YACF;YACA,IAAI,IAAI,CAACxB,OAAO,CAAC0B,aAAa,EAAE;gBAC9B,OAAOC,MAAMN,IAAII,KAAK;YACxB;YACA,OAAOG;QACT;IACF;AACF;AAEO,eAAehC,iBACpBiC,GAA4C,EAC5C,EACE9B,QAAQ,EACR+B,WAAW,EACXC,UAAU,EACV7B,IAAI,EAML;IAED,MAAM8B,UAAU,IAAInC,gBAAgBE,UAAU+B,aAAaC,YAAY7B;IAEvE,MAAM8B,QAAQxB,KAAK;IACnB,yEAAyE;IACzE,MAAMqB,IAAIG;IAEVA,QAAQjB,QAAQ;AAClB"}