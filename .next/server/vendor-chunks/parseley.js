"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/parseley";
exports.ids = ["vendor-chunks/parseley"];
exports.modules = {

/***/ "(rsc)/./node_modules/parseley/lib/parseley.mjs":
/*!************************************************!*\
  !*** ./node_modules/parseley/lib/parseley.mjs ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Ast: () => (/* binding */ ast),\n/* harmony export */   compareSelectors: () => (/* binding */ compareSelectors),\n/* harmony export */   compareSpecificity: () => (/* binding */ compareSpecificity),\n/* harmony export */   normalize: () => (/* binding */ normalize),\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   parse1: () => (/* binding */ parse1),\n/* harmony export */   serialize: () => (/* binding */ serialize)\n/* harmony export */ });\n/* harmony import */ var leac__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! leac */ \"(rsc)/./node_modules/leac/lib/leac.mjs\");\n/* harmony import */ var peberminta__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! peberminta */ \"(rsc)/./node_modules/peberminta/lib/core.mjs\");\n\n\n\nvar ast = /*#__PURE__*/Object.freeze({\n    __proto__: null\n});\n\nconst ws = `(?:[ \\\\t\\\\r\\\\n\\\\f]*)`;\nconst nl = `(?:\\\\n|\\\\r\\\\n|\\\\r|\\\\f)`;\nconst nonascii = `[^\\\\x00-\\\\x7F]`;\nconst unicode = `(?:\\\\\\\\[0-9a-f]{1,6}(?:\\\\r\\\\n|[ \\\\n\\\\r\\\\t\\\\f])?)`;\nconst escape = `(?:\\\\\\\\[^\\\\n\\\\r\\\\f0-9a-f])`;\nconst nmstart = `(?:[_a-z]|${nonascii}|${unicode}|${escape})`;\nconst nmchar = `(?:[_a-z0-9-]|${nonascii}|${unicode}|${escape})`;\nconst name = `(?:${nmchar}+)`;\nconst ident = `(?:[-]?${nmstart}${nmchar}*)`;\nconst string1 = `'([^\\\\n\\\\r\\\\f\\\\\\\\']|\\\\\\\\${nl}|${nonascii}|${unicode}|${escape})*'`;\nconst string2 = `\"([^\\\\n\\\\r\\\\f\\\\\\\\\"]|\\\\\\\\${nl}|${nonascii}|${unicode}|${escape})*\"`;\nconst lexSelector = (0,leac__WEBPACK_IMPORTED_MODULE_0__.createLexer)([\n    { name: 'ws', regex: new RegExp(ws) },\n    { name: 'hash', regex: new RegExp(`#${name}`, 'i') },\n    { name: 'ident', regex: new RegExp(ident, 'i') },\n    { name: 'str1', regex: new RegExp(string1, 'i') },\n    { name: 'str2', regex: new RegExp(string2, 'i') },\n    { name: '*' },\n    { name: '.' },\n    { name: ',' },\n    { name: '[' },\n    { name: ']' },\n    { name: '=' },\n    { name: '>' },\n    { name: '|' },\n    { name: '+' },\n    { name: '~' },\n    { name: '^' },\n    { name: '$' },\n]);\nconst lexEscapedString = (0,leac__WEBPACK_IMPORTED_MODULE_0__.createLexer)([\n    { name: 'unicode', regex: new RegExp(unicode, 'i') },\n    { name: 'escape', regex: new RegExp(escape, 'i') },\n    { name: 'any', regex: new RegExp('[\\\\s\\\\S]', 'i') }\n]);\nfunction sumSpec([a0, a1, a2], [b0, b1, b2]) {\n    return [a0 + b0, a1 + b1, a2 + b2];\n}\nfunction sumAllSpec(ss) {\n    return ss.reduce(sumSpec, [0, 0, 0]);\n}\nconst unicodeEscapedSequence_ = peberminta__WEBPACK_IMPORTED_MODULE_1__.token((t) => t.name === 'unicode' ? String.fromCodePoint(parseInt(t.text.slice(1), 16)) : undefined);\nconst escapedSequence_ = peberminta__WEBPACK_IMPORTED_MODULE_1__.token((t) => t.name === 'escape' ? t.text.slice(1) : undefined);\nconst anyChar_ = peberminta__WEBPACK_IMPORTED_MODULE_1__.token((t) => t.name === 'any' ? t.text : undefined);\nconst escapedString_ = peberminta__WEBPACK_IMPORTED_MODULE_1__.map(peberminta__WEBPACK_IMPORTED_MODULE_1__.many(peberminta__WEBPACK_IMPORTED_MODULE_1__.or(unicodeEscapedSequence_, escapedSequence_, anyChar_)), (cs) => cs.join(''));\nfunction unescape(escapedString) {\n    const lexerResult = lexEscapedString(escapedString);\n    const result = escapedString_({ tokens: lexerResult.tokens, options: undefined }, 0);\n    return result.value;\n}\nfunction literal(name) {\n    return peberminta__WEBPACK_IMPORTED_MODULE_1__.token((t) => t.name === name ? true : undefined);\n}\nconst whitespace_ = peberminta__WEBPACK_IMPORTED_MODULE_1__.token((t) => t.name === 'ws' ? null : undefined);\nconst optionalWhitespace_ = peberminta__WEBPACK_IMPORTED_MODULE_1__.option(whitespace_, null);\nfunction optionallySpaced(parser) {\n    return peberminta__WEBPACK_IMPORTED_MODULE_1__.middle(optionalWhitespace_, parser, optionalWhitespace_);\n}\nconst identifier_ = peberminta__WEBPACK_IMPORTED_MODULE_1__.token((t) => t.name === 'ident' ? unescape(t.text) : undefined);\nconst hashId_ = peberminta__WEBPACK_IMPORTED_MODULE_1__.token((t) => t.name === 'hash' ? unescape(t.text.slice(1)) : undefined);\nconst string_ = peberminta__WEBPACK_IMPORTED_MODULE_1__.token((t) => t.name.startsWith('str') ? unescape(t.text.slice(1, -1)) : undefined);\nconst namespace_ = peberminta__WEBPACK_IMPORTED_MODULE_1__.left(peberminta__WEBPACK_IMPORTED_MODULE_1__.option(identifier_, ''), literal('|'));\nconst qualifiedName_ = peberminta__WEBPACK_IMPORTED_MODULE_1__.eitherOr(peberminta__WEBPACK_IMPORTED_MODULE_1__.ab(namespace_, identifier_, (ns, name) => ({ name: name, namespace: ns })), peberminta__WEBPACK_IMPORTED_MODULE_1__.map(identifier_, (name) => ({ name: name, namespace: null })));\nconst uniSelector_ = peberminta__WEBPACK_IMPORTED_MODULE_1__.eitherOr(peberminta__WEBPACK_IMPORTED_MODULE_1__.ab(namespace_, literal('*'), (ns) => ({ type: 'universal', namespace: ns, specificity: [0, 0, 0] })), peberminta__WEBPACK_IMPORTED_MODULE_1__.map(literal('*'), () => ({ type: 'universal', namespace: null, specificity: [0, 0, 0] })));\nconst tagSelector_ = peberminta__WEBPACK_IMPORTED_MODULE_1__.map(qualifiedName_, ({ name, namespace }) => ({\n    type: 'tag',\n    name: name,\n    namespace: namespace,\n    specificity: [0, 0, 1]\n}));\nconst classSelector_ = peberminta__WEBPACK_IMPORTED_MODULE_1__.ab(literal('.'), identifier_, (fullstop, name) => ({\n    type: 'class',\n    name: name,\n    specificity: [0, 1, 0]\n}));\nconst idSelector_ = peberminta__WEBPACK_IMPORTED_MODULE_1__.map(hashId_, (name) => ({\n    type: 'id',\n    name: name,\n    specificity: [1, 0, 0]\n}));\nconst attrModifier_ = peberminta__WEBPACK_IMPORTED_MODULE_1__.token((t) => {\n    if (t.name === 'ident') {\n        if (t.text === 'i' || t.text === 'I') {\n            return 'i';\n        }\n        if (t.text === 's' || t.text === 'S') {\n            return 's';\n        }\n    }\n    return undefined;\n});\nconst attrValue_ = peberminta__WEBPACK_IMPORTED_MODULE_1__.eitherOr(peberminta__WEBPACK_IMPORTED_MODULE_1__.ab(string_, peberminta__WEBPACK_IMPORTED_MODULE_1__.option(peberminta__WEBPACK_IMPORTED_MODULE_1__.right(optionalWhitespace_, attrModifier_), null), (v, mod) => ({ value: v, modifier: mod })), peberminta__WEBPACK_IMPORTED_MODULE_1__.ab(identifier_, peberminta__WEBPACK_IMPORTED_MODULE_1__.option(peberminta__WEBPACK_IMPORTED_MODULE_1__.right(whitespace_, attrModifier_), null), (v, mod) => ({ value: v, modifier: mod })));\nconst attrMatcher_ = peberminta__WEBPACK_IMPORTED_MODULE_1__.choice(peberminta__WEBPACK_IMPORTED_MODULE_1__.map(literal('='), () => '='), peberminta__WEBPACK_IMPORTED_MODULE_1__.ab(literal('~'), literal('='), () => '~='), peberminta__WEBPACK_IMPORTED_MODULE_1__.ab(literal('|'), literal('='), () => '|='), peberminta__WEBPACK_IMPORTED_MODULE_1__.ab(literal('^'), literal('='), () => '^='), peberminta__WEBPACK_IMPORTED_MODULE_1__.ab(literal('$'), literal('='), () => '$='), peberminta__WEBPACK_IMPORTED_MODULE_1__.ab(literal('*'), literal('='), () => '*='));\nconst attrPresenceSelector_ = peberminta__WEBPACK_IMPORTED_MODULE_1__.abc(literal('['), optionallySpaced(qualifiedName_), literal(']'), (lbr, { name, namespace }) => ({\n    type: 'attrPresence',\n    name: name,\n    namespace: namespace,\n    specificity: [0, 1, 0]\n}));\nconst attrValueSelector_ = peberminta__WEBPACK_IMPORTED_MODULE_1__.middle(literal('['), peberminta__WEBPACK_IMPORTED_MODULE_1__.abc(optionallySpaced(qualifiedName_), attrMatcher_, optionallySpaced(attrValue_), ({ name, namespace }, matcher, { value, modifier }) => ({\n    type: 'attrValue',\n    name: name,\n    namespace: namespace,\n    matcher: matcher,\n    value: value,\n    modifier: modifier,\n    specificity: [0, 1, 0]\n})), literal(']'));\nconst attrSelector_ = peberminta__WEBPACK_IMPORTED_MODULE_1__.eitherOr(attrPresenceSelector_, attrValueSelector_);\nconst typeSelector_ = peberminta__WEBPACK_IMPORTED_MODULE_1__.eitherOr(uniSelector_, tagSelector_);\nconst subclassSelector_ = peberminta__WEBPACK_IMPORTED_MODULE_1__.choice(idSelector_, classSelector_, attrSelector_);\nconst compoundSelector_ = peberminta__WEBPACK_IMPORTED_MODULE_1__.map(peberminta__WEBPACK_IMPORTED_MODULE_1__.eitherOr(peberminta__WEBPACK_IMPORTED_MODULE_1__.flatten(typeSelector_, peberminta__WEBPACK_IMPORTED_MODULE_1__.many(subclassSelector_)), peberminta__WEBPACK_IMPORTED_MODULE_1__.many1(subclassSelector_)), (ss) => {\n    return {\n        type: 'compound',\n        list: ss,\n        specificity: sumAllSpec(ss.map(s => s.specificity))\n    };\n});\nconst combinator_ = peberminta__WEBPACK_IMPORTED_MODULE_1__.choice(peberminta__WEBPACK_IMPORTED_MODULE_1__.map(literal('>'), () => '>'), peberminta__WEBPACK_IMPORTED_MODULE_1__.map(literal('+'), () => '+'), peberminta__WEBPACK_IMPORTED_MODULE_1__.map(literal('~'), () => '~'), peberminta__WEBPACK_IMPORTED_MODULE_1__.ab(literal('|'), literal('|'), () => '||'));\nconst combinatorSeparator_ = peberminta__WEBPACK_IMPORTED_MODULE_1__.eitherOr(optionallySpaced(combinator_), peberminta__WEBPACK_IMPORTED_MODULE_1__.map(whitespace_, () => ' '));\nconst complexSelector_ = peberminta__WEBPACK_IMPORTED_MODULE_1__.leftAssoc2(compoundSelector_, peberminta__WEBPACK_IMPORTED_MODULE_1__.map(combinatorSeparator_, (c) => (left, right) => ({\n    type: 'compound',\n    list: [...right.list, { type: 'combinator', combinator: c, left: left, specificity: left.specificity }],\n    specificity: sumSpec(left.specificity, right.specificity)\n})), compoundSelector_);\nconst listSelector_ = peberminta__WEBPACK_IMPORTED_MODULE_1__.leftAssoc2(peberminta__WEBPACK_IMPORTED_MODULE_1__.map(complexSelector_, (s) => ({ type: 'list', list: [s] })), peberminta__WEBPACK_IMPORTED_MODULE_1__.map(optionallySpaced(literal(',')), () => (acc, next) => ({ type: 'list', list: [...acc.list, next] })), complexSelector_);\nfunction parse_(parser, str) {\n    if (!(typeof str === 'string' || str instanceof String)) {\n        throw new Error('Expected a selector string. Actual input is not a string!');\n    }\n    const lexerResult = lexSelector(str);\n    if (!lexerResult.complete) {\n        throw new Error(`The input \"${str}\" was only partially tokenized, stopped at offset ${lexerResult.offset}!\\n` +\n            prettyPrintPosition(str, lexerResult.offset));\n    }\n    const result = optionallySpaced(parser)({ tokens: lexerResult.tokens, options: undefined }, 0);\n    if (!result.matched) {\n        throw new Error(`No match for \"${str}\" input!`);\n    }\n    if (result.position < lexerResult.tokens.length) {\n        const token = lexerResult.tokens[result.position];\n        throw new Error(`The input \"${str}\" was only partially parsed, stopped at offset ${token.offset}!\\n` +\n            prettyPrintPosition(str, token.offset, token.len));\n    }\n    return result.value;\n}\nfunction prettyPrintPosition(str, offset, len = 1) {\n    return `${str.replace(/(\\t)|(\\r)|(\\n)/g, (m, t, r) => t ? '\\u2409' : r ? '\\u240d' : '\\u240a')}\\n${''.padEnd(offset)}${'^'.repeat(len)}`;\n}\nfunction parse(str) {\n    return parse_(listSelector_, str);\n}\nfunction parse1(str) {\n    return parse_(complexSelector_, str);\n}\n\nfunction serialize(selector) {\n    if (!selector.type) {\n        throw new Error('This is not an AST node.');\n    }\n    switch (selector.type) {\n        case 'universal':\n            return _serNs(selector.namespace) + '*';\n        case 'tag':\n            return _serNs(selector.namespace) + _serIdent(selector.name);\n        case 'class':\n            return '.' + _serIdent(selector.name);\n        case 'id':\n            return '#' + _serIdent(selector.name);\n        case 'attrPresence':\n            return `[${_serNs(selector.namespace)}${_serIdent(selector.name)}]`;\n        case 'attrValue':\n            return `[${_serNs(selector.namespace)}${_serIdent(selector.name)}${selector.matcher}\"${_serStr(selector.value)}\"${(selector.modifier ? selector.modifier : '')}]`;\n        case 'combinator':\n            return serialize(selector.left) + selector.combinator;\n        case 'compound':\n            return selector.list.reduce((acc, node) => {\n                if (node.type === 'combinator') {\n                    return serialize(node) + acc;\n                }\n                else {\n                    return acc + serialize(node);\n                }\n            }, '');\n        case 'list':\n            return selector.list.map(serialize).join(',');\n    }\n}\nfunction _serNs(ns) {\n    return (ns || ns === '')\n        ? _serIdent(ns) + '|'\n        : '';\n}\nfunction _codePoint(char) {\n    return `\\\\${char.codePointAt(0).toString(16)} `;\n}\nfunction _serIdent(str) {\n    return str.replace(\n    /(^[0-9])|(^-[0-9])|(^-$)|([-0-9a-zA-Z_]|[^\\x00-\\x7F])|(\\x00)|([\\x01-\\x1f]|\\x7f)|([\\s\\S])/g, (m, d1, d2, hy, safe, nl, ctrl, other) => d1 ? _codePoint(d1) :\n        d2 ? '-' + _codePoint(d2.slice(1)) :\n            hy ? '\\\\-' :\n                safe ? safe :\n                    nl ? '\\ufffd' :\n                        ctrl ? _codePoint(ctrl) :\n                            '\\\\' + other);\n}\nfunction _serStr(str) {\n    return str.replace(\n    /(\")|(\\\\)|(\\x00)|([\\x01-\\x1f]|\\x7f)/g, (m, dq, bs, nl, ctrl) => dq ? '\\\\\"' :\n        bs ? '\\\\\\\\' :\n            nl ? '\\ufffd' :\n                _codePoint(ctrl));\n}\nfunction normalize(selector) {\n    if (!selector.type) {\n        throw new Error('This is not an AST node.');\n    }\n    switch (selector.type) {\n        case 'compound': {\n            selector.list.forEach(normalize);\n            selector.list.sort((a, b) => _compareArrays(_getSelectorPriority(a), _getSelectorPriority(b)));\n            break;\n        }\n        case 'combinator': {\n            normalize(selector.left);\n            break;\n        }\n        case 'list': {\n            selector.list.forEach(normalize);\n            selector.list.sort((a, b) => (serialize(a) < serialize(b)) ? -1 : 1);\n            break;\n        }\n    }\n    return selector;\n}\nfunction _getSelectorPriority(selector) {\n    switch (selector.type) {\n        case 'universal':\n            return [1];\n        case 'tag':\n            return [1];\n        case 'id':\n            return [2];\n        case 'class':\n            return [3, selector.name];\n        case 'attrPresence':\n            return [4, serialize(selector)];\n        case 'attrValue':\n            return [5, serialize(selector)];\n        case 'combinator':\n            return [15, serialize(selector)];\n    }\n}\nfunction compareSelectors(a, b) {\n    return _compareArrays(a.specificity, b.specificity);\n}\nfunction compareSpecificity(a, b) {\n    return _compareArrays(a, b);\n}\nfunction _compareArrays(a, b) {\n    if (!Array.isArray(a) || !Array.isArray(b)) {\n        throw new Error('Arguments must be arrays.');\n    }\n    const shorter = (a.length < b.length) ? a.length : b.length;\n    for (let i = 0; i < shorter; i++) {\n        if (a[i] === b[i]) {\n            continue;\n        }\n        return (a[i] < b[i]) ? -1 : 1;\n    }\n    return a.length - b.length;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/parseley/lib/parseley.mjs\n");

/***/ })

};
;