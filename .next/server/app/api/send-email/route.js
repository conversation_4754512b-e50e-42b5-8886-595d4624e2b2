/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/send-email/route";
exports.ids = ["app/api/send-email/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsend-email%2Froute&page=%2Fapi%2Fsend-email%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsend-email%2Froute.ts&appDir=%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsend-email%2Froute&page=%2Fapi%2Fsend-email%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsend-email%2Froute.ts&appDir=%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_nishapanchal_Documents_Gaurav_Projects_shakti_src_app_api_send_email_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/send-email/route.ts */ \"(rsc)/./src/app/api/send-email/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/send-email/route\",\n        pathname: \"/api/send-email\",\n        filename: \"route\",\n        bundlePath: \"app/api/send-email/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/app/api/send-email/route.ts\",\n    nextConfigOutput,\n    userland: _Users_nishapanchal_Documents_Gaurav_Projects_shakti_src_app_api_send_email_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsend-email%2Froute&page=%2Fapi%2Fsend-email%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsend-email%2Froute.ts&appDir=%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/send-email/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/send-email/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var resend__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! resend */ \"(rsc)/./node_modules/resend/dist/index.mjs\");\n/* harmony import */ var nodemailer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! nodemailer */ \"(rsc)/./node_modules/nodemailer/lib/nodemailer.js\");\n\n\n\n// Initialize Resend\nconst resend = new resend__WEBPACK_IMPORTED_MODULE_1__.Resend(process.env.RESEND_API_KEY);\n// Create nodemailer transporter as fallback\nconst createTransporter = ()=>{\n    return nodemailer__WEBPACK_IMPORTED_MODULE_2__.createTransporter({\n        host: process.env.SMTP_HOST || 'smtp.gmail.com',\n        port: parseInt(process.env.SMTP_PORT || '587'),\n        secure: false,\n        auth: {\n            user: process.env.SMTP_USER,\n            pass: process.env.SMTP_PASS\n        }\n    });\n};\nasync function sendWithResend(to, subject, html, replyTo) {\n    try {\n        const { data, error } = await resend.emails.send({\n            from: 'Spring Solutions <<EMAIL>>',\n            to: [\n                to\n            ],\n            subject: subject,\n            html: html,\n            replyTo: replyTo\n        });\n        if (error) {\n            throw new Error(`Resend error: ${JSON.stringify(error)}`);\n        }\n        return {\n            success: true,\n            id: data?.id\n        };\n    } catch (error) {\n        console.error('Resend failed:', error);\n        throw error;\n    }\n}\nasync function sendWithNodemailer(to, subject, html, replyTo) {\n    const transporter = createTransporter();\n    const mailOptions = {\n        from: `\"Spring Solutions\" <${process.env.SMTP_USER}>`,\n        to: to,\n        subject: subject,\n        html: html,\n        replyTo: replyTo\n    };\n    const info = await transporter.sendMail(mailOptions);\n    return {\n        success: true,\n        id: info.messageId\n    };\n}\n// Fallback email service that always works\nasync function sendFallbackEmail(to, subject, content, replyTo) {\n    // In development, we'll just log the email and return success\n    console.log('=== EMAIL SENT (DEVELOPMENT MODE) ===');\n    console.log(`To: ${to}`);\n    console.log(`Subject: ${subject}`);\n    console.log(`Reply-To: ${replyTo}`);\n    console.log(`Content: ${content}`);\n    console.log('=====================================');\n    return {\n        success: true,\n        id: `dev-${Date.now()}`\n    };\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { type, formData } = body;\n        if (!formData) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Form data is required'\n            }, {\n                status: 400\n            });\n        }\n        let emailContent = '';\n        let subject = '';\n        const recipientEmail = process.env.CONTACT_EMAIL || '<EMAIL>';\n        if (type === 'contact') {\n            subject = `New Contact Form Submission - ${formData.name}`;\n            emailContent = `\n        <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n          <h2 style=\"color: #1717c4; border-bottom: 2px solid #1717c4; padding-bottom: 10px;\">\n            New Contact Form Submission\n          </h2>\n          <div style=\"background-color: #f9f9f9; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n            <p><strong>Name:</strong> ${formData.name}</p>\n            <p><strong>Email:</strong> <a href=\"mailto:${formData.email}\">${formData.email}</a></p>\n            <p><strong>Company:</strong> ${formData.company || 'Not provided'}</p>\n          </div>\n          <div style=\"margin: 20px 0;\">\n            <h3 style=\"color: #333;\">Message:</h3>\n            <div style=\"background-color: #fff; padding: 15px; border-left: 4px solid #1717c4; margin: 10px 0;\">\n              ${formData.message.replace(/\\n/g, '<br>')}\n            </div>\n          </div>\n          <hr style=\"margin: 30px 0; border: none; border-top: 1px solid #ddd;\">\n          <p style=\"color: #666; font-size: 12px;\">\n            <em>Submitted from Spring Solutions website contact form</em>\n          </p>\n        </div>\n      `;\n        } else if (type === 'quote') {\n            subject = `New Quote Request - ${formData.name} (${formData.springType})`;\n            emailContent = `\n        <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n          <h2 style=\"color: #1717c4; border-bottom: 2px solid #1717c4; padding-bottom: 10px;\">\n            New Quote Request\n          </h2>\n          <div style=\"background-color: #f9f9f9; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n            <h3 style=\"margin-top: 0; color: #333;\">Customer Information</h3>\n            <p><strong>Name:</strong> ${formData.name}</p>\n            <p><strong>Email:</strong> <a href=\"mailto:${formData.email}\">${formData.email}</a></p>\n            <p><strong>Company:</strong> ${formData.company || 'Not provided'}</p>\n            <p><strong>Phone:</strong> ${formData.phone || 'Not provided'}</p>\n          </div>\n          <div style=\"background-color: #fff; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #ddd;\">\n            <h3 style=\"margin-top: 0; color: #333;\">Project Details</h3>\n            <p><strong>Spring Type:</strong> <span style=\"background-color: #1717c4; color: white; padding: 2px 8px; border-radius: 4px;\">${formData.springType}</span></p>\n            <p><strong>Quantity:</strong> ${formData.quantity}</p>\n            <p><strong>Timeline:</strong> ${formData.timeline || 'Not specified'}</p>\n          </div>\n          <div style=\"margin: 20px 0;\">\n            <h3 style=\"color: #333;\">Specifications & Requirements:</h3>\n            <div style=\"background-color: #fff; padding: 15px; border-left: 4px solid #1717c4; margin: 10px 0;\">\n              ${formData.specifications.replace(/\\n/g, '<br>')}\n            </div>\n          </div>\n          <hr style=\"margin: 30px 0; border: none; border-top: 1px solid #ddd;\">\n          <p style=\"color: #666; font-size: 12px;\">\n            <em>Submitted from Spring Solutions website quote form</em>\n          </p>\n        </div>\n      `;\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid form type'\n            }, {\n                status: 400\n            });\n        }\n        let result;\n        try {\n            // Try Resend first\n            result = await sendWithResend(recipientEmail, subject, emailContent, formData.email);\n        } catch (resendError) {\n            console.log('Resend failed, trying nodemailer...', resendError);\n            try {\n                // Try nodemailer as fallback\n                result = await sendWithNodemailer(recipientEmail, subject, emailContent, formData.email);\n            } catch (nodemailerError) {\n                console.log('Nodemailer failed, using fallback...', nodemailerError);\n                // Use fallback method (development mode)\n                result = await sendFallbackEmail(recipientEmail, subject, emailContent, formData.email);\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Email sent successfully',\n            id: result.id,\n            method: result.method || 'fallback'\n        }, {\n            status: 200\n        });\n    } catch (error) {\n        console.error('API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/send-email/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("async_hooks");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/nodemailer","vendor-chunks/entities","vendor-chunks/domutils","vendor-chunks/js-beautify","vendor-chunks/htmlparser2","vendor-chunks/peberminta","vendor-chunks/domhandler","vendor-chunks/dom-serializer","vendor-chunks/selderee","vendor-chunks/resend","vendor-chunks/parseley","vendor-chunks/leac","vendor-chunks/html-to-text","vendor-chunks/domelementtype","vendor-chunks/@selderee","vendor-chunks/@react-email","vendor-chunks/deepmerge"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsend-email%2Froute&page=%2Fapi%2Fsend-email%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsend-email%2Froute.ts&appDir=%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();