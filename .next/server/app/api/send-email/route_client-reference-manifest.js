globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/api/send-email/route"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/components/ui/toast.tsx":{"*":{"id":"(ssr)/./src/components/ui/toast.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx":{"*":{"id":"(ssr)/./src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/screens/HomeDesktop/sections/HeaderSection/HeaderSection.tsx":{"*":{"id":"(ssr)/./src/components/screens/HomeDesktop/sections/HeaderSection/HeaderSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx":{"*":{"id":"(ssr)/./src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx":{"*":{"id":"(ssr)/./src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/avatar.tsx":{"*":{"id":"(ssr)/./src/components/ui/avatar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/separator.tsx":{"*":{"id":"(ssr)/./src/components/ui/separator.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/QuoteModal.tsx":{"*":{"id":"(ssr)/./src/components/QuoteModal.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Documents/Gaurav/Projects/shakti/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Archivo\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-archivo\",\"display\":\"swap\"}],\"variableName\":\"archivo\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Archivo\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-archivo\",\"display\":\"swap\"}],\"variableName\":\"archivo\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Documents/Gaurav/Projects/shakti/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Lexend_Deca\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-lexend-deca\",\"display\":\"swap\"}],\"variableName\":\"lexendDeca\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Lexend_Deca\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-lexend-deca\",\"display\":\"swap\"}],\"variableName\":\"lexendDeca\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/app/globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/toast.tsx":{"id":"(app-pages-browser)/./src/components/ui/toast.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Documents/Gaurav/Projects/shakti/node_modules/next/dist/client/image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/Gaurav/Projects/shakti/node_modules/next/dist/esm/client/image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx":{"id":"(app-pages-browser)/./src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/HeaderSection/HeaderSection.tsx":{"id":"(app-pages-browser)/./src/components/screens/HomeDesktop/sections/HeaderSection/HeaderSection.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx":{"id":"(app-pages-browser)/./src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx":{"id":"(app-pages-browser)/./src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/avatar.tsx":{"id":"(app-pages-browser)/./src/components/ui/avatar.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/separator.tsx":{"id":"(app-pages-browser)/./src/components/ui/separator.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/Gaurav/Projects/shakti/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Gaurav/Projects/shakti/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Gaurav/Projects/shakti/node_modules/next/dist/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Gaurav/Projects/shakti/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Gaurav/Projects/shakti/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Gaurav/Projects/shakti/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Gaurav/Projects/shakti/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Gaurav/Projects/shakti/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Gaurav/Projects/shakti/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Gaurav/Projects/shakti/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Gaurav/Projects/shakti/node_modules/next/dist/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Gaurav/Projects/shakti/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Gaurav/Projects/shakti/node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Gaurav/Projects/shakti/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Gaurav/Projects/shakti/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Gaurav/Projects/shakti/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/QuoteModal.tsx":{"id":"(app-pages-browser)/./src/components/QuoteModal.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/":[],"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/app/layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/app/page":[],"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/app/api/send-email/route":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/toast.tsx":{"*":{"id":"(rsc)/./src/components/ui/toast.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx":{"*":{"id":"(rsc)/./src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/screens/HomeDesktop/sections/HeaderSection/HeaderSection.tsx":{"*":{"id":"(rsc)/./src/components/screens/HomeDesktop/sections/HeaderSection/HeaderSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx":{"*":{"id":"(rsc)/./src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx":{"*":{"id":"(rsc)/./src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/avatar.tsx":{"*":{"id":"(rsc)/./src/components/ui/avatar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/separator.tsx":{"*":{"id":"(rsc)/./src/components/ui/separator.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/QuoteModal.tsx":{"*":{"id":"(rsc)/./src/components/QuoteModal.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}