/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contact/page",{

/***/ "(app-pages-browser)/./node_modules/@radix-ui/react-navigation-menu/dist/index.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-navigation-menu/dist/index.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Indicator: () => (/* binding */ Indicator),\n/* harmony export */   Item: () => (/* binding */ Item),\n/* harmony export */   Link: () => (/* binding */ Link),\n/* harmony export */   List: () => (/* binding */ List),\n/* harmony export */   NavigationMenu: () => (/* binding */ NavigationMenu),\n/* harmony export */   NavigationMenuContent: () => (/* binding */ NavigationMenuContent),\n/* harmony export */   NavigationMenuIndicator: () => (/* binding */ NavigationMenuIndicator),\n/* harmony export */   NavigationMenuItem: () => (/* binding */ NavigationMenuItem),\n/* harmony export */   NavigationMenuLink: () => (/* binding */ NavigationMenuLink),\n/* harmony export */   NavigationMenuList: () => (/* binding */ NavigationMenuList),\n/* harmony export */   NavigationMenuSub: () => (/* binding */ NavigationMenuSub),\n/* harmony export */   NavigationMenuTrigger: () => (/* binding */ NavigationMenuTrigger),\n/* harmony export */   NavigationMenuViewport: () => (/* binding */ NavigationMenuViewport),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Sub: () => (/* binding */ Sub),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createNavigationMenuScope: () => (/* binding */ createNavigationMenuScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-context */ \"(app-pages-browser)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/primitive */ \"(app-pages-browser)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(app-pages-browser)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(app-pages-browser)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(app-pages-browser)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(app-pages-browser)/./node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(app-pages-browser)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-id */ \"(app-pages-browser)/./node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(app-pages-browser)/./node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(app-pages-browser)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(app-pages-browser)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(app-pages-browser)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(app-pages-browser)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(app-pages-browser)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Content,Indicator,Item,Link,List,NavigationMenu,NavigationMenuContent,NavigationMenuIndicator,NavigationMenuItem,NavigationMenuLink,NavigationMenuList,NavigationMenuSub,NavigationMenuTrigger,NavigationMenuViewport,Root,Sub,Trigger,Viewport,createNavigationMenuScope auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$(), _s5 = $RefreshSig$(), _s6 = $RefreshSig$(), _s7 = $RefreshSig$(), _s8 = $RefreshSig$(), _s9 = $RefreshSig$(), _s10 = $RefreshSig$(), _s11 = $RefreshSig$(), _s12 = $RefreshSig$(), _s13 = $RefreshSig$(), _s14 = $RefreshSig$(), _s15 = $RefreshSig$();\n// src/navigation-menu.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar NAVIGATION_MENU_NAME = \"NavigationMenu\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(NAVIGATION_MENU_NAME);\nvar [FocusGroupCollection, useFocusGroupCollection, createFocusGroupCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(NAVIGATION_MENU_NAME);\nvar [createNavigationMenuContext, createNavigationMenuScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__.createContextScope)(NAVIGATION_MENU_NAME, [\n    createCollectionScope,\n    createFocusGroupCollectionScope\n]);\nvar [NavigationMenuProviderImpl, useNavigationMenuContext] = createNavigationMenuContext(NAVIGATION_MENU_NAME);\nvar [ViewportContentProvider, useViewportContentContext] = createNavigationMenuContext(NAVIGATION_MENU_NAME);\nvar NavigationMenu = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c = _s((props, forwardedRef)=>{\n    _s();\n    const { __scopeNavigationMenu, value: valueProp, onValueChange, defaultValue, delayDuration = 200, skipDelayDuration = 300, orientation = \"horizontal\", dir, ...NavigationMenuProps } = props;\n    const [navigationMenu, setNavigationMenu] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, {\n        \"NavigationMenu.useComposedRefs[composedRef]\": (node)=>setNavigationMenu(node)\n    }[\"NavigationMenu.useComposedRefs[composedRef]\"]);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__.useDirection)(dir);\n    const openTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const closeTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const skipDelayTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const [isOpenDelayed, setIsOpenDelayed] = react__WEBPACK_IMPORTED_MODULE_0__.useState(true);\n    const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n        prop: valueProp,\n        onChange: {\n            \"NavigationMenu.useControllableState\": (value2)=>{\n                const isOpen = value2 !== \"\";\n                const hasSkipDelayDuration = skipDelayDuration > 0;\n                if (isOpen) {\n                    window.clearTimeout(skipDelayTimerRef.current);\n                    if (hasSkipDelayDuration) setIsOpenDelayed(false);\n                } else {\n                    window.clearTimeout(skipDelayTimerRef.current);\n                    skipDelayTimerRef.current = window.setTimeout({\n                        \"NavigationMenu.useControllableState\": ()=>setIsOpenDelayed(true)\n                    }[\"NavigationMenu.useControllableState\"], skipDelayDuration);\n                }\n                onValueChange === null || onValueChange === void 0 ? void 0 : onValueChange(value2);\n            }\n        }[\"NavigationMenu.useControllableState\"],\n        defaultProp: defaultValue !== null && defaultValue !== void 0 ? defaultValue : \"\",\n        caller: NAVIGATION_MENU_NAME\n    });\n    const startCloseTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"NavigationMenu.useCallback[startCloseTimer]\": ()=>{\n            window.clearTimeout(closeTimerRef.current);\n            closeTimerRef.current = window.setTimeout({\n                \"NavigationMenu.useCallback[startCloseTimer]\": ()=>setValue(\"\")\n            }[\"NavigationMenu.useCallback[startCloseTimer]\"], 150);\n        }\n    }[\"NavigationMenu.useCallback[startCloseTimer]\"], [\n        setValue\n    ]);\n    const handleOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"NavigationMenu.useCallback[handleOpen]\": (itemValue)=>{\n            window.clearTimeout(closeTimerRef.current);\n            setValue(itemValue);\n        }\n    }[\"NavigationMenu.useCallback[handleOpen]\"], [\n        setValue\n    ]);\n    const handleDelayedOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"NavigationMenu.useCallback[handleDelayedOpen]\": (itemValue)=>{\n            const isOpenItem = value === itemValue;\n            if (isOpenItem) {\n                window.clearTimeout(closeTimerRef.current);\n            } else {\n                openTimerRef.current = window.setTimeout({\n                    \"NavigationMenu.useCallback[handleDelayedOpen]\": ()=>{\n                        window.clearTimeout(closeTimerRef.current);\n                        setValue(itemValue);\n                    }\n                }[\"NavigationMenu.useCallback[handleDelayedOpen]\"], delayDuration);\n            }\n        }\n    }[\"NavigationMenu.useCallback[handleDelayedOpen]\"], [\n        value,\n        setValue,\n        delayDuration\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"NavigationMenu.useEffect\": ()=>{\n            return ({\n                \"NavigationMenu.useEffect\": ()=>{\n                    window.clearTimeout(openTimerRef.current);\n                    window.clearTimeout(closeTimerRef.current);\n                    window.clearTimeout(skipDelayTimerRef.current);\n                }\n            })[\"NavigationMenu.useEffect\"];\n        }\n    }[\"NavigationMenu.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuProvider, {\n        scope: __scopeNavigationMenu,\n        isRootMenu: true,\n        value,\n        dir: direction,\n        orientation,\n        rootNavigationMenu: navigationMenu,\n        onTriggerEnter: (itemValue)=>{\n            window.clearTimeout(openTimerRef.current);\n            if (isOpenDelayed) handleDelayedOpen(itemValue);\n            else handleOpen(itemValue);\n        },\n        onTriggerLeave: ()=>{\n            window.clearTimeout(openTimerRef.current);\n            startCloseTimer();\n        },\n        onContentEnter: ()=>window.clearTimeout(closeTimerRef.current),\n        onContentLeave: startCloseTimer,\n        onItemSelect: (itemValue)=>{\n            setValue((prevValue)=>prevValue === itemValue ? \"\" : itemValue);\n        },\n        onItemDismiss: ()=>setValue(\"\"),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.nav, {\n            \"aria-label\": \"Main\",\n            \"data-orientation\": orientation,\n            dir: direction,\n            ...NavigationMenuProps,\n            ref: composedRef\n        })\n    });\n}, \"3/+WACBrWJIrqgfBRlK1yUkTr7k=\", false, function() {\n    return [\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs,\n        _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__.useDirection,\n        _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState\n    ];\n})), \"3/+WACBrWJIrqgfBRlK1yUkTr7k=\", false, function() {\n    return [\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs,\n        _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__.useDirection,\n        _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState\n    ];\n});\n_c1 = NavigationMenu;\nNavigationMenu.displayName = NAVIGATION_MENU_NAME;\nvar SUB_NAME = \"NavigationMenuSub\";\nvar NavigationMenuSub = /*#__PURE__*/ _s1(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c2 = _s1((props, forwardedRef)=>{\n    _s1();\n    const { __scopeNavigationMenu, value: valueProp, onValueChange, defaultValue, orientation = \"horizontal\", ...subProps } = props;\n    const context = useNavigationMenuContext(SUB_NAME, __scopeNavigationMenu);\n    const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n        prop: valueProp,\n        onChange: onValueChange,\n        defaultProp: defaultValue !== null && defaultValue !== void 0 ? defaultValue : \"\",\n        caller: SUB_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuProvider, {\n        scope: __scopeNavigationMenu,\n        isRootMenu: false,\n        value,\n        dir: context.dir,\n        orientation,\n        rootNavigationMenu: context.rootNavigationMenu,\n        onTriggerEnter: (itemValue)=>setValue(itemValue),\n        onItemSelect: (itemValue)=>setValue(itemValue),\n        onItemDismiss: ()=>setValue(\"\"),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.div, {\n            \"data-orientation\": orientation,\n            ...subProps,\n            ref: forwardedRef\n        })\n    });\n}, \"2J847yjT4rH9WVxGzVjbIQoyXoY=\", false, function() {\n    return [\n        useNavigationMenuContext,\n        _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState\n    ];\n})), \"2J847yjT4rH9WVxGzVjbIQoyXoY=\", false, function() {\n    return [\n        useNavigationMenuContext,\n        _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState\n    ];\n});\n_c3 = NavigationMenuSub;\nNavigationMenuSub.displayName = SUB_NAME;\nvar NavigationMenuProvider = (props)=>{\n    _s2();\n    const { scope, isRootMenu, rootNavigationMenu, dir, orientation, children, value, onItemSelect, onItemDismiss, onTriggerEnter, onTriggerLeave, onContentEnter, onContentLeave } = props;\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [viewportContent, setViewportContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(/* @__PURE__ */ new Map());\n    const [indicatorTrack, setIndicatorTrack] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuProviderImpl, {\n        scope,\n        isRootMenu,\n        rootNavigationMenu,\n        value,\n        previousValue: (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_9__.usePrevious)(value),\n        baseId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__.useId)(),\n        dir,\n        orientation,\n        viewport,\n        onViewportChange: setViewport,\n        indicatorTrack,\n        onIndicatorTrackChange: setIndicatorTrack,\n        onTriggerEnter: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(onTriggerEnter),\n        onTriggerLeave: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(onTriggerLeave),\n        onContentEnter: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(onContentEnter),\n        onContentLeave: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(onContentLeave),\n        onItemSelect: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(onItemSelect),\n        onItemDismiss: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(onItemDismiss),\n        onViewportContentChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"NavigationMenuProvider.useCallback\": (contentValue, contentData)=>{\n                setViewportContent({\n                    \"NavigationMenuProvider.useCallback\": (prevContent)=>{\n                        prevContent.set(contentValue, contentData);\n                        return new Map(prevContent);\n                    }\n                }[\"NavigationMenuProvider.useCallback\"]);\n            }\n        }[\"NavigationMenuProvider.useCallback\"], []),\n        onViewportContentRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"NavigationMenuProvider.useCallback\": (contentValue)=>{\n                setViewportContent({\n                    \"NavigationMenuProvider.useCallback\": (prevContent)=>{\n                        if (!prevContent.has(contentValue)) return prevContent;\n                        prevContent.delete(contentValue);\n                        return new Map(prevContent);\n                    }\n                }[\"NavigationMenuProvider.useCallback\"]);\n            }\n        }[\"NavigationMenuProvider.useCallback\"], []),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Provider, {\n            scope,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ViewportContentProvider, {\n                scope,\n                items: viewportContent,\n                children\n            })\n        })\n    });\n};\n_s2(NavigationMenuProvider, \"IKMIdJ3YcKsQ87et93JAbsiHnU8=\", false, function() {\n    return [\n        _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_9__.usePrevious,\n        _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__.useId,\n        _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef,\n        _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef,\n        _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef,\n        _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef,\n        _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef,\n        _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef\n    ];\n});\n_c4 = NavigationMenuProvider;\nvar LIST_NAME = \"NavigationMenuList\";\nvar NavigationMenuList = /*#__PURE__*/ _s3(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c5 = _s3((props, forwardedRef)=>{\n    _s3();\n    const { __scopeNavigationMenu, ...listProps } = props;\n    const context = useNavigationMenuContext(LIST_NAME, __scopeNavigationMenu);\n    const list = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.ul, {\n        \"data-orientation\": context.orientation,\n        ...listProps,\n        ref: forwardedRef\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.div, {\n        style: {\n            position: \"relative\"\n        },\n        ref: context.onIndicatorTrackChange,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n            scope: __scopeNavigationMenu,\n            children: context.isRootMenu ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusGroup, {\n                asChild: true,\n                children: list\n            }) : list\n        })\n    });\n}, \"6TYfq3Pa5jXZcTkLLpsU9OCn7mc=\", false, function() {\n    return [\n        useNavigationMenuContext\n    ];\n})), \"6TYfq3Pa5jXZcTkLLpsU9OCn7mc=\", false, function() {\n    return [\n        useNavigationMenuContext\n    ];\n});\n_c6 = NavigationMenuList;\nNavigationMenuList.displayName = LIST_NAME;\nvar ITEM_NAME = \"NavigationMenuItem\";\nvar [NavigationMenuItemContextProvider, useNavigationMenuItemContext] = createNavigationMenuContext(ITEM_NAME);\nvar NavigationMenuItem = /*#__PURE__*/ _s4(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c7 = _s4((props, forwardedRef)=>{\n    _s4();\n    const { __scopeNavigationMenu, value: valueProp, ...itemProps } = props;\n    const autoValue = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__.useId)();\n    const value = valueProp || autoValue || \"LEGACY_REACT_AUTO_VALUE\";\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const triggerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const focusProxyRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const restoreContentTabOrderRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        \"NavigationMenuItem.useRef[restoreContentTabOrderRef]\": ()=>{}\n    }[\"NavigationMenuItem.useRef[restoreContentTabOrderRef]\"]);\n    const wasEscapeCloseRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleContentEntry = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"NavigationMenuItem.useCallback[handleContentEntry]\": function() {\n            let side = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"start\";\n            if (contentRef.current) {\n                restoreContentTabOrderRef.current();\n                const candidates = getTabbableCandidates(contentRef.current);\n                if (candidates.length) focusFirst(side === \"start\" ? candidates : candidates.reverse());\n            }\n        }\n    }[\"NavigationMenuItem.useCallback[handleContentEntry]\"], []);\n    const handleContentExit = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"NavigationMenuItem.useCallback[handleContentExit]\": ()=>{\n            if (contentRef.current) {\n                const candidates = getTabbableCandidates(contentRef.current);\n                if (candidates.length) restoreContentTabOrderRef.current = removeFromTabOrder(candidates);\n            }\n        }\n    }[\"NavigationMenuItem.useCallback[handleContentExit]\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuItemContextProvider, {\n        scope: __scopeNavigationMenu,\n        value,\n        triggerRef,\n        contentRef,\n        focusProxyRef,\n        wasEscapeCloseRef,\n        onEntryKeyDown: handleContentEntry,\n        onFocusProxyEnter: handleContentEntry,\n        onRootContentClose: handleContentExit,\n        onContentFocusOutside: handleContentExit,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.li, {\n            ...itemProps,\n            ref: forwardedRef\n        })\n    });\n}, \"yYwRRNzlHnUEYc2x3Y1xYRGgJ4c=\", false, function() {\n    return [\n        _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__.useId\n    ];\n})), \"yYwRRNzlHnUEYc2x3Y1xYRGgJ4c=\", false, function() {\n    return [\n        _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__.useId\n    ];\n});\n_c8 = NavigationMenuItem;\nNavigationMenuItem.displayName = ITEM_NAME;\nvar TRIGGER_NAME = \"NavigationMenuTrigger\";\nvar NavigationMenuTrigger = /*#__PURE__*/ _s5(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c9 = _s5((props, forwardedRef)=>{\n    _s5();\n    const { __scopeNavigationMenu, disabled, ...triggerProps } = props;\n    const context = useNavigationMenuContext(TRIGGER_NAME, props.__scopeNavigationMenu);\n    const itemContext = useNavigationMenuItemContext(TRIGGER_NAME, props.__scopeNavigationMenu);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(ref, itemContext.triggerRef, forwardedRef);\n    const triggerId = makeTriggerId(context.baseId, itemContext.value);\n    const contentId = makeContentId(context.baseId, itemContext.value);\n    const hasPointerMoveOpenedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const wasClickCloseRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const open = itemContext.value === context.value;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.ItemSlot, {\n                scope: __scopeNavigationMenu,\n                value: itemContext.value,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusGroupItem, {\n                    asChild: true,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.button, {\n                        id: triggerId,\n                        disabled,\n                        \"data-disabled\": disabled ? \"\" : void 0,\n                        \"data-state\": getOpenState(open),\n                        \"aria-expanded\": open,\n                        \"aria-controls\": contentId,\n                        ...triggerProps,\n                        ref: composedRefs,\n                        onPointerEnter: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerEnter, ()=>{\n                            wasClickCloseRef.current = false;\n                            itemContext.wasEscapeCloseRef.current = false;\n                        }),\n                        onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, whenMouse(()=>{\n                            if (disabled || wasClickCloseRef.current || itemContext.wasEscapeCloseRef.current || hasPointerMoveOpenedRef.current) return;\n                            context.onTriggerEnter(itemContext.value);\n                            hasPointerMoveOpenedRef.current = true;\n                        })),\n                        onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerLeave, whenMouse(()=>{\n                            if (disabled) return;\n                            context.onTriggerLeave();\n                            hasPointerMoveOpenedRef.current = false;\n                        })),\n                        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onClick, ()=>{\n                            context.onItemSelect(itemContext.value);\n                            wasClickCloseRef.current = open;\n                        }),\n                        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                            const verticalEntryKey = context.dir === \"rtl\" ? \"ArrowLeft\" : \"ArrowRight\";\n                            const entryKey = {\n                                horizontal: \"ArrowDown\",\n                                vertical: verticalEntryKey\n                            }[context.orientation];\n                            if (open && event.key === entryKey) {\n                                itemContext.onEntryKeyDown();\n                                event.preventDefault();\n                            }\n                        })\n                    })\n                })\n            }),\n            open && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n                children: [\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                        \"aria-hidden\": true,\n                        tabIndex: 0,\n                        ref: itemContext.focusProxyRef,\n                        onFocus: (event)=>{\n                            const content = itemContext.contentRef.current;\n                            const prevFocusedElement = event.relatedTarget;\n                            const wasTriggerFocused = prevFocusedElement === ref.current;\n                            const wasFocusFromContent = content === null || content === void 0 ? void 0 : content.contains(prevFocusedElement);\n                            if (wasTriggerFocused || !wasFocusFromContent) {\n                                itemContext.onFocusProxyEnter(wasTriggerFocused ? \"start\" : \"end\");\n                            }\n                        }\n                    }),\n                    context.viewport && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"span\", {\n                        \"aria-owns\": contentId\n                    })\n                ]\n            })\n        ]\n    });\n}, \"M82V3DW50+M1k73Dl1yDCygICgg=\", false, function() {\n    return [\n        useNavigationMenuContext,\n        useNavigationMenuItemContext,\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs\n    ];\n})), \"M82V3DW50+M1k73Dl1yDCygICgg=\", false, function() {\n    return [\n        useNavigationMenuContext,\n        useNavigationMenuItemContext,\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs\n    ];\n});\n_c10 = NavigationMenuTrigger;\nNavigationMenuTrigger.displayName = TRIGGER_NAME;\nvar LINK_NAME = \"NavigationMenuLink\";\nvar LINK_SELECT = \"navigationMenu.linkSelect\";\nvar NavigationMenuLink = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c11 = (props, forwardedRef)=>{\n    const { __scopeNavigationMenu, active, onSelect, ...linkProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusGroupItem, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.a, {\n            \"data-active\": active ? \"\" : void 0,\n            \"aria-current\": active ? \"page\" : void 0,\n            ...linkProps,\n            ref: forwardedRef,\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onClick, (event)=>{\n                const target = event.target;\n                const linkSelectEvent = new CustomEvent(LINK_SELECT, {\n                    bubbles: true,\n                    cancelable: true\n                });\n                target.addEventListener(LINK_SELECT, (event2)=>onSelect === null || onSelect === void 0 ? void 0 : onSelect(event2), {\n                    once: true\n                });\n                (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.dispatchDiscreteCustomEvent)(target, linkSelectEvent);\n                if (!linkSelectEvent.defaultPrevented && !event.metaKey) {\n                    const rootContentDismissEvent = new CustomEvent(ROOT_CONTENT_DISMISS, {\n                        bubbles: true,\n                        cancelable: true\n                    });\n                    (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.dispatchDiscreteCustomEvent)(target, rootContentDismissEvent);\n                }\n            }, {\n                checkForDefaultPrevented: false\n            })\n        })\n    });\n});\n_c12 = NavigationMenuLink;\nNavigationMenuLink.displayName = LINK_NAME;\nvar INDICATOR_NAME = \"NavigationMenuIndicator\";\nvar NavigationMenuIndicator = /*#__PURE__*/ _s6(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c13 = _s6((props, forwardedRef)=>{\n    _s6();\n    const { forceMount, ...indicatorProps } = props;\n    const context = useNavigationMenuContext(INDICATOR_NAME, props.__scopeNavigationMenu);\n    const isVisible = Boolean(context.value);\n    return context.indicatorTrack ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_14__.Presence, {\n        present: forceMount || isVisible,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuIndicatorImpl, {\n            ...indicatorProps,\n            ref: forwardedRef\n        })\n    }), context.indicatorTrack) : null;\n}, \"6TYfq3Pa5jXZcTkLLpsU9OCn7mc=\", false, function() {\n    return [\n        useNavigationMenuContext\n    ];\n})), \"6TYfq3Pa5jXZcTkLLpsU9OCn7mc=\", false, function() {\n    return [\n        useNavigationMenuContext\n    ];\n});\n_c14 = NavigationMenuIndicator;\nNavigationMenuIndicator.displayName = INDICATOR_NAME;\nvar NavigationMenuIndicatorImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_s7((props, forwardedRef)=>{\n    _s7();\n    const { __scopeNavigationMenu, ...indicatorProps } = props;\n    const context = useNavigationMenuContext(INDICATOR_NAME, __scopeNavigationMenu);\n    const getItems = useCollection(__scopeNavigationMenu);\n    const [activeTrigger, setActiveTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [position, setPosition] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const isHorizontal = context.orientation === \"horizontal\";\n    const isVisible = Boolean(context.value);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"NavigationMenuIndicatorImpl.useEffect\": ()=>{\n            var _items_find;\n            const items = getItems();\n            const triggerNode = (_items_find = items.find({\n                \"NavigationMenuIndicatorImpl.useEffect\": (item)=>item.value === context.value\n            }[\"NavigationMenuIndicatorImpl.useEffect\"])) === null || _items_find === void 0 ? void 0 : _items_find.ref.current;\n            if (triggerNode) setActiveTrigger(triggerNode);\n        }\n    }[\"NavigationMenuIndicatorImpl.useEffect\"], [\n        getItems,\n        context.value\n    ]);\n    const handlePositionChange = ()=>{\n        if (activeTrigger) {\n            setPosition({\n                size: isHorizontal ? activeTrigger.offsetWidth : activeTrigger.offsetHeight,\n                offset: isHorizontal ? activeTrigger.offsetLeft : activeTrigger.offsetTop\n            });\n        }\n    };\n    useResizeObserver(activeTrigger, handlePositionChange);\n    useResizeObserver(context.indicatorTrack, handlePositionChange);\n    return position ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.div, {\n        \"aria-hidden\": true,\n        \"data-state\": isVisible ? \"visible\" : \"hidden\",\n        \"data-orientation\": context.orientation,\n        ...indicatorProps,\n        ref: forwardedRef,\n        style: {\n            position: \"absolute\",\n            ...isHorizontal ? {\n                left: 0,\n                width: position.size + \"px\",\n                transform: \"translateX(\".concat(position.offset, \"px)\")\n            } : {\n                top: 0,\n                height: position.size + \"px\",\n                transform: \"translateY(\".concat(position.offset, \"px)\")\n            },\n            ...indicatorProps.style\n        }\n    }) : null;\n}, \"vYub289AmTwCgoPULyU7bZuG6J0=\", false, function() {\n    return [\n        useNavigationMenuContext,\n        useCollection,\n        useResizeObserver,\n        useResizeObserver\n    ];\n}));\n_c15 = NavigationMenuIndicatorImpl;\nvar CONTENT_NAME = \"NavigationMenuContent\";\nvar NavigationMenuContent = /*#__PURE__*/ _s8(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c16 = _s8((props, forwardedRef)=>{\n    _s8();\n    const { forceMount, ...contentProps } = props;\n    const context = useNavigationMenuContext(CONTENT_NAME, props.__scopeNavigationMenu);\n    const itemContext = useNavigationMenuItemContext(CONTENT_NAME, props.__scopeNavigationMenu);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(itemContext.contentRef, forwardedRef);\n    const open = itemContext.value === context.value;\n    const commonProps = {\n        value: itemContext.value,\n        triggerRef: itemContext.triggerRef,\n        focusProxyRef: itemContext.focusProxyRef,\n        wasEscapeCloseRef: itemContext.wasEscapeCloseRef,\n        onContentFocusOutside: itemContext.onContentFocusOutside,\n        onRootContentClose: itemContext.onRootContentClose,\n        ...contentProps\n    };\n    return !context.viewport ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_14__.Presence, {\n        present: forceMount || open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuContentImpl, {\n            \"data-state\": getOpenState(open),\n            ...commonProps,\n            ref: composedRefs,\n            onPointerEnter: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerEnter, context.onContentEnter),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerLeave, whenMouse(context.onContentLeave)),\n            style: {\n                // Prevent interaction when animating out\n                pointerEvents: !open && context.isRootMenu ? \"none\" : void 0,\n                ...commonProps.style\n            }\n        })\n    }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ViewportContentMounter, {\n        forceMount,\n        ...commonProps,\n        ref: composedRefs\n    });\n}, \"DdLqUR/Htxu6sglwyW2pnSqYaMw=\", false, function() {\n    return [\n        useNavigationMenuContext,\n        useNavigationMenuItemContext,\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs\n    ];\n})), \"DdLqUR/Htxu6sglwyW2pnSqYaMw=\", false, function() {\n    return [\n        useNavigationMenuContext,\n        useNavigationMenuItemContext,\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs\n    ];\n});\n_c17 = NavigationMenuContent;\nNavigationMenuContent.displayName = CONTENT_NAME;\nvar ViewportContentMounter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_s9((props, forwardedRef)=>{\n    _s9();\n    const context = useNavigationMenuContext(CONTENT_NAME, props.__scopeNavigationMenu);\n    const { onViewportContentChange, onViewportContentRemove } = context;\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_15__.useLayoutEffect)({\n        \"ViewportContentMounter.useLayoutEffect\": ()=>{\n            onViewportContentChange(props.value, {\n                ref: forwardedRef,\n                ...props\n            });\n        }\n    }[\"ViewportContentMounter.useLayoutEffect\"], [\n        props,\n        forwardedRef,\n        onViewportContentChange\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_15__.useLayoutEffect)({\n        \"ViewportContentMounter.useLayoutEffect\": ()=>{\n            return ({\n                \"ViewportContentMounter.useLayoutEffect\": ()=>onViewportContentRemove(props.value)\n            })[\"ViewportContentMounter.useLayoutEffect\"];\n        }\n    }[\"ViewportContentMounter.useLayoutEffect\"], [\n        props.value,\n        onViewportContentRemove\n    ]);\n    return null;\n}, \"b1Sde6RgzIoz0mSNnjS0GdxsLd0=\", false, function() {\n    return [\n        useNavigationMenuContext\n    ];\n}));\n_c18 = ViewportContentMounter;\nvar ROOT_CONTENT_DISMISS = \"navigationMenu.rootContentDismiss\";\nvar NavigationMenuContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_s10((props, forwardedRef)=>{\n    _s10();\n    const { __scopeNavigationMenu, value, triggerRef, focusProxyRef, wasEscapeCloseRef, onRootContentClose, onContentFocusOutside, ...contentProps } = props;\n    const context = useNavigationMenuContext(CONTENT_NAME, __scopeNavigationMenu);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(ref, forwardedRef);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const getItems = useCollection(__scopeNavigationMenu);\n    const prevMotionAttributeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { onItemDismiss } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"NavigationMenuContentImpl.useEffect\": ()=>{\n            const content = ref.current;\n            if (context.isRootMenu && content) {\n                const handleClose = {\n                    \"NavigationMenuContentImpl.useEffect.handleClose\": ()=>{\n                        var _triggerRef_current;\n                        onItemDismiss();\n                        onRootContentClose();\n                        if (content.contains(document.activeElement)) (_triggerRef_current = triggerRef.current) === null || _triggerRef_current === void 0 ? void 0 : _triggerRef_current.focus();\n                    }\n                }[\"NavigationMenuContentImpl.useEffect.handleClose\"];\n                content.addEventListener(ROOT_CONTENT_DISMISS, handleClose);\n                return ({\n                    \"NavigationMenuContentImpl.useEffect\": ()=>content.removeEventListener(ROOT_CONTENT_DISMISS, handleClose)\n                })[\"NavigationMenuContentImpl.useEffect\"];\n            }\n        }\n    }[\"NavigationMenuContentImpl.useEffect\"], [\n        context.isRootMenu,\n        props.value,\n        triggerRef,\n        onItemDismiss,\n        onRootContentClose\n    ]);\n    const motionAttribute = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"NavigationMenuContentImpl.useMemo[motionAttribute]\": ()=>{\n            const items = getItems();\n            const values = items.map({\n                \"NavigationMenuContentImpl.useMemo[motionAttribute].values\": (item)=>item.value\n            }[\"NavigationMenuContentImpl.useMemo[motionAttribute].values\"]);\n            if (context.dir === \"rtl\") values.reverse();\n            const index = values.indexOf(context.value);\n            const prevIndex = values.indexOf(context.previousValue);\n            const isSelected = value === context.value;\n            const wasSelected = prevIndex === values.indexOf(value);\n            if (!isSelected && !wasSelected) return prevMotionAttributeRef.current;\n            const attribute = ({\n                \"NavigationMenuContentImpl.useMemo[motionAttribute].attribute\": ()=>{\n                    if (index !== prevIndex) {\n                        if (isSelected && prevIndex !== -1) return index > prevIndex ? \"from-end\" : \"from-start\";\n                        if (wasSelected && index !== -1) return index > prevIndex ? \"to-start\" : \"to-end\";\n                    }\n                    return null;\n                }\n            })[\"NavigationMenuContentImpl.useMemo[motionAttribute].attribute\"]();\n            prevMotionAttributeRef.current = attribute;\n            return attribute;\n        }\n    }[\"NavigationMenuContentImpl.useMemo[motionAttribute]\"], [\n        context.previousValue,\n        context.value,\n        context.dir,\n        getItems,\n        value\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusGroup, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_16__.DismissableLayer, {\n            id: contentId,\n            \"aria-labelledby\": triggerId,\n            \"data-motion\": motionAttribute,\n            \"data-orientation\": context.orientation,\n            ...contentProps,\n            ref: composedRefs,\n            disableOutsidePointerEvents: false,\n            onDismiss: ()=>{\n                var _ref_current;\n                const rootContentDismissEvent = new Event(ROOT_CONTENT_DISMISS, {\n                    bubbles: true,\n                    cancelable: true\n                });\n                (_ref_current = ref.current) === null || _ref_current === void 0 ? void 0 : _ref_current.dispatchEvent(rootContentDismissEvent);\n            },\n            onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onFocusOutside, (event)=>{\n                var _context_rootNavigationMenu;\n                onContentFocusOutside();\n                const target = event.target;\n                if ((_context_rootNavigationMenu = context.rootNavigationMenu) === null || _context_rootNavigationMenu === void 0 ? void 0 : _context_rootNavigationMenu.contains(target)) event.preventDefault();\n            }),\n            onPointerDownOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerDownOutside, (event)=>{\n                var _context_viewport;\n                const target = event.target;\n                const isTrigger = getItems().some((item)=>{\n                    var _item_ref_current;\n                    return (_item_ref_current = item.ref.current) === null || _item_ref_current === void 0 ? void 0 : _item_ref_current.contains(target);\n                });\n                const isRootViewport = context.isRootMenu && ((_context_viewport = context.viewport) === null || _context_viewport === void 0 ? void 0 : _context_viewport.contains(target));\n                if (isTrigger || isRootViewport || !context.isRootMenu) event.preventDefault();\n            }),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                const isMetaKey = event.altKey || event.ctrlKey || event.metaKey;\n                const isTabKey = event.key === \"Tab\" && !isMetaKey;\n                if (isTabKey) {\n                    const candidates = getTabbableCandidates(event.currentTarget);\n                    const focusedElement = document.activeElement;\n                    const index = candidates.findIndex((candidate)=>candidate === focusedElement);\n                    const isMovingBackwards = event.shiftKey;\n                    const nextCandidates = isMovingBackwards ? candidates.slice(0, index).reverse() : candidates.slice(index + 1, candidates.length);\n                    if (focusFirst(nextCandidates)) {\n                        event.preventDefault();\n                    } else {\n                        var _focusProxyRef_current;\n                        (_focusProxyRef_current = focusProxyRef.current) === null || _focusProxyRef_current === void 0 ? void 0 : _focusProxyRef_current.focus();\n                    }\n                }\n            }),\n            onEscapeKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onEscapeKeyDown, (_event)=>{\n                wasEscapeCloseRef.current = true;\n            })\n        })\n    });\n}, \"cytg5GAep6ozPlBxzt+s3BMSV5I=\", false, function() {\n    return [\n        useNavigationMenuContext,\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs,\n        useCollection\n    ];\n}));\n_c19 = NavigationMenuContentImpl;\nvar VIEWPORT_NAME = \"NavigationMenuViewport\";\nvar NavigationMenuViewport = /*#__PURE__*/ _s11(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c20 = _s11((props, forwardedRef)=>{\n    _s11();\n    const { forceMount, ...viewportProps } = props;\n    const context = useNavigationMenuContext(VIEWPORT_NAME, props.__scopeNavigationMenu);\n    const open = Boolean(context.value);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_14__.Presence, {\n        present: forceMount || open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuViewportImpl, {\n            ...viewportProps,\n            ref: forwardedRef\n        })\n    });\n}, \"6TYfq3Pa5jXZcTkLLpsU9OCn7mc=\", false, function() {\n    return [\n        useNavigationMenuContext\n    ];\n})), \"6TYfq3Pa5jXZcTkLLpsU9OCn7mc=\", false, function() {\n    return [\n        useNavigationMenuContext\n    ];\n});\n_c21 = NavigationMenuViewport;\nNavigationMenuViewport.displayName = VIEWPORT_NAME;\nvar NavigationMenuViewportImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_s12((props, forwardedRef)=>{\n    _s12();\n    const { __scopeNavigationMenu, children, ...viewportImplProps } = props;\n    const context = useNavigationMenuContext(VIEWPORT_NAME, __scopeNavigationMenu);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, context.onViewportChange);\n    const viewportContentContext = useViewportContentContext(CONTENT_NAME, props.__scopeNavigationMenu);\n    const [size, setSize] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const viewportWidth = size ? (size === null || size === void 0 ? void 0 : size.width) + \"px\" : void 0;\n    const viewportHeight = size ? (size === null || size === void 0 ? void 0 : size.height) + \"px\" : void 0;\n    const open = Boolean(context.value);\n    const activeContentValue = open ? context.value : context.previousValue;\n    const handleSizeChange = ()=>{\n        if (content) setSize({\n            width: content.offsetWidth,\n            height: content.offsetHeight\n        });\n    };\n    useResizeObserver(content, handleSizeChange);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.div, {\n        \"data-state\": getOpenState(open),\n        \"data-orientation\": context.orientation,\n        ...viewportImplProps,\n        ref: composedRefs,\n        style: {\n            // Prevent interaction when animating out\n            pointerEvents: !open && context.isRootMenu ? \"none\" : void 0,\n            [\"--radix-navigation-menu-viewport-width\"]: viewportWidth,\n            [\"--radix-navigation-menu-viewport-height\"]: viewportHeight,\n            ...viewportImplProps.style\n        },\n        onPointerEnter: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerEnter, context.onContentEnter),\n        onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerLeave, whenMouse(context.onContentLeave)),\n        children: Array.from(viewportContentContext.items).map((param)=>{\n            let [value, { ref, forceMount, ...props2 }] = param;\n            const isActive = activeContentValue === value;\n            return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_14__.Presence, {\n                present: forceMount || isActive,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuContentImpl, {\n                    ...props2,\n                    ref: (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.composeRefs)(ref, (node)=>{\n                        if (isActive && node) setContent(node);\n                    })\n                })\n            }, value);\n        })\n    });\n}, \"G7RD3vqlYDEDRowHDd5h/lASyXU=\", false, function() {\n    return [\n        useNavigationMenuContext,\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs,\n        useViewportContentContext,\n        useResizeObserver\n    ];\n}));\n_c22 = NavigationMenuViewportImpl;\nvar FOCUS_GROUP_NAME = \"FocusGroup\";\nvar FocusGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_s13((props, forwardedRef)=>{\n    _s13();\n    const { __scopeNavigationMenu, ...groupProps } = props;\n    const context = useNavigationMenuContext(FOCUS_GROUP_NAME, __scopeNavigationMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusGroupCollection.Provider, {\n        scope: __scopeNavigationMenu,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusGroupCollection.Slot, {\n            scope: __scopeNavigationMenu,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.div, {\n                dir: context.dir,\n                ...groupProps,\n                ref: forwardedRef\n            })\n        })\n    });\n}, \"6TYfq3Pa5jXZcTkLLpsU9OCn7mc=\", false, function() {\n    return [\n        useNavigationMenuContext\n    ];\n}));\n_c23 = FocusGroup;\nvar ARROW_KEYS = [\n    \"ArrowRight\",\n    \"ArrowLeft\",\n    \"ArrowUp\",\n    \"ArrowDown\"\n];\nvar FOCUS_GROUP_ITEM_NAME = \"FocusGroupItem\";\nvar FocusGroupItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_s14((props, forwardedRef)=>{\n    _s14();\n    const { __scopeNavigationMenu, ...groupProps } = props;\n    const getItems = useFocusGroupCollection(__scopeNavigationMenu);\n    const context = useNavigationMenuContext(FOCUS_GROUP_ITEM_NAME, __scopeNavigationMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusGroupCollection.ItemSlot, {\n        scope: __scopeNavigationMenu,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.button, {\n            ...groupProps,\n            ref: forwardedRef,\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                const isFocusNavigationKey = [\n                    \"Home\",\n                    \"End\",\n                    ...ARROW_KEYS\n                ].includes(event.key);\n                if (isFocusNavigationKey) {\n                    let candidateNodes = getItems().map((item)=>item.ref.current);\n                    const prevItemKey = context.dir === \"rtl\" ? \"ArrowRight\" : \"ArrowLeft\";\n                    const prevKeys = [\n                        prevItemKey,\n                        \"ArrowUp\",\n                        \"End\"\n                    ];\n                    if (prevKeys.includes(event.key)) candidateNodes.reverse();\n                    if (ARROW_KEYS.includes(event.key)) {\n                        const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                        candidateNodes = candidateNodes.slice(currentIndex + 1);\n                    }\n                    setTimeout(()=>focusFirst(candidateNodes));\n                    event.preventDefault();\n                }\n            })\n        })\n    });\n}, \"3bJZe4nmYWpR70N2usGKJ2WbMTY=\", false, function() {\n    return [\n        useFocusGroupCollection,\n        useNavigationMenuContext\n    ];\n}));\n_c24 = FocusGroupItem;\nfunction getTabbableCandidates(container) {\n    const nodes = [];\n    const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n        acceptNode: (node)=>{\n            const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n            if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n            return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n        }\n    });\n    while(walker.nextNode())nodes.push(walker.currentNode);\n    return nodes;\n}\nfunction focusFirst(candidates) {\n    const previouslyFocusedElement = document.activeElement;\n    return candidates.some((candidate)=>{\n        if (candidate === previouslyFocusedElement) return true;\n        candidate.focus();\n        return document.activeElement !== previouslyFocusedElement;\n    });\n}\nfunction removeFromTabOrder(candidates) {\n    candidates.forEach((candidate)=>{\n        candidate.dataset.tabindex = candidate.getAttribute(\"tabindex\") || \"\";\n        candidate.setAttribute(\"tabindex\", \"-1\");\n    });\n    return ()=>{\n        candidates.forEach((candidate)=>{\n            const prevTabIndex = candidate.dataset.tabindex;\n            candidate.setAttribute(\"tabindex\", prevTabIndex);\n        });\n    };\n}\nfunction useResizeObserver(element, onResize) {\n    _s15();\n    const handleResize = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(onResize);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_15__.useLayoutEffect)({\n        \"useResizeObserver.useLayoutEffect\": ()=>{\n            let rAF = 0;\n            if (element) {\n                const resizeObserver = new ResizeObserver({\n                    \"useResizeObserver.useLayoutEffect\": ()=>{\n                        cancelAnimationFrame(rAF);\n                        rAF = window.requestAnimationFrame(handleResize);\n                    }\n                }[\"useResizeObserver.useLayoutEffect\"]);\n                resizeObserver.observe(element);\n                return ({\n                    \"useResizeObserver.useLayoutEffect\": ()=>{\n                        window.cancelAnimationFrame(rAF);\n                        resizeObserver.unobserve(element);\n                    }\n                })[\"useResizeObserver.useLayoutEffect\"];\n            }\n        }\n    }[\"useResizeObserver.useLayoutEffect\"], [\n        element,\n        handleResize\n    ]);\n}\n_s15(useResizeObserver, \"/Ap8uEYN0y92xlnU4jqBFTKYw/I=\", false, function() {\n    return [\n        _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef\n    ];\n});\nfunction getOpenState(open) {\n    return open ? \"open\" : \"closed\";\n}\nfunction makeTriggerId(baseId, value) {\n    return \"\".concat(baseId, \"-trigger-\").concat(value);\n}\nfunction makeContentId(baseId, value) {\n    return \"\".concat(baseId, \"-content-\").concat(value);\n}\nfunction whenMouse(handler) {\n    return (event)=>event.pointerType === \"mouse\" ? handler(event) : void 0;\n}\nvar Root2 = NavigationMenu;\nvar Sub = NavigationMenuSub;\nvar List = NavigationMenuList;\nvar Item = NavigationMenuItem;\nvar Trigger = NavigationMenuTrigger;\nvar Link = NavigationMenuLink;\nvar Indicator = NavigationMenuIndicator;\nvar Content = NavigationMenuContent;\nvar Viewport = NavigationMenuViewport;\n //# sourceMappingURL=index.mjs.map\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24;\n$RefreshReg$(_c, \"NavigationMenu$React.forwardRef\");\n$RefreshReg$(_c1, \"NavigationMenu\");\n$RefreshReg$(_c2, \"NavigationMenuSub$React.forwardRef\");\n$RefreshReg$(_c3, \"NavigationMenuSub\");\n$RefreshReg$(_c4, \"NavigationMenuProvider\");\n$RefreshReg$(_c5, \"NavigationMenuList$React.forwardRef\");\n$RefreshReg$(_c6, \"NavigationMenuList\");\n$RefreshReg$(_c7, \"NavigationMenuItem$React.forwardRef\");\n$RefreshReg$(_c8, \"NavigationMenuItem\");\n$RefreshReg$(_c9, \"NavigationMenuTrigger$React.forwardRef\");\n$RefreshReg$(_c10, \"NavigationMenuTrigger\");\n$RefreshReg$(_c11, \"NavigationMenuLink$React.forwardRef\");\n$RefreshReg$(_c12, \"NavigationMenuLink\");\n$RefreshReg$(_c13, \"NavigationMenuIndicator$React.forwardRef\");\n$RefreshReg$(_c14, \"NavigationMenuIndicator\");\n$RefreshReg$(_c15, \"NavigationMenuIndicatorImpl\");\n$RefreshReg$(_c16, \"NavigationMenuContent$React.forwardRef\");\n$RefreshReg$(_c17, \"NavigationMenuContent\");\n$RefreshReg$(_c18, \"ViewportContentMounter\");\n$RefreshReg$(_c19, \"NavigationMenuContentImpl\");\n$RefreshReg$(_c20, \"NavigationMenuViewport$React.forwardRef\");\n$RefreshReg$(_c21, \"NavigationMenuViewport\");\n$RefreshReg$(_c22, \"NavigationMenuViewportImpl\");\n$RefreshReg$(_c23, \"FocusGroup\");\n$RefreshReg$(_c24, \"FocusGroupItem\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-navigation-menu/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/menu.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Menu)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Menu = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Menu\", [\n    [\n        \"line\",\n        {\n            x1: \"4\",\n            x2: \"20\",\n            y1: \"12\",\n            y2: \"12\",\n            key: \"1e0a9i\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"4\",\n            x2: \"20\",\n            y1: \"6\",\n            y2: \"6\",\n            key: \"1owob3\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"4\",\n            x2: \"20\",\n            y1: \"18\",\n            y2: \"18\",\n            key: \"yk5zj1\"\n        }\n    ]\n]);\n //# sourceMappingURL=menu.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2FQuoteModal.tsx%22%2C%22ids%22%3A%5B%22QuoteModal%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fscreens%2FHomeDesktop%2Fsections%2FContactInfoSection%2FContactInfoSection.tsx%22%2C%22ids%22%3A%5B%22ContactInfoSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fscreens%2FHomeDesktop%2Fsections%2FNavigationBarSection%2FNavigationBarSection.tsx%22%2C%22ids%22%3A%5B%22NavigationBarSection%22%5D%7D&server=false!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2FQuoteModal.tsx%22%2C%22ids%22%3A%5B%22QuoteModal%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fscreens%2FHomeDesktop%2Fsections%2FContactInfoSection%2FContactInfoSection.tsx%22%2C%22ids%22%3A%5B%22ContactInfoSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fscreens%2FHomeDesktop%2Fsections%2FNavigationBarSection%2FNavigationBarSection.tsx%22%2C%22ids%22%3A%5B%22NavigationBarSection%22%5D%7D&server=false! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(app-pages-browser)/./node_modules/next/dist/client/image-component.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/QuoteModal.tsx */ \"(app-pages-browser)/./src/components/QuoteModal.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx */ \"(app-pages-browser)/./src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx */ \"(app-pages-browser)/./src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2FQuoteModal.tsx%22%2C%22ids%22%3A%5B%22QuoteModal%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fscreens%2FHomeDesktop%2Fsections%2FContactInfoSection%2FContactInfoSection.tsx%22%2C%22ids%22%3A%5B%22ContactInfoSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fscreens%2FHomeDesktop%2Fsections%2FNavigationBarSection%2FNavigationBarSection.tsx%22%2C%22ids%22%3A%5B%22NavigationBarSection%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/client/app-dir/link.js ***!
  \*******************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    /**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */ default: function() {\n        return LinkComponent;\n    },\n    useLinkStatus: function() {\n        return useLinkStatus;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _formaturl = __webpack_require__(/*! ../../shared/lib/router/utils/format-url */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _routerreducertypes = __webpack_require__(/*! ../components/router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _usemergedref = __webpack_require__(/*! ../use-merged-ref */ \"(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js\");\nconst _utils = __webpack_require__(/*! ../../shared/lib/utils */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _warnonce = __webpack_require__(/*! ../../shared/lib/utils/warn-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nconst _links = __webpack_require__(/*! ../components/links */ \"(app-pages-browser)/./node_modules/next/dist/client/components/links.js\");\nconst _islocalurl = __webpack_require__(/*! ../../shared/lib/router/utils/is-local-url */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _approuterinstance = __webpack_require__(/*! ../components/app-router-instance */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\");\nconst _erroronce = __webpack_require__(/*! ../../shared/lib/utils/error-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js\");\nfunction isModifiedEvent(event) {\n    const eventTarget = event.currentTarget;\n    const target = eventTarget.getAttribute('target');\n    return target && target !== '_self' || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download\n    event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate) {\n    const { nodeName } = e.currentTarget;\n    // anchors inside an svg have a lowercase nodeName\n    const isAnchorNodeName = nodeName.toUpperCase() === 'A';\n    if (isAnchorNodeName && isModifiedEvent(e) || e.currentTarget.hasAttribute('download')) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    if (!(0, _islocalurl.isLocalURL)(href)) {\n        if (replace) {\n            // browser default behavior does not replace the history state\n            // so we need to do it manually\n            e.preventDefault();\n            location.replace(href);\n        }\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    const navigate = ()=>{\n        if (onNavigate) {\n            let isDefaultPrevented = false;\n            onNavigate({\n                preventDefault: ()=>{\n                    isDefaultPrevented = true;\n                }\n            });\n            if (isDefaultPrevented) {\n                return;\n            }\n        }\n        (0, _approuterinstance.dispatchNavigateAction)(as || href, replace ? 'replace' : 'push', scroll != null ? scroll : true, linkInstanceRef.current);\n    };\n    _react.default.startTransition(navigate);\n}\nfunction formatStringOrUrl(urlObjOrString) {\n    if (typeof urlObjOrString === 'string') {\n        return urlObjOrString;\n    }\n    return (0, _formaturl.formatUrl)(urlObjOrString);\n}\nfunction LinkComponent(props) {\n    _s();\n    const [linkStatus, setOptimisticLinkStatus] = (0, _react.useOptimistic)(_links.IDLE_LINK_STATUS);\n    let children;\n    const linkInstanceRef = (0, _react.useRef)(null);\n    const { href: hrefProp, as: asProp, children: childrenProp, prefetch: prefetchProp = null, passHref, replace, shallow, scroll, onClick, onMouseEnter: onMouseEnterProp, onTouchStart: onTouchStartProp, legacyBehavior = false, onNavigate, ref: forwardedRef, unstable_dynamicOnHover, ...restProps } = props;\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === 'string' || typeof children === 'number')) {\n        children = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            children: children\n        });\n    }\n    const router = _react.default.useContext(_approutercontextsharedruntime.AppRouterContext);\n    const prefetchEnabled = prefetchProp !== false;\n    /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */ const appPrefetchKind = prefetchProp === null ? _routerreducertypes.PrefetchKind.AUTO : _routerreducertypes.PrefetchKind.FULL;\n    if (true) {\n        function createPropError(args) {\n            return Object.defineProperty(new Error(\"Failed prop type: The prop `\" + args.key + \"` expects a \" + args.expected + \" in `<Link>`, but got `\" + args.actual + \"` instead.\" + ( true ? \"\\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                value: \"E319\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        // TypeScript trick for type-guarding:\n        const requiredPropsGuard = {\n            href: true\n        };\n        const requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach((key)=>{\n            if (key === 'href') {\n                if (props[key] == null || typeof props[key] !== 'string' && typeof props[key] !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: props[key] === null ? 'null' : typeof props[key]\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        const optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            unstable_dynamicOnHover: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true,\n            onNavigate: true\n        };\n        const optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach((key)=>{\n            const valType = typeof props[key];\n            if (key === 'as') {\n                if (props[key] && valType !== 'string' && valType !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'onClick' || key === 'onMouseEnter' || key === 'onTouchStart' || key === 'onNavigate') {\n                if (props[key] && valType !== 'function') {\n                    throw createPropError({\n                        key,\n                        expected: '`function`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'replace' || key === 'scroll' || key === 'shallow' || key === 'passHref' || key === 'prefetch' || key === 'legacyBehavior' || key === 'unstable_dynamicOnHover') {\n                if (props[key] != null && valType !== 'boolean') {\n                    throw createPropError({\n                        key,\n                        expected: '`boolean`',\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n    }\n    if (true) {\n        if (props.locale) {\n            (0, _warnonce.warnOnce)('The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization');\n        }\n        if (!asProp) {\n            let href;\n            if (typeof hrefProp === 'string') {\n                href = hrefProp;\n            } else if (typeof hrefProp === 'object' && typeof hrefProp.pathname === 'string') {\n                href = hrefProp.pathname;\n            }\n            if (href) {\n                const hasDynamicSegment = href.split('/').some((segment)=>segment.startsWith('[') && segment.endsWith(']'));\n                if (hasDynamicSegment) {\n                    throw Object.defineProperty(new Error(\"Dynamic href `\" + href + \"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E267\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n        }\n    }\n    const { href, as } = _react.default.useMemo({\n        \"LinkComponent.useMemo\": ()=>{\n            const resolvedHref = formatStringOrUrl(hrefProp);\n            return {\n                href: resolvedHref,\n                as: asProp ? formatStringOrUrl(asProp) : resolvedHref\n            };\n        }\n    }[\"LinkComponent.useMemo\"], [\n        hrefProp,\n        asProp\n    ]);\n    // This will return the first child, if multiple are provided it will throw an error\n    let child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link');\n            }\n            if (onMouseEnterProp) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw Object.defineProperty(new Error(\"No children were passed to <Link> with `href` of `\" + hrefProp + \"` but one child is required https://nextjs.org/docs/messages/link-no-children\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E320\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                throw Object.defineProperty(new Error(\"Multiple children were passed to <Link> with `href` of `\" + hrefProp + \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\" + ( true ? \" \\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                    value: \"E266\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        } else {}\n    } else {\n        if (true) {\n            if ((children == null ? void 0 : children.type) === 'a') {\n                throw Object.defineProperty(new Error('Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E209\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n    }\n    const childRef = legacyBehavior ? child && typeof child === 'object' && child.ref : forwardedRef;\n    // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n    // mount. In the future we will also use this to keep track of all the\n    // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n    // a revalidation or refresh.\n    const observeLinkVisibilityOnMount = _react.default.useCallback({\n        \"LinkComponent.useCallback[observeLinkVisibilityOnMount]\": (element)=>{\n            if (router !== null) {\n                linkInstanceRef.current = (0, _links.mountLinkInstance)(element, href, router, appPrefetchKind, prefetchEnabled, setOptimisticLinkStatus);\n            }\n            return ({\n                \"LinkComponent.useCallback[observeLinkVisibilityOnMount]\": ()=>{\n                    if (linkInstanceRef.current) {\n                        (0, _links.unmountLinkForCurrentNavigation)(linkInstanceRef.current);\n                        linkInstanceRef.current = null;\n                    }\n                    (0, _links.unmountPrefetchableInstance)(element);\n                }\n            })[\"LinkComponent.useCallback[observeLinkVisibilityOnMount]\"];\n        }\n    }[\"LinkComponent.useCallback[observeLinkVisibilityOnMount]\"], [\n        prefetchEnabled,\n        href,\n        router,\n        appPrefetchKind,\n        setOptimisticLinkStatus\n    ]);\n    const mergedRef = (0, _usemergedref.useMergedRef)(observeLinkVisibilityOnMount, childRef);\n    const childProps = {\n        ref: mergedRef,\n        onClick (e) {\n            if (true) {\n                if (!e) {\n                    throw Object.defineProperty(new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E312\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n            if (!legacyBehavior && typeof onClick === 'function') {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === 'function') {\n                child.props.onClick(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (e.defaultPrevented) {\n                return;\n            }\n            linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate);\n        },\n        onMouseEnter (e) {\n            if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n                onMouseEnterProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === 'function') {\n                child.props.onMouseEnter(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled || \"development\" === 'development') {\n                return;\n            }\n            const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true;\n            (0, _links.onNavigationIntent)(e.currentTarget, upgradeToDynamicPrefetch);\n        },\n        onTouchStart:  false ? 0 : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n                onTouchStartProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === 'function') {\n                child.props.onTouchStart(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled) {\n                return;\n            }\n            const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true;\n            (0, _links.onNavigationIntent)(e.currentTarget, upgradeToDynamicPrefetch);\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the basePath.\n    if ((0, _utils.isAbsoluteUrl)(as)) {\n        childProps.href = as;\n    } else if (!legacyBehavior || passHref || child.type === 'a' && !('href' in child.props)) {\n        childProps.href = (0, _addbasepath.addBasePath)(as);\n    }\n    let link;\n    if (legacyBehavior) {\n        if (true) {\n            (0, _erroronce.errorOnce)('`legacyBehavior` is deprecated and will be removed in a future ' + 'release. A codemod is available to upgrade your components:\\n\\n' + 'npx @next/codemod@latest new-link .\\n\\n' + 'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components');\n        }\n        link = /*#__PURE__*/ _react.default.cloneElement(child, childProps);\n    } else {\n        link = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            ...restProps,\n            ...childProps,\n            children: children\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(LinkStatusContext.Provider, {\n        value: linkStatus,\n        children: link\n    });\n}\n_s(LinkComponent, \"MNV6IdWv8Lu3MKc7Fm4v59uGRY0=\");\n_c = LinkComponent;\nconst LinkStatusContext = /*#__PURE__*/ (0, _react.createContext)(_links.IDLE_LINK_STATUS);\nconst useLinkStatus = ()=>{\n    return (0, _react.useContext)(LinkStatusContext);\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\nvar _c;\n$RefreshReg$(_c, \"LinkComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2FwcC1kaXIvbGluay5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztJQW1UQTs7Ozs7Ozs7O0NBU0MsR0FDRCxPQXlaQztlQXpadUJBOztJQStaWEMsYUFBYTtlQUFiQTs7Ozs7NkVBMXRCMkQ7dUNBRTlDOzJEQUNPO2dEQUNKOzBDQUNBO21DQUNDO3lDQUNGO3NDQUNIO21DQVNsQjt3Q0FDb0I7K0NBQ1k7dUNBQ2I7QUEwTTFCLFNBQVNDLGdCQUFnQkMsS0FBdUI7SUFDOUMsTUFBTUMsY0FBY0QsTUFBTUUsYUFBYTtJQUN2QyxNQUFNQyxTQUFTRixZQUFZRyxZQUFZLENBQUM7SUFDeEMsT0FDR0QsVUFBVUEsV0FBVyxXQUN0QkgsTUFBTUssT0FBTyxJQUNiTCxNQUFNTSxPQUFPLElBQ2JOLE1BQU1PLFFBQVEsSUFDZFAsTUFBTVEsTUFBTSxJQUFJLDZCQUE2QjtJQUM1Q1IsTUFBTVMsV0FBVyxJQUFJVCxNQUFNUyxXQUFXLENBQUNDLEtBQUssS0FBSztBQUV0RDtBQUVBLFNBQVNDLFlBQ1BDLENBQW1CLEVBQ25CQyxJQUFZLEVBQ1pDLEVBQVUsRUFDVkMsZUFBcUQsRUFDckRDLE9BQWlCLEVBQ2pCQyxNQUFnQixFQUNoQkMsVUFBbUM7SUFFbkMsTUFBTSxFQUFFQyxRQUFRLEVBQUUsR0FBR1AsRUFBRVYsYUFBYTtJQUVwQyxrREFBa0Q7SUFDbEQsTUFBTWtCLG1CQUFtQkQsU0FBU0UsV0FBVyxPQUFPO0lBRXBELElBQ0dELG9CQUFvQnJCLGdCQUFnQmEsTUFDckNBLEVBQUVWLGFBQWEsQ0FBQ29CLFlBQVksQ0FBQyxhQUM3QjtRQUNBLDhDQUE4QztRQUM5QztJQUNGO0lBRUEsSUFBSSxDQUFDQyxDQUFBQSxHQUFBQSxZQUFBQSxVQUFBQSxFQUFXVixPQUFPO1FBQ3JCLElBQUlHLFNBQVM7WUFDWCw4REFBOEQ7WUFDOUQsK0JBQStCO1lBQy9CSixFQUFFWSxjQUFjO1lBQ2hCQyxTQUFTVCxPQUFPLENBQUNIO1FBQ25CO1FBRUEsOENBQThDO1FBQzlDO0lBQ0Y7SUFFQUQsRUFBRVksY0FBYztJQUVoQixNQUFNRSxXQUFXO1FBQ2YsSUFBSVIsWUFBWTtZQUNkLElBQUlTLHFCQUFxQjtZQUV6QlQsV0FBVztnQkFDVE0sZ0JBQWdCO29CQUNkRyxxQkFBcUI7Z0JBQ3ZCO1lBQ0Y7WUFFQSxJQUFJQSxvQkFBb0I7Z0JBQ3RCO1lBQ0Y7UUFDRjtRQUVBQyxDQUFBQSxHQUFBQSxtQkFBQUEsc0JBQUFBLEVBQ0VkLE1BQU1ELE1BQ05HLFVBQVUsWUFBWSxRQUN0QkMsVUFBQUEsT0FBQUEsU0FBVSxNQUNWRixnQkFBZ0JjLE9BQU87SUFFM0I7SUFFQUMsT0FBQUEsT0FBSyxDQUFDQyxlQUFlLENBQUNMO0FBQ3hCO0FBRUEsU0FBU00sa0JBQWtCQyxjQUFrQztJQUMzRCxJQUFJLE9BQU9BLG1CQUFtQixVQUFVO1FBQ3RDLE9BQU9BO0lBQ1Q7SUFFQSxPQUFPQyxDQUFBQSxHQUFBQSxXQUFBQSxTQUFBQSxFQUFVRDtBQUNuQjtBQVllLHVCQUNiRSxLQUdDOztJQUVELE1BQU0sQ0FBQ0MsWUFBWUMsd0JBQXdCLEdBQUdDLENBQUFBLEdBQUFBLE9BQUFBLGFBQUFBLEVBQWNDLE9BQUFBLGdCQUFnQjtJQUU1RSxJQUFJQztJQUVKLE1BQU16QixrQkFBa0IwQixDQUFBQSxHQUFBQSxPQUFBQSxNQUFNLEVBQXNCO0lBRXBELE1BQU0sRUFDSjVCLE1BQU02QixRQUFRLEVBQ2Q1QixJQUFJNkIsTUFBTSxFQUNWSCxVQUFVSSxZQUFZLEVBQ3RCQyxVQUFVQyxlQUFlLElBQUksRUFDN0JDLFFBQVEsRUFDUi9CLE9BQU8sRUFDUGdDLE9BQU8sRUFDUC9CLE1BQU0sRUFDTmdDLE9BQU8sRUFDUEMsY0FBY0MsZ0JBQWdCLEVBQzlCQyxjQUFjQyxnQkFBZ0IsRUFDOUJDLGlCQUFpQixLQUFLLEVBQ3RCcEMsVUFBVSxFQUNWcUMsS0FBS0MsWUFBWSxFQUNqQkMsdUJBQXVCLEVBQ3ZCLEdBQUdDLFdBQ0osR0FBR3ZCO0lBRUpLLFdBQVdJO0lBRVgsSUFDRVUsa0JBQ0MsUUFBT2QsYUFBYSxZQUFZLE9BQU9BLGFBQWEsU0FBTyxFQUM1RDtRQUNBQSxXQUFBQSxXQUFBQSxHQUFXLHFCQUFDbUIsS0FBQUE7c0JBQUduQjs7SUFDakI7SUFFQSxNQUFNb0IsU0FBUzlCLE9BQUFBLE9BQUssQ0FBQytCLFVBQVUsQ0FBQ0MsK0JBQUFBLGdCQUFnQjtJQUVoRCxNQUFNQyxrQkFBa0JqQixpQkFBaUI7SUFDekM7Ozs7OztHQU1DLEdBQ0QsTUFBTWtCLGtCQUNKbEIsaUJBQWlCLE9BQU9tQixvQkFBQUEsWUFBWSxDQUFDQyxJQUFJLEdBQUdELG9CQUFBQSxZQUFZLENBQUNFLElBQUk7SUFFL0QsSUFBSUMsSUFBb0IsRUFBbUI7UUFDekMsU0FBU0csZ0JBQWdCQyxJQUl4QjtZQUNDLE9BQU8scUJBS04sQ0FMTSxJQUFJQyxNQUNSLGlDQUErQkQsS0FBS0UsR0FBRyxHQUFDLGlCQUFlRixLQUFLRyxRQUFRLEdBQUMsNEJBQTRCSCxLQUFLSSxNQUFNLEdBQUMsZUFDM0csTUFBNkIsR0FDMUIscUVBQ0EsRUFBQyxHQUpGO3VCQUFBOzRCQUFBOzhCQUFBO1lBS1A7UUFDRjtRQUVBLHNDQUFzQztRQUN0QyxNQUFNRSxxQkFBc0Q7WUFDMURqRSxNQUFNO1FBQ1I7UUFDQSxNQUFNa0UsZ0JBQXFDQyxPQUFPQyxJQUFJLENBQ3BESDtRQUVGQyxjQUFjRyxPQUFPLENBQUMsQ0FBQ1I7WUFDckIsSUFBSUEsUUFBUSxRQUFRO2dCQUNsQixJQUNFdkMsS0FBSyxDQUFDdUMsSUFBSSxJQUFJLFFBQ2IsT0FBT3ZDLEtBQUssQ0FBQ3VDLElBQUksS0FBSyxZQUFZLE9BQU92QyxLQUFLLENBQUN1QyxJQUFJLEtBQUssVUFDekQ7b0JBQ0EsTUFBTUgsZ0JBQWdCO3dCQUNwQkc7d0JBQ0FDLFVBQVU7d0JBQ1ZDLFFBQVF6QyxLQUFLLENBQUN1QyxJQUFJLEtBQUssT0FBTyxTQUFTLE9BQU92QyxLQUFLLENBQUN1QyxJQUFJO29CQUMxRDtnQkFDRjtZQUNGLE9BQU87Z0JBQ0wsc0NBQXNDO2dCQUN0Qyw2REFBNkQ7Z0JBQzdELE1BQU1TLElBQVdUO1lBQ25CO1FBQ0Y7UUFFQSxzQ0FBc0M7UUFDdEMsTUFBTVUscUJBQXNEO1lBQzFEdEUsSUFBSTtZQUNKRSxTQUFTO1lBQ1RDLFFBQVE7WUFDUitCLFNBQVM7WUFDVEQsVUFBVTtZQUNWRixVQUFVO1lBQ1ZZLHlCQUF5QjtZQUN6QlIsU0FBUztZQUNUQyxjQUFjO1lBQ2RFLGNBQWM7WUFDZEUsZ0JBQWdCO1lBQ2hCcEMsWUFBWTtRQUNkO1FBQ0EsTUFBTW1FLGdCQUFxQ0wsT0FBT0MsSUFBSSxDQUNwREc7UUFFRkMsY0FBY0gsT0FBTyxDQUFDLENBQUNSO1lBQ3JCLE1BQU1ZLFVBQVUsT0FBT25ELEtBQUssQ0FBQ3VDLElBQUk7WUFFakMsSUFBSUEsUUFBUSxNQUFNO2dCQUNoQixJQUFJdkMsS0FBSyxDQUFDdUMsSUFBSSxJQUFJWSxZQUFZLFlBQVlBLFlBQVksVUFBVTtvQkFDOUQsTUFBTWYsZ0JBQWdCO3dCQUNwQkc7d0JBQ0FDLFVBQVU7d0JBQ1ZDLFFBQVFVO29CQUNWO2dCQUNGO1lBQ0YsT0FBTyxJQUNMWixRQUFRLGFBQ1JBLFFBQVEsa0JBQ1JBLFFBQVEsa0JBQ1JBLFFBQVEsY0FDUjtnQkFDQSxJQUFJdkMsS0FBSyxDQUFDdUMsSUFBSSxJQUFJWSxZQUFZLFlBQVk7b0JBQ3hDLE1BQU1mLGdCQUFnQjt3QkFDcEJHO3dCQUNBQyxVQUFVO3dCQUNWQyxRQUFRVTtvQkFDVjtnQkFDRjtZQUNGLE9BQU8sSUFDTFosUUFBUSxhQUNSQSxRQUFRLFlBQ1JBLFFBQVEsYUFDUkEsUUFBUSxjQUNSQSxRQUFRLGNBQ1JBLFFBQVEsb0JBQ1JBLFFBQVEsMkJBQ1I7Z0JBQ0EsSUFBSXZDLEtBQUssQ0FBQ3VDLElBQUksSUFBSSxRQUFRWSxZQUFZLFdBQVc7b0JBQy9DLE1BQU1mLGdCQUFnQjt3QkFDcEJHO3dCQUNBQyxVQUFVO3dCQUNWQyxRQUFRVTtvQkFDVjtnQkFDRjtZQUNGLE9BQU87Z0JBQ0wsc0NBQXNDO2dCQUN0Qyw2REFBNkQ7Z0JBQzdELE1BQU1ILElBQVdUO1lBQ25CO1FBQ0Y7SUFDRjtJQUVBLElBQUlOLElBQW9CLEVBQW1CO1FBQ3pDLElBQUlqQyxNQUFNb0QsTUFBTSxFQUFFO1lBQ2hCQyxDQUFBQSxHQUFBQSxVQUFBQSxRQUFBQSxFQUNFO1FBRUo7UUFDQSxJQUFJLENBQUM3QyxRQUFRO1lBQ1gsSUFBSTlCO1lBQ0osSUFBSSxPQUFPNkIsYUFBYSxVQUFVO2dCQUNoQzdCLE9BQU82QjtZQUNULE9BQU8sSUFDTCxPQUFPQSxhQUFhLFlBQ3BCLE9BQU9BLFNBQVMrQyxRQUFRLEtBQUssVUFDN0I7Z0JBQ0E1RSxPQUFPNkIsU0FBUytDLFFBQVE7WUFDMUI7WUFFQSxJQUFJNUUsTUFBTTtnQkFDUixNQUFNNkUsb0JBQW9CN0UsS0FDdkI4RSxLQUFLLENBQUMsS0FDTkMsSUFBSSxDQUFDLENBQUNDLFVBQVlBLFFBQVFDLFVBQVUsQ0FBQyxRQUFRRCxRQUFRRSxRQUFRLENBQUM7Z0JBRWpFLElBQUlMLG1CQUFtQjtvQkFDckIsTUFBTSxxQkFFTCxDQUZLLElBQUlqQixNQUNQLG1CQUFpQjVELE9BQUssNklBRG5COytCQUFBO29DQUFBO3NDQUFBO29CQUVOO2dCQUNGO1lBQ0Y7UUFDRjtJQUNGO0lBRUEsTUFBTSxFQUFFQSxJQUFJLEVBQUVDLEVBQUUsRUFBRSxHQUFHZ0IsT0FBQUEsT0FBSyxDQUFDa0UsT0FBTztpQ0FBQztZQUNqQyxNQUFNQyxlQUFlakUsa0JBQWtCVTtZQUN2QyxPQUFPO2dCQUNMN0IsTUFBTW9GO2dCQUNObkYsSUFBSTZCLFNBQVNYLGtCQUFrQlcsVUFBVXNEO1lBQzNDO1FBQ0Y7Z0NBQUc7UUFBQ3ZEO1FBQVVDO0tBQU87SUFFckIsb0ZBQW9GO0lBQ3BGLElBQUl1RDtJQUNKLElBQUk1QyxnQkFBZ0I7UUFDbEIsSUFBSWMsSUFBb0IsRUFBb0I7WUFDMUMsSUFBSW5CLFNBQVM7Z0JBQ1hrRCxRQUFRQyxJQUFJLENBQ1Qsb0RBQW9EMUQsV0FBUztZQUVsRTtZQUNBLElBQUlTLGtCQUFrQjtnQkFDcEJnRCxRQUFRQyxJQUFJLENBQ1QseURBQXlEMUQsV0FBUztZQUV2RTtZQUNBLElBQUk7Z0JBQ0Z3RCxRQUFRcEUsT0FBQUEsT0FBSyxDQUFDdUUsUUFBUSxDQUFDQyxJQUFJLENBQUM5RDtZQUM5QixFQUFFLE9BQU8rRCxLQUFLO2dCQUNaLElBQUksQ0FBQy9ELFVBQVU7b0JBQ2IsTUFBTSxxQkFFTCxDQUZLLElBQUlpQyxNQUNQLHVEQUF1RC9CLFdBQVMsa0ZBRDdEOytCQUFBO29DQUFBO3NDQUFBO29CQUVOO2dCQUNGO2dCQUNBLE1BQU0scUJBS0wsQ0FMSyxJQUFJK0IsTUFDUCw2REFBNkQvQixXQUFTLDhGQUNwRSxNQUE2QixHQUMxQixzRUFDQSxFQUFDLEdBSkg7MkJBQUE7Z0NBQUE7a0NBQUE7Z0JBS047WUFDRjtRQUNGLE9BQU8sRUFFTjtJQUNILE9BQU87UUFDTCxJQUFJMEIsSUFBb0IsRUFBb0I7WUFDMUMsSUFBSSxDQUFDNUIsWUFBQUEsT0FBQUEsS0FBQUEsSUFBQUEsU0FBa0JnRSxJQUFBQSxNQUFTLEtBQUs7Z0JBQ25DLE1BQU0scUJBRUwsQ0FGSyxJQUFJL0IsTUFDUixvS0FESTsyQkFBQTtnQ0FBQTtrQ0FBQTtnQkFFTjtZQUNGO1FBQ0Y7SUFDRjtJQUVBLE1BQU1nQyxXQUFnQm5ELGlCQUNsQjRDLFNBQVMsT0FBT0EsVUFBVSxZQUFZQSxNQUFNM0MsR0FBRyxHQUMvQ0M7SUFFSiw0RUFBNEU7SUFDNUUsc0VBQXNFO0lBQ3RFLDRFQUE0RTtJQUM1RSw2QkFBNkI7SUFDN0IsTUFBTWtELCtCQUErQjVFLE9BQUFBLE9BQUssQ0FBQzZFLFdBQVc7bUVBQ3BELENBQUNDO1lBQ0MsSUFBSWhELFdBQVcsTUFBTTtnQkFDbkI3QyxnQkFBZ0JjLE9BQU8sR0FBR2dGLENBQUFBLEdBQUFBLE9BQUFBLGlCQUFBQSxFQUN4QkQsU0FDQS9GLE1BQ0ErQyxRQUNBSSxpQkFDQUQsaUJBQ0ExQjtZQUVKO1lBRUE7MkVBQU87b0JBQ0wsSUFBSXRCLGdCQUFnQmMsT0FBTyxFQUFFO3dCQUMzQmlGLENBQUFBLEdBQUFBLE9BQUFBLCtCQUFBQSxFQUFnQy9GLGdCQUFnQmMsT0FBTzt3QkFDdkRkLGdCQUFnQmMsT0FBTyxHQUFHO29CQUM1QjtvQkFDQWtGLENBQUFBLEdBQUFBLE9BQUFBLDJCQUFBQSxFQUE0Qkg7Z0JBQzlCOztRQUNGO2tFQUNBO1FBQUM3QztRQUFpQmxEO1FBQU0rQztRQUFRSTtRQUFpQjNCO0tBQXdCO0lBRzNFLE1BQU0yRSxZQUFZQyxDQUFBQSxHQUFBQSxjQUFBQSxZQUFBQSxFQUFhUCw4QkFBOEJEO0lBRTdELE1BQU1TLGFBTUY7UUFDRjNELEtBQUt5RDtRQUNML0QsU0FBUXJDLENBQUM7WUFDUCxJQUFJd0QsSUFBb0IsRUFBbUI7Z0JBQ3pDLElBQUksQ0FBQ3hELEdBQUc7b0JBQ04sTUFBTSxxQkFFTCxDQUZLLElBQUk2RCxNQUNQLG1GQURHOytCQUFBO29DQUFBO3NDQUFBO29CQUVOO2dCQUNGO1lBQ0Y7WUFFQSxJQUFJLENBQUNuQixrQkFBa0IsT0FBT0wsWUFBWSxZQUFZO2dCQUNwREEsUUFBUXJDO1lBQ1Y7WUFFQSxJQUNFMEMsa0JBQ0E0QyxNQUFNL0QsS0FBSyxJQUNYLE9BQU8rRCxNQUFNL0QsS0FBSyxDQUFDYyxPQUFPLEtBQUssWUFDL0I7Z0JBQ0FpRCxNQUFNL0QsS0FBSyxDQUFDYyxPQUFPLENBQUNyQztZQUN0QjtZQUVBLElBQUksQ0FBQ2dELFFBQVE7Z0JBQ1g7WUFDRjtZQUVBLElBQUloRCxFQUFFdUcsZ0JBQWdCLEVBQUU7Z0JBQ3RCO1lBQ0Y7WUFFQXhHLFlBQVlDLEdBQUdDLE1BQU1DLElBQUlDLGlCQUFpQkMsU0FBU0MsUUFBUUM7UUFDN0Q7UUFDQWdDLGNBQWF0QyxDQUFDO1lBQ1osSUFBSSxDQUFDMEMsa0JBQWtCLE9BQU9ILHFCQUFxQixZQUFZO2dCQUM3REEsaUJBQWlCdkM7WUFDbkI7WUFFQSxJQUNFMEMsa0JBQ0E0QyxNQUFNL0QsS0FBSyxJQUNYLE9BQU8rRCxNQUFNL0QsS0FBSyxDQUFDZSxZQUFZLEtBQUssWUFDcEM7Z0JBQ0FnRCxNQUFNL0QsS0FBSyxDQUFDZSxZQUFZLENBQUN0QztZQUMzQjtZQUVBLElBQUksQ0FBQ2dELFFBQVE7Z0JBQ1g7WUFDRjtZQUVBLElBQUksQ0FBQ0csbUJBQW1CSyxRQUFRQyxHQUFHLENBQUNDLE1BQWEsRUFBTCxhQUFvQjtnQkFDOUQ7WUFDRjtZQUVBLE1BQU04QywyQkFBMkIzRCw0QkFBNEI7WUFDN0Q0RCxDQUFBQSxHQUFBQSxPQUFBQSxrQkFBQUEsRUFDRXpHLEVBQUVWLGFBQWEsRUFDZmtIO1FBRUo7UUFDQWhFLGNBQWNnQixNQUFzQyxHQUNoRG1ELENBQVNBLEdBQ1QsU0FBU25FLGFBQWF4QyxDQUFDO1lBQ3JCLElBQUksQ0FBQzBDLGtCQUFrQixPQUFPRCxxQkFBcUIsWUFBWTtnQkFDN0RBLGlCQUFpQnpDO1lBQ25CO1lBRUEsSUFDRTBDLGtCQUNBNEMsTUFBTS9ELEtBQUssSUFDWCxPQUFPK0QsTUFBTS9ELEtBQUssQ0FBQ2lCLFlBQVksS0FBSyxZQUNwQztnQkFDQThDLE1BQU0vRCxLQUFLLENBQUNpQixZQUFZLENBQUN4QztZQUMzQjtZQUVBLElBQUksQ0FBQ2dELFFBQVE7Z0JBQ1g7WUFDRjtZQUVBLElBQUksQ0FBQ0csaUJBQWlCO2dCQUNwQjtZQUNGO1lBRUEsTUFBTXFELDJCQUEyQjNELDRCQUE0QjtZQUM3RDRELENBQUFBLEdBQUFBLE9BQUFBLGtCQUFBQSxFQUNFekcsRUFBRVYsYUFBYSxFQUNma0g7UUFFSjtJQUNOO0lBRUEsNkZBQTZGO0lBQzdGLHdGQUF3RjtJQUN4RiwyRUFBMkU7SUFDM0UsSUFBSUksQ0FBQUEsR0FBQUEsT0FBQUEsYUFBQUEsRUFBYzFHLEtBQUs7UUFDckJvRyxXQUFXckcsSUFBSSxHQUFHQztJQUNwQixPQUFPLElBQ0wsQ0FBQ3dDLGtCQUNEUCxZQUNDbUQsTUFBTU0sSUFBSSxLQUFLLE9BQU8sQ0FBRSxXQUFVTixNQUFNL0QsS0FBQUEsR0FDekM7UUFDQStFLFdBQVdyRyxJQUFJLEdBQUc0RyxDQUFBQSxHQUFBQSxhQUFBQSxXQUFBQSxFQUFZM0c7SUFDaEM7SUFFQSxJQUFJNEc7SUFFSixJQUFJcEUsZ0JBQWdCO1FBQ2xCLElBQUljLElBQW9CLEVBQW9CO1lBQzFDdUQsQ0FBQUEsR0FBQUEsV0FBQUEsU0FBUyxFQUNQLG9FQUNFLG9FQUNBLDRDQUNBO1FBRU47UUFDQUQsT0FBQUEsV0FBQUEsR0FBTzVGLE9BQUFBLE9BQUssQ0FBQzhGLFlBQVksQ0FBQzFCLE9BQU9nQjtJQUNuQyxPQUFPO1FBQ0xRLE9BQUFBLFdBQUFBLEdBQ0UscUJBQUMvRCxLQUFBQTtZQUFHLEdBQUdELFNBQVM7WUFBRyxHQUFHd0QsVUFBVTtzQkFDN0IxRTs7SUFHUDtJQUVBLHFCQUNFLHFCQUFDcUYsa0JBQWtCQyxRQUFRO1FBQUNDLE9BQU8zRjtrQkFDaENzRjs7QUFHUDs7S0F6WndCN0g7QUEyWnhCLE1BQU1nSSxvQkFBQUEsV0FBQUEsR0FBb0JHLENBQUFBLEdBQUFBLE9BQUFBLGFBQUFBLEVBRXhCekYsT0FBQUEsZ0JBQWdCO0FBRVgsTUFBTXpDLGdCQUFnQjtJQUMzQixPQUFPK0QsQ0FBQUEsR0FBQUEsT0FBQUEsVUFBQUEsRUFBV2dFO0FBQ3BCIiwic291cmNlcyI6WyIvVXNlcnMvbmlzaGFwYW5jaGFsL0RvY3VtZW50cy9zcmMvY2xpZW50L2FwcC1kaXIvbGluay50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VPcHRpbWlzdGljLCB1c2VSZWYgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB0eXBlIHsgVXJsT2JqZWN0IH0gZnJvbSAndXJsJ1xuaW1wb3J0IHsgZm9ybWF0VXJsIH0gZnJvbSAnLi4vLi4vc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvZm9ybWF0LXVybCdcbmltcG9ydCB7IEFwcFJvdXRlckNvbnRleHQgfSBmcm9tICcuLi8uLi9zaGFyZWQvbGliL2FwcC1yb3V0ZXItY29udGV4dC5zaGFyZWQtcnVudGltZSdcbmltcG9ydCB7IFByZWZldGNoS2luZCB9IGZyb20gJy4uL2NvbXBvbmVudHMvcm91dGVyLXJlZHVjZXIvcm91dGVyLXJlZHVjZXItdHlwZXMnXG5pbXBvcnQgeyB1c2VNZXJnZWRSZWYgfSBmcm9tICcuLi91c2UtbWVyZ2VkLXJlZidcbmltcG9ydCB7IGlzQWJzb2x1dGVVcmwgfSBmcm9tICcuLi8uLi9zaGFyZWQvbGliL3V0aWxzJ1xuaW1wb3J0IHsgYWRkQmFzZVBhdGggfSBmcm9tICcuLi9hZGQtYmFzZS1wYXRoJ1xuaW1wb3J0IHsgd2Fybk9uY2UgfSBmcm9tICcuLi8uLi9zaGFyZWQvbGliL3V0aWxzL3dhcm4tb25jZSdcbmltcG9ydCB0eXBlIHsgUEVORElOR19MSU5LX1NUQVRVUyB9IGZyb20gJy4uL2NvbXBvbmVudHMvbGlua3MnXG5pbXBvcnQge1xuICBJRExFX0xJTktfU1RBVFVTLFxuICBtb3VudExpbmtJbnN0YW5jZSxcbiAgb25OYXZpZ2F0aW9uSW50ZW50LFxuICB1bm1vdW50TGlua0ZvckN1cnJlbnROYXZpZ2F0aW9uLFxuICB1bm1vdW50UHJlZmV0Y2hhYmxlSW5zdGFuY2UsXG4gIHR5cGUgTGlua0luc3RhbmNlLFxufSBmcm9tICcuLi9jb21wb25lbnRzL2xpbmtzJ1xuaW1wb3J0IHsgaXNMb2NhbFVSTCB9IGZyb20gJy4uLy4uL3NoYXJlZC9saWIvcm91dGVyL3V0aWxzL2lzLWxvY2FsLXVybCdcbmltcG9ydCB7IGRpc3BhdGNoTmF2aWdhdGVBY3Rpb24gfSBmcm9tICcuLi9jb21wb25lbnRzL2FwcC1yb3V0ZXItaW5zdGFuY2UnXG5pbXBvcnQgeyBlcnJvck9uY2UgfSBmcm9tICcuLi8uLi9zaGFyZWQvbGliL3V0aWxzL2Vycm9yLW9uY2UnXG5cbnR5cGUgVXJsID0gc3RyaW5nIHwgVXJsT2JqZWN0XG50eXBlIFJlcXVpcmVkS2V5czxUPiA9IHtcbiAgW0sgaW4ga2V5b2YgVF0tPzoge30gZXh0ZW5kcyBQaWNrPFQsIEs+ID8gbmV2ZXIgOiBLXG59W2tleW9mIFRdXG50eXBlIE9wdGlvbmFsS2V5czxUPiA9IHtcbiAgW0sgaW4ga2V5b2YgVF0tPzoge30gZXh0ZW5kcyBQaWNrPFQsIEs+ID8gSyA6IG5ldmVyXG59W2tleW9mIFRdXG5cbnR5cGUgT25OYXZpZ2F0ZUV2ZW50SGFuZGxlciA9IChldmVudDogeyBwcmV2ZW50RGVmYXVsdDogKCkgPT4gdm9pZCB9KSA9PiB2b2lkXG5cbnR5cGUgSW50ZXJuYWxMaW5rUHJvcHMgPSB7XG4gIC8qKlxuICAgKiAqKlJlcXVpcmVkKiouIFRoZSBwYXRoIG9yIFVSTCB0byBuYXZpZ2F0ZSB0by4gSXQgY2FuIGFsc28gYmUgYW4gb2JqZWN0IChzaW1pbGFyIHRvIGBVUkxgKS5cbiAgICpcbiAgICogQGV4YW1wbGVcbiAgICogYGBgdHN4XG4gICAqIC8vIE5hdmlnYXRlIHRvIC9kYXNoYm9hcmQ6XG4gICAqIDxMaW5rIGhyZWY9XCIvZGFzaGJvYXJkXCI+RGFzaGJvYXJkPC9MaW5rPlxuICAgKlxuICAgKiAvLyBOYXZpZ2F0ZSB0byAvYWJvdXQ/bmFtZT10ZXN0OlxuICAgKiA8TGluayBocmVmPXt7IHBhdGhuYW1lOiAnL2Fib3V0JywgcXVlcnk6IHsgbmFtZTogJ3Rlc3QnIH0gfX0+XG4gICAqICAgQWJvdXRcbiAgICogPC9MaW5rPlxuICAgKiBgYGBcbiAgICpcbiAgICogQHJlbWFya3NcbiAgICogLSBGb3IgZXh0ZXJuYWwgVVJMcywgdXNlIGEgZnVsbHkgcXVhbGlmaWVkIFVSTCBzdWNoIGFzIGBodHRwczovLy4uLmAuXG4gICAqIC0gSW4gdGhlIEFwcCBSb3V0ZXIsIGR5bmFtaWMgcm91dGVzIG11c3Qgbm90IGluY2x1ZGUgYnJhY2tldGVkIHNlZ21lbnRzIGluIGBocmVmYC5cbiAgICovXG4gIGhyZWY6IFVybFxuXG4gIC8qKlxuICAgKiBAZGVwcmVjYXRlZCB2MTAuMC4wOiBgaHJlZmAgcHJvcHMgcG9pbnRpbmcgdG8gYSBkeW5hbWljIHJvdXRlIGFyZVxuICAgKiBhdXRvbWF0aWNhbGx5IHJlc29sdmVkIGFuZCBubyBsb25nZXIgcmVxdWlyZSB0aGUgYGFzYCBwcm9wLlxuICAgKi9cbiAgYXM/OiBVcmxcblxuICAvKipcbiAgICogUmVwbGFjZSB0aGUgY3VycmVudCBgaGlzdG9yeWAgc3RhdGUgaW5zdGVhZCBvZiBhZGRpbmcgYSBuZXcgVVJMIGludG8gdGhlIHN0YWNrLlxuICAgKlxuICAgKiBAZGVmYXVsdFZhbHVlIGBmYWxzZWBcbiAgICpcbiAgICogQGV4YW1wbGVcbiAgICogYGBgdHN4XG4gICAqIDxMaW5rIGhyZWY9XCIvYWJvdXRcIiByZXBsYWNlPlxuICAgKiAgIEFib3V0IChyZXBsYWNlcyB0aGUgaGlzdG9yeSBzdGF0ZSlcbiAgICogPC9MaW5rPlxuICAgKiBgYGBcbiAgICovXG4gIHJlcGxhY2U/OiBib29sZWFuXG5cbiAgLyoqXG4gICAqIFdoZXRoZXIgdG8gb3ZlcnJpZGUgdGhlIGRlZmF1bHQgc2Nyb2xsIGJlaGF2aW9yLiBJZiBgdHJ1ZWAsIE5leHQuanMgYXR0ZW1wdHMgdG8gbWFpbnRhaW5cbiAgICogdGhlIHNjcm9sbCBwb3NpdGlvbiBpZiB0aGUgbmV3bHkgbmF2aWdhdGVkIHBhZ2UgaXMgc3RpbGwgdmlzaWJsZS4gSWYgbm90LCBpdCBzY3JvbGxzIHRvIHRoZSB0b3AuXG4gICAqXG4gICAqIElmIGBmYWxzZWAsIE5leHQuanMgd2lsbCBub3QgbW9kaWZ5IHRoZSBzY3JvbGwgYmVoYXZpb3IgYXQgYWxsLlxuICAgKlxuICAgKiBAZGVmYXVsdFZhbHVlIGB0cnVlYFxuICAgKlxuICAgKiBAZXhhbXBsZVxuICAgKiBgYGB0c3hcbiAgICogPExpbmsgaHJlZj1cIi9kYXNoYm9hcmRcIiBzY3JvbGw9e2ZhbHNlfT5cbiAgICogICBObyBhdXRvIHNjcm9sbFxuICAgKiA8L0xpbms+XG4gICAqIGBgYFxuICAgKi9cbiAgc2Nyb2xsPzogYm9vbGVhblxuXG4gIC8qKlxuICAgKiBVcGRhdGUgdGhlIHBhdGggb2YgdGhlIGN1cnJlbnQgcGFnZSB3aXRob3V0IHJlcnVubmluZyBkYXRhIGZldGNoaW5nIG1ldGhvZHNcbiAgICogbGlrZSBgZ2V0U3RhdGljUHJvcHNgLCBgZ2V0U2VydmVyU2lkZVByb3BzYCwgb3IgYGdldEluaXRpYWxQcm9wc2AuXG4gICAqXG4gICAqIEByZW1hcmtzXG4gICAqIGBzaGFsbG93YCBvbmx5IGFwcGxpZXMgdG8gdGhlIFBhZ2VzIFJvdXRlci4gRm9yIHRoZSBBcHAgUm91dGVyLCBzZWUgdGhlXG4gICAqIFtmb2xsb3dpbmcgZG9jdW1lbnRhdGlvbl0oaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBwL2J1aWxkaW5nLXlvdXItYXBwbGljYXRpb24vcm91dGluZy9saW5raW5nLWFuZC1uYXZpZ2F0aW5nI3VzaW5nLXRoZS1uYXRpdmUtaGlzdG9yeS1hcGkpLlxuICAgKlxuICAgKiBAZGVmYXVsdFZhbHVlIGBmYWxzZWBcbiAgICpcbiAgICogQGV4YW1wbGVcbiAgICogYGBgdHN4XG4gICAqIDxMaW5rIGhyZWY9XCIvYmxvZ1wiIHNoYWxsb3c+XG4gICAqICAgU2hhbGxvdyBuYXZpZ2F0aW9uXG4gICAqIDwvTGluaz5cbiAgICogYGBgXG4gICAqL1xuICBzaGFsbG93PzogYm9vbGVhblxuXG4gIC8qKlxuICAgKiBGb3JjZXMgYExpbmtgIHRvIHBhc3MgaXRzIGBocmVmYCB0byB0aGUgY2hpbGQgY29tcG9uZW50LiBVc2VmdWwgaWYgdGhlIGNoaWxkIGlzIGEgY3VzdG9tXG4gICAqIGNvbXBvbmVudCB0aGF0IHdyYXBzIGFuIGA8YT5gIHRhZywgb3IgaWYgeW91J3JlIHVzaW5nIGNlcnRhaW4gc3R5bGluZyBsaWJyYXJpZXMuXG4gICAqXG4gICAqIEBkZWZhdWx0VmFsdWUgYGZhbHNlYFxuICAgKlxuICAgKiBAZXhhbXBsZVxuICAgKiBgYGB0c3hcbiAgICogPExpbmsgaHJlZj1cIi9kYXNoYm9hcmRcIiBwYXNzSHJlZj5cbiAgICogICA8TXlTdHlsZWRBbmNob3I+RGFzaGJvYXJkPC9NeVN0eWxlZEFuY2hvcj5cbiAgICogPC9MaW5rPlxuICAgKiBgYGBcbiAgICovXG4gIHBhc3NIcmVmPzogYm9vbGVhblxuXG4gIC8qKlxuICAgKiBQcmVmZXRjaCB0aGUgcGFnZSBpbiB0aGUgYmFja2dyb3VuZC5cbiAgICogQW55IGA8TGluayAvPmAgdGhhdCBpcyBpbiB0aGUgdmlld3BvcnQgKGluaXRpYWxseSBvciB0aHJvdWdoIHNjcm9sbCkgd2lsbCBiZSBwcmVmZXRjaGVkLlxuICAgKiBQcmVmZXRjaCBjYW4gYmUgZGlzYWJsZWQgYnkgcGFzc2luZyBgcHJlZmV0Y2g9e2ZhbHNlfWAuXG4gICAqXG4gICAqIEByZW1hcmtzXG4gICAqIFByZWZldGNoaW5nIGlzIG9ubHkgZW5hYmxlZCBpbiBwcm9kdWN0aW9uLlxuICAgKlxuICAgKiAtIEluIHRoZSAqKkFwcCBSb3V0ZXIqKjpcbiAgICogICAtIGBudWxsYCAoZGVmYXVsdCk6IFByZWZldGNoIGJlaGF2aW9yIGRlcGVuZHMgb24gc3RhdGljIHZzIGR5bmFtaWMgcm91dGVzOlxuICAgKiAgICAgLSBTdGF0aWMgcm91dGVzOiBmdWxseSBwcmVmZXRjaGVkXG4gICAqICAgICAtIER5bmFtaWMgcm91dGVzOiBwYXJ0aWFsIHByZWZldGNoIHRvIHRoZSBuZWFyZXN0IHNlZ21lbnQgd2l0aCBhIGBsb2FkaW5nLmpzYFxuICAgKiAgIC0gYHRydWVgOiBBbHdheXMgcHJlZmV0Y2ggdGhlIGZ1bGwgcm91dGUgYW5kIGRhdGEuXG4gICAqICAgLSBgZmFsc2VgOiBEaXNhYmxlIHByZWZldGNoaW5nIG9uIGJvdGggdmlld3BvcnQgYW5kIGhvdmVyLlxuICAgKiAtIEluIHRoZSAqKlBhZ2VzIFJvdXRlcioqOlxuICAgKiAgIC0gYHRydWVgIChkZWZhdWx0KTogUHJlZmV0Y2hlcyB0aGUgcm91dGUgYW5kIGRhdGEgaW4gdGhlIGJhY2tncm91bmQgb24gdmlld3BvcnQgb3IgaG92ZXIuXG4gICAqICAgLSBgZmFsc2VgOiBQcmVmZXRjaCBvbmx5IG9uIGhvdmVyLCBub3Qgb24gdmlld3BvcnQuXG4gICAqXG4gICAqIEBkZWZhdWx0VmFsdWUgYHRydWVgIChQYWdlcyBSb3V0ZXIpIG9yIGBudWxsYCAoQXBwIFJvdXRlcilcbiAgICpcbiAgICogQGV4YW1wbGVcbiAgICogYGBgdHN4XG4gICAqIDxMaW5rIGhyZWY9XCIvZGFzaGJvYXJkXCIgcHJlZmV0Y2g9e2ZhbHNlfT5cbiAgICogICBEYXNoYm9hcmRcbiAgICogPC9MaW5rPlxuICAgKiBgYGBcbiAgICovXG4gIHByZWZldGNoPzogYm9vbGVhbiB8IG51bGxcblxuICAvKipcbiAgICogKHVuc3RhYmxlKSBTd2l0Y2ggdG8gYSBkeW5hbWljIHByZWZldGNoIG9uIGhvdmVyLiBFZmZlY3RpdmVseSB0aGUgc2FtZSBhc1xuICAgKiB1cGRhdGluZyB0aGUgcHJlZmV0Y2ggcHJvcCB0byBgdHJ1ZWAgaW4gYSBtb3VzZSBldmVudC5cbiAgICovXG4gIHVuc3RhYmxlX2R5bmFtaWNPbkhvdmVyPzogYm9vbGVhblxuXG4gIC8qKlxuICAgKiBUaGUgYWN0aXZlIGxvY2FsZSBpcyBhdXRvbWF0aWNhbGx5IHByZXBlbmRlZCBpbiB0aGUgUGFnZXMgUm91dGVyLiBgbG9jYWxlYCBhbGxvd3MgZm9yIHByb3ZpZGluZ1xuICAgKiBhIGRpZmZlcmVudCBsb2NhbGUsIG9yIGNhbiBiZSBzZXQgdG8gYGZhbHNlYCB0byBvcHQgb3V0IG9mIGF1dG9tYXRpYyBsb2NhbGUgYmVoYXZpb3IuXG4gICAqXG4gICAqIEByZW1hcmtzXG4gICAqIE5vdGU6IGxvY2FsZSBvbmx5IGFwcGxpZXMgaW4gdGhlIFBhZ2VzIFJvdXRlciBhbmQgaXMgaWdub3JlZCBpbiB0aGUgQXBwIFJvdXRlci5cbiAgICpcbiAgICogQGV4YW1wbGVcbiAgICogYGBgdHN4XG4gICAqIC8vIFVzZSB0aGUgJ2ZyJyBsb2NhbGU6XG4gICAqIDxMaW5rIGhyZWY9XCIvYWJvdXRcIiBsb2NhbGU9XCJmclwiPlxuICAgKiAgIEFib3V0IChGcmVuY2gpXG4gICAqIDwvTGluaz5cbiAgICpcbiAgICogLy8gRGlzYWJsZSBsb2NhbGUgcHJlZml4OlxuICAgKiA8TGluayBocmVmPVwiL2Fib3V0XCIgbG9jYWxlPXtmYWxzZX0+XG4gICAqICAgQWJvdXQgKG5vIGxvY2FsZSBwcmVmaXgpXG4gICAqIDwvTGluaz5cbiAgICogYGBgXG4gICAqL1xuICBsb2NhbGU/OiBzdHJpbmcgfCBmYWxzZVxuXG4gIC8qKlxuICAgKiBFbmFibGUgbGVnYWN5IGxpbmsgYmVoYXZpb3IsIHJlcXVpcmluZyBhbiBgPGE+YCB0YWcgdG8gd3JhcCB0aGUgY2hpbGQgY29udGVudFxuICAgKiBpZiB0aGUgY2hpbGQgaXMgYSBzdHJpbmcgb3IgbnVtYmVyLlxuICAgKlxuICAgKiBAZGVwcmVjYXRlZCBUaGlzIHdpbGwgYmUgcmVtb3ZlZCBpbiB2MTZcbiAgICogQGRlZmF1bHRWYWx1ZSBgZmFsc2VgXG4gICAqIEBzZWUgaHR0cHM6Ly9naXRodWIuY29tL3ZlcmNlbC9uZXh0LmpzL2NvbW1pdC80ODllNjVlZDk4NTQ0ZTY5YjBhZmQ3ZTBjZmMzZjlmNmMyYjgwM2I3XG4gICAqL1xuICBsZWdhY3lCZWhhdmlvcj86IGJvb2xlYW5cblxuICAvKipcbiAgICogT3B0aW9uYWwgZXZlbnQgaGFuZGxlciBmb3Igd2hlbiB0aGUgbW91c2UgcG9pbnRlciBpcyBtb3ZlZCBvbnRvIHRoZSBgPExpbms+YC5cbiAgICovXG4gIG9uTW91c2VFbnRlcj86IFJlYWN0Lk1vdXNlRXZlbnRIYW5kbGVyPEhUTUxBbmNob3JFbGVtZW50PlxuXG4gIC8qKlxuICAgKiBPcHRpb25hbCBldmVudCBoYW5kbGVyIGZvciB3aGVuIHRoZSBgPExpbms+YCBpcyB0b3VjaGVkLlxuICAgKi9cbiAgb25Ub3VjaFN0YXJ0PzogUmVhY3QuVG91Y2hFdmVudEhhbmRsZXI8SFRNTEFuY2hvckVsZW1lbnQ+XG5cbiAgLyoqXG4gICAqIE9wdGlvbmFsIGV2ZW50IGhhbmRsZXIgZm9yIHdoZW4gdGhlIGA8TGluaz5gIGlzIGNsaWNrZWQuXG4gICAqL1xuICBvbkNsaWNrPzogUmVhY3QuTW91c2VFdmVudEhhbmRsZXI8SFRNTEFuY2hvckVsZW1lbnQ+XG5cbiAgLyoqXG4gICAqIE9wdGlvbmFsIGV2ZW50IGhhbmRsZXIgZm9yIHdoZW4gdGhlIGA8TGluaz5gIGlzIG5hdmlnYXRlZC5cbiAgICovXG4gIG9uTmF2aWdhdGU/OiBPbk5hdmlnYXRlRXZlbnRIYW5kbGVyXG59XG5cbi8vIFRPRE8tQVBQOiBJbmNsdWRlIHRoZSBmdWxsIHNldCBvZiBBbmNob3IgcHJvcHNcbi8vIGFkZGluZyB0aGlzIHRvIHRoZSBwdWJsaWNseSBleHBvcnRlZCB0eXBlIGN1cnJlbnRseSBicmVha3MgZXhpc3RpbmcgYXBwc1xuXG4vLyBgUm91dGVJbmZlclR5cGVgIGlzIGEgc3R1YiBoZXJlIHRvIGF2b2lkIGJyZWFraW5nIGB0eXBlZFJvdXRlc2Agd2hlbiB0aGUgdHlwZVxuLy8gaXNuJ3QgZ2VuZXJhdGVkIHlldC4gSXQgd2lsbCBiZSByZXBsYWNlZCB3aGVuIHRoZSB3ZWJwYWNrIHBsdWdpbiBydW5zLlxuLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9uby11bnVzZWQtdmFyc1xuZXhwb3J0IHR5cGUgTGlua1Byb3BzPFJvdXRlSW5mZXJUeXBlID0gYW55PiA9IEludGVybmFsTGlua1Byb3BzXG50eXBlIExpbmtQcm9wc1JlcXVpcmVkID0gUmVxdWlyZWRLZXlzPExpbmtQcm9wcz5cbnR5cGUgTGlua1Byb3BzT3B0aW9uYWwgPSBPcHRpb25hbEtleXM8T21pdDxJbnRlcm5hbExpbmtQcm9wcywgJ2xvY2FsZSc+PlxuXG5mdW5jdGlvbiBpc01vZGlmaWVkRXZlbnQoZXZlbnQ6IFJlYWN0Lk1vdXNlRXZlbnQpOiBib29sZWFuIHtcbiAgY29uc3QgZXZlbnRUYXJnZXQgPSBldmVudC5jdXJyZW50VGFyZ2V0IGFzIEhUTUxBbmNob3JFbGVtZW50IHwgU1ZHQUVsZW1lbnRcbiAgY29uc3QgdGFyZ2V0ID0gZXZlbnRUYXJnZXQuZ2V0QXR0cmlidXRlKCd0YXJnZXQnKVxuICByZXR1cm4gKFxuICAgICh0YXJnZXQgJiYgdGFyZ2V0ICE9PSAnX3NlbGYnKSB8fFxuICAgIGV2ZW50Lm1ldGFLZXkgfHxcbiAgICBldmVudC5jdHJsS2V5IHx8XG4gICAgZXZlbnQuc2hpZnRLZXkgfHxcbiAgICBldmVudC5hbHRLZXkgfHwgLy8gdHJpZ2dlcnMgcmVzb3VyY2UgZG93bmxvYWRcbiAgICAoZXZlbnQubmF0aXZlRXZlbnQgJiYgZXZlbnQubmF0aXZlRXZlbnQud2hpY2ggPT09IDIpXG4gIClcbn1cblxuZnVuY3Rpb24gbGlua0NsaWNrZWQoXG4gIGU6IFJlYWN0Lk1vdXNlRXZlbnQsXG4gIGhyZWY6IHN0cmluZyxcbiAgYXM6IHN0cmluZyxcbiAgbGlua0luc3RhbmNlUmVmOiBSZWFjdC5SZWZPYmplY3Q8TGlua0luc3RhbmNlIHwgbnVsbD4sXG4gIHJlcGxhY2U/OiBib29sZWFuLFxuICBzY3JvbGw/OiBib29sZWFuLFxuICBvbk5hdmlnYXRlPzogT25OYXZpZ2F0ZUV2ZW50SGFuZGxlclxuKTogdm9pZCB7XG4gIGNvbnN0IHsgbm9kZU5hbWUgfSA9IGUuY3VycmVudFRhcmdldFxuXG4gIC8vIGFuY2hvcnMgaW5zaWRlIGFuIHN2ZyBoYXZlIGEgbG93ZXJjYXNlIG5vZGVOYW1lXG4gIGNvbnN0IGlzQW5jaG9yTm9kZU5hbWUgPSBub2RlTmFtZS50b1VwcGVyQ2FzZSgpID09PSAnQSdcblxuICBpZiAoXG4gICAgKGlzQW5jaG9yTm9kZU5hbWUgJiYgaXNNb2RpZmllZEV2ZW50KGUpKSB8fFxuICAgIGUuY3VycmVudFRhcmdldC5oYXNBdHRyaWJ1dGUoJ2Rvd25sb2FkJylcbiAgKSB7XG4gICAgLy8gaWdub3JlIGNsaWNrIGZvciBicm93c2Vy4oCZcyBkZWZhdWx0IGJlaGF2aW9yXG4gICAgcmV0dXJuXG4gIH1cblxuICBpZiAoIWlzTG9jYWxVUkwoaHJlZikpIHtcbiAgICBpZiAocmVwbGFjZSkge1xuICAgICAgLy8gYnJvd3NlciBkZWZhdWx0IGJlaGF2aW9yIGRvZXMgbm90IHJlcGxhY2UgdGhlIGhpc3Rvcnkgc3RhdGVcbiAgICAgIC8vIHNvIHdlIG5lZWQgdG8gZG8gaXQgbWFudWFsbHlcbiAgICAgIGUucHJldmVudERlZmF1bHQoKVxuICAgICAgbG9jYXRpb24ucmVwbGFjZShocmVmKVxuICAgIH1cblxuICAgIC8vIGlnbm9yZSBjbGljayBmb3IgYnJvd3NlcuKAmXMgZGVmYXVsdCBiZWhhdmlvclxuICAgIHJldHVyblxuICB9XG5cbiAgZS5wcmV2ZW50RGVmYXVsdCgpXG5cbiAgY29uc3QgbmF2aWdhdGUgPSAoKSA9PiB7XG4gICAgaWYgKG9uTmF2aWdhdGUpIHtcbiAgICAgIGxldCBpc0RlZmF1bHRQcmV2ZW50ZWQgPSBmYWxzZVxuXG4gICAgICBvbk5hdmlnYXRlKHtcbiAgICAgICAgcHJldmVudERlZmF1bHQ6ICgpID0+IHtcbiAgICAgICAgICBpc0RlZmF1bHRQcmV2ZW50ZWQgPSB0cnVlXG4gICAgICAgIH0sXG4gICAgICB9KVxuXG4gICAgICBpZiAoaXNEZWZhdWx0UHJldmVudGVkKSB7XG4gICAgICAgIHJldHVyblxuICAgICAgfVxuICAgIH1cblxuICAgIGRpc3BhdGNoTmF2aWdhdGVBY3Rpb24oXG4gICAgICBhcyB8fCBocmVmLFxuICAgICAgcmVwbGFjZSA/ICdyZXBsYWNlJyA6ICdwdXNoJyxcbiAgICAgIHNjcm9sbCA/PyB0cnVlLFxuICAgICAgbGlua0luc3RhbmNlUmVmLmN1cnJlbnRcbiAgICApXG4gIH1cblxuICBSZWFjdC5zdGFydFRyYW5zaXRpb24obmF2aWdhdGUpXG59XG5cbmZ1bmN0aW9uIGZvcm1hdFN0cmluZ09yVXJsKHVybE9iak9yU3RyaW5nOiBVcmxPYmplY3QgfCBzdHJpbmcpOiBzdHJpbmcge1xuICBpZiAodHlwZW9mIHVybE9iak9yU3RyaW5nID09PSAnc3RyaW5nJykge1xuICAgIHJldHVybiB1cmxPYmpPclN0cmluZ1xuICB9XG5cbiAgcmV0dXJuIGZvcm1hdFVybCh1cmxPYmpPclN0cmluZylcbn1cblxuLyoqXG4gKiBBIFJlYWN0IGNvbXBvbmVudCB0aGF0IGV4dGVuZHMgdGhlIEhUTUwgYDxhPmAgZWxlbWVudCB0byBwcm92aWRlXG4gKiBbcHJlZmV0Y2hpbmddKGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL2FwcC9idWlsZGluZy15b3VyLWFwcGxpY2F0aW9uL3JvdXRpbmcvbGlua2luZy1hbmQtbmF2aWdhdGluZyMyLXByZWZldGNoaW5nKVxuICogYW5kIGNsaWVudC1zaWRlIG5hdmlnYXRpb24uIFRoaXMgaXMgdGhlIHByaW1hcnkgd2F5IHRvIG5hdmlnYXRlIGJldHdlZW4gcm91dGVzIGluIE5leHQuanMuXG4gKlxuICogQHJlbWFya3NcbiAqIC0gUHJlZmV0Y2hpbmcgaXMgb25seSBlbmFibGVkIGluIHByb2R1Y3Rpb24uXG4gKlxuICogQHNlZSBodHRwczovL25leHRqcy5vcmcvZG9jcy9hcHAvYXBpLXJlZmVyZW5jZS9jb21wb25lbnRzL2xpbmtcbiAqL1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTGlua0NvbXBvbmVudChcbiAgcHJvcHM6IExpbmtQcm9wcyAmIHtcbiAgICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG4gICAgcmVmOiBSZWFjdC5SZWY8SFRNTEFuY2hvckVsZW1lbnQ+XG4gIH1cbikge1xuICBjb25zdCBbbGlua1N0YXR1cywgc2V0T3B0aW1pc3RpY0xpbmtTdGF0dXNdID0gdXNlT3B0aW1pc3RpYyhJRExFX0xJTktfU1RBVFVTKVxuXG4gIGxldCBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG5cbiAgY29uc3QgbGlua0luc3RhbmNlUmVmID0gdXNlUmVmPExpbmtJbnN0YW5jZSB8IG51bGw+KG51bGwpXG5cbiAgY29uc3Qge1xuICAgIGhyZWY6IGhyZWZQcm9wLFxuICAgIGFzOiBhc1Byb3AsXG4gICAgY2hpbGRyZW46IGNoaWxkcmVuUHJvcCxcbiAgICBwcmVmZXRjaDogcHJlZmV0Y2hQcm9wID0gbnVsbCxcbiAgICBwYXNzSHJlZixcbiAgICByZXBsYWNlLFxuICAgIHNoYWxsb3csXG4gICAgc2Nyb2xsLFxuICAgIG9uQ2xpY2ssXG4gICAgb25Nb3VzZUVudGVyOiBvbk1vdXNlRW50ZXJQcm9wLFxuICAgIG9uVG91Y2hTdGFydDogb25Ub3VjaFN0YXJ0UHJvcCxcbiAgICBsZWdhY3lCZWhhdmlvciA9IGZhbHNlLFxuICAgIG9uTmF2aWdhdGUsXG4gICAgcmVmOiBmb3J3YXJkZWRSZWYsXG4gICAgdW5zdGFibGVfZHluYW1pY09uSG92ZXIsXG4gICAgLi4ucmVzdFByb3BzXG4gIH0gPSBwcm9wc1xuXG4gIGNoaWxkcmVuID0gY2hpbGRyZW5Qcm9wXG5cbiAgaWYgKFxuICAgIGxlZ2FjeUJlaGF2aW9yICYmXG4gICAgKHR5cGVvZiBjaGlsZHJlbiA9PT0gJ3N0cmluZycgfHwgdHlwZW9mIGNoaWxkcmVuID09PSAnbnVtYmVyJylcbiAgKSB7XG4gICAgY2hpbGRyZW4gPSA8YT57Y2hpbGRyZW59PC9hPlxuICB9XG5cbiAgY29uc3Qgcm91dGVyID0gUmVhY3QudXNlQ29udGV4dChBcHBSb3V0ZXJDb250ZXh0KVxuXG4gIGNvbnN0IHByZWZldGNoRW5hYmxlZCA9IHByZWZldGNoUHJvcCAhPT0gZmFsc2VcbiAgLyoqXG4gICAqIFRoZSBwb3NzaWJsZSBzdGF0ZXMgZm9yIHByZWZldGNoIGFyZTpcbiAgICogLSBudWxsOiB0aGlzIGlzIHRoZSBkZWZhdWx0IFwiYXV0b1wiIG1vZGUsIHdoZXJlIHdlIHdpbGwgcHJlZmV0Y2ggcGFydGlhbGx5IGlmIHRoZSBsaW5rIGlzIGluIHRoZSB2aWV3cG9ydFxuICAgKiAtIHRydWU6IHdlIHdpbGwgcHJlZmV0Y2ggaWYgdGhlIGxpbmsgaXMgdmlzaWJsZSBhbmQgcHJlZmV0Y2ggdGhlIGZ1bGwgcGFnZSwgbm90IGp1c3QgcGFydGlhbGx5XG4gICAqIC0gZmFsc2U6IHdlIHdpbGwgbm90IHByZWZldGNoIGlmIGluIHRoZSB2aWV3cG9ydCBhdCBhbGxcbiAgICogLSAndW5zdGFibGVfZHluYW1pY09uSG92ZXInOiB0aGlzIHN0YXJ0cyBpbiBcImF1dG9cIiBtb2RlLCBidXQgc3dpdGNoZXMgdG8gXCJmdWxsXCIgd2hlbiB0aGUgbGluayBpcyBob3ZlcmVkXG4gICAqL1xuICBjb25zdCBhcHBQcmVmZXRjaEtpbmQgPVxuICAgIHByZWZldGNoUHJvcCA9PT0gbnVsbCA/IFByZWZldGNoS2luZC5BVVRPIDogUHJlZmV0Y2hLaW5kLkZVTExcblxuICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICAgIGZ1bmN0aW9uIGNyZWF0ZVByb3BFcnJvcihhcmdzOiB7XG4gICAgICBrZXk6IHN0cmluZ1xuICAgICAgZXhwZWN0ZWQ6IHN0cmluZ1xuICAgICAgYWN0dWFsOiBzdHJpbmdcbiAgICB9KSB7XG4gICAgICByZXR1cm4gbmV3IEVycm9yKFxuICAgICAgICBgRmFpbGVkIHByb3AgdHlwZTogVGhlIHByb3AgXFxgJHthcmdzLmtleX1cXGAgZXhwZWN0cyBhICR7YXJncy5leHBlY3RlZH0gaW4gXFxgPExpbms+XFxgLCBidXQgZ290IFxcYCR7YXJncy5hY3R1YWx9XFxgIGluc3RlYWQuYCArXG4gICAgICAgICAgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnXG4gICAgICAgICAgICA/IFwiXFxuT3BlbiB5b3VyIGJyb3dzZXIncyBjb25zb2xlIHRvIHZpZXcgdGhlIENvbXBvbmVudCBzdGFjayB0cmFjZS5cIlxuICAgICAgICAgICAgOiAnJylcbiAgICAgIClcbiAgICB9XG5cbiAgICAvLyBUeXBlU2NyaXB0IHRyaWNrIGZvciB0eXBlLWd1YXJkaW5nOlxuICAgIGNvbnN0IHJlcXVpcmVkUHJvcHNHdWFyZDogUmVjb3JkPExpbmtQcm9wc1JlcXVpcmVkLCB0cnVlPiA9IHtcbiAgICAgIGhyZWY6IHRydWUsXG4gICAgfSBhcyBjb25zdFxuICAgIGNvbnN0IHJlcXVpcmVkUHJvcHM6IExpbmtQcm9wc1JlcXVpcmVkW10gPSBPYmplY3Qua2V5cyhcbiAgICAgIHJlcXVpcmVkUHJvcHNHdWFyZFxuICAgICkgYXMgTGlua1Byb3BzUmVxdWlyZWRbXVxuICAgIHJlcXVpcmVkUHJvcHMuZm9yRWFjaCgoa2V5OiBMaW5rUHJvcHNSZXF1aXJlZCkgPT4ge1xuICAgICAgaWYgKGtleSA9PT0gJ2hyZWYnKSB7XG4gICAgICAgIGlmIChcbiAgICAgICAgICBwcm9wc1trZXldID09IG51bGwgfHxcbiAgICAgICAgICAodHlwZW9mIHByb3BzW2tleV0gIT09ICdzdHJpbmcnICYmIHR5cGVvZiBwcm9wc1trZXldICE9PSAnb2JqZWN0JylcbiAgICAgICAgKSB7XG4gICAgICAgICAgdGhyb3cgY3JlYXRlUHJvcEVycm9yKHtcbiAgICAgICAgICAgIGtleSxcbiAgICAgICAgICAgIGV4cGVjdGVkOiAnYHN0cmluZ2Agb3IgYG9iamVjdGAnLFxuICAgICAgICAgICAgYWN0dWFsOiBwcm9wc1trZXldID09PSBudWxsID8gJ251bGwnIDogdHlwZW9mIHByb3BzW2tleV0sXG4gICAgICAgICAgfSlcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gVHlwZVNjcmlwdCB0cmljayBmb3IgdHlwZS1ndWFyZGluZzpcbiAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9uby11bnVzZWQtdmFyc1xuICAgICAgICBjb25zdCBfOiBuZXZlciA9IGtleVxuICAgICAgfVxuICAgIH0pXG5cbiAgICAvLyBUeXBlU2NyaXB0IHRyaWNrIGZvciB0eXBlLWd1YXJkaW5nOlxuICAgIGNvbnN0IG9wdGlvbmFsUHJvcHNHdWFyZDogUmVjb3JkPExpbmtQcm9wc09wdGlvbmFsLCB0cnVlPiA9IHtcbiAgICAgIGFzOiB0cnVlLFxuICAgICAgcmVwbGFjZTogdHJ1ZSxcbiAgICAgIHNjcm9sbDogdHJ1ZSxcbiAgICAgIHNoYWxsb3c6IHRydWUsXG4gICAgICBwYXNzSHJlZjogdHJ1ZSxcbiAgICAgIHByZWZldGNoOiB0cnVlLFxuICAgICAgdW5zdGFibGVfZHluYW1pY09uSG92ZXI6IHRydWUsXG4gICAgICBvbkNsaWNrOiB0cnVlLFxuICAgICAgb25Nb3VzZUVudGVyOiB0cnVlLFxuICAgICAgb25Ub3VjaFN0YXJ0OiB0cnVlLFxuICAgICAgbGVnYWN5QmVoYXZpb3I6IHRydWUsXG4gICAgICBvbk5hdmlnYXRlOiB0cnVlLFxuICAgIH0gYXMgY29uc3RcbiAgICBjb25zdCBvcHRpb25hbFByb3BzOiBMaW5rUHJvcHNPcHRpb25hbFtdID0gT2JqZWN0LmtleXMoXG4gICAgICBvcHRpb25hbFByb3BzR3VhcmRcbiAgICApIGFzIExpbmtQcm9wc09wdGlvbmFsW11cbiAgICBvcHRpb25hbFByb3BzLmZvckVhY2goKGtleTogTGlua1Byb3BzT3B0aW9uYWwpID0+IHtcbiAgICAgIGNvbnN0IHZhbFR5cGUgPSB0eXBlb2YgcHJvcHNba2V5XVxuXG4gICAgICBpZiAoa2V5ID09PSAnYXMnKSB7XG4gICAgICAgIGlmIChwcm9wc1trZXldICYmIHZhbFR5cGUgIT09ICdzdHJpbmcnICYmIHZhbFR5cGUgIT09ICdvYmplY3QnKSB7XG4gICAgICAgICAgdGhyb3cgY3JlYXRlUHJvcEVycm9yKHtcbiAgICAgICAgICAgIGtleSxcbiAgICAgICAgICAgIGV4cGVjdGVkOiAnYHN0cmluZ2Agb3IgYG9iamVjdGAnLFxuICAgICAgICAgICAgYWN0dWFsOiB2YWxUeXBlLFxuICAgICAgICAgIH0pXG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSBpZiAoXG4gICAgICAgIGtleSA9PT0gJ29uQ2xpY2snIHx8XG4gICAgICAgIGtleSA9PT0gJ29uTW91c2VFbnRlcicgfHxcbiAgICAgICAga2V5ID09PSAnb25Ub3VjaFN0YXJ0JyB8fFxuICAgICAgICBrZXkgPT09ICdvbk5hdmlnYXRlJ1xuICAgICAgKSB7XG4gICAgICAgIGlmIChwcm9wc1trZXldICYmIHZhbFR5cGUgIT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgICB0aHJvdyBjcmVhdGVQcm9wRXJyb3Ioe1xuICAgICAgICAgICAga2V5LFxuICAgICAgICAgICAgZXhwZWN0ZWQ6ICdgZnVuY3Rpb25gJyxcbiAgICAgICAgICAgIGFjdHVhbDogdmFsVHlwZSxcbiAgICAgICAgICB9KVxuICAgICAgICB9XG4gICAgICB9IGVsc2UgaWYgKFxuICAgICAgICBrZXkgPT09ICdyZXBsYWNlJyB8fFxuICAgICAgICBrZXkgPT09ICdzY3JvbGwnIHx8XG4gICAgICAgIGtleSA9PT0gJ3NoYWxsb3cnIHx8XG4gICAgICAgIGtleSA9PT0gJ3Bhc3NIcmVmJyB8fFxuICAgICAgICBrZXkgPT09ICdwcmVmZXRjaCcgfHxcbiAgICAgICAga2V5ID09PSAnbGVnYWN5QmVoYXZpb3InIHx8XG4gICAgICAgIGtleSA9PT0gJ3Vuc3RhYmxlX2R5bmFtaWNPbkhvdmVyJ1xuICAgICAgKSB7XG4gICAgICAgIGlmIChwcm9wc1trZXldICE9IG51bGwgJiYgdmFsVHlwZSAhPT0gJ2Jvb2xlYW4nKSB7XG4gICAgICAgICAgdGhyb3cgY3JlYXRlUHJvcEVycm9yKHtcbiAgICAgICAgICAgIGtleSxcbiAgICAgICAgICAgIGV4cGVjdGVkOiAnYGJvb2xlYW5gJyxcbiAgICAgICAgICAgIGFjdHVhbDogdmFsVHlwZSxcbiAgICAgICAgICB9KVxuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBUeXBlU2NyaXB0IHRyaWNrIGZvciB0eXBlLWd1YXJkaW5nOlxuICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L25vLXVudXNlZC12YXJzXG4gICAgICAgIGNvbnN0IF86IG5ldmVyID0ga2V5XG4gICAgICB9XG4gICAgfSlcbiAgfVxuXG4gIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gICAgaWYgKHByb3BzLmxvY2FsZSkge1xuICAgICAgd2Fybk9uY2UoXG4gICAgICAgICdUaGUgYGxvY2FsZWAgcHJvcCBpcyBub3Qgc3VwcG9ydGVkIGluIGBuZXh0L2xpbmtgIHdoaWxlIHVzaW5nIHRoZSBgYXBwYCByb3V0ZXIuIFJlYWQgbW9yZSBhYm91dCBhcHAgcm91dGVyIGludGVybmFsaXphdGlvbjogaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBwL2J1aWxkaW5nLXlvdXItYXBwbGljYXRpb24vcm91dGluZy9pbnRlcm5hdGlvbmFsaXphdGlvbidcbiAgICAgIClcbiAgICB9XG4gICAgaWYgKCFhc1Byb3ApIHtcbiAgICAgIGxldCBocmVmOiBzdHJpbmcgfCB1bmRlZmluZWRcbiAgICAgIGlmICh0eXBlb2YgaHJlZlByb3AgPT09ICdzdHJpbmcnKSB7XG4gICAgICAgIGhyZWYgPSBocmVmUHJvcFxuICAgICAgfSBlbHNlIGlmIChcbiAgICAgICAgdHlwZW9mIGhyZWZQcm9wID09PSAnb2JqZWN0JyAmJlxuICAgICAgICB0eXBlb2YgaHJlZlByb3AucGF0aG5hbWUgPT09ICdzdHJpbmcnXG4gICAgICApIHtcbiAgICAgICAgaHJlZiA9IGhyZWZQcm9wLnBhdGhuYW1lXG4gICAgICB9XG5cbiAgICAgIGlmIChocmVmKSB7XG4gICAgICAgIGNvbnN0IGhhc0R5bmFtaWNTZWdtZW50ID0gaHJlZlxuICAgICAgICAgIC5zcGxpdCgnLycpXG4gICAgICAgICAgLnNvbWUoKHNlZ21lbnQpID0+IHNlZ21lbnQuc3RhcnRzV2l0aCgnWycpICYmIHNlZ21lbnQuZW5kc1dpdGgoJ10nKSlcblxuICAgICAgICBpZiAoaGFzRHluYW1pY1NlZ21lbnQpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgICAgICBgRHluYW1pYyBocmVmIFxcYCR7aHJlZn1cXGAgZm91bmQgaW4gPExpbms+IHdoaWxlIHVzaW5nIHRoZSBcXGAvYXBwXFxgIHJvdXRlciwgdGhpcyBpcyBub3Qgc3VwcG9ydGVkLiBSZWFkIG1vcmU6IGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL21lc3NhZ2VzL2FwcC1kaXItZHluYW1pYy1ocmVmYFxuICAgICAgICAgIClcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IHsgaHJlZiwgYXMgfSA9IFJlYWN0LnVzZU1lbW8oKCkgPT4ge1xuICAgIGNvbnN0IHJlc29sdmVkSHJlZiA9IGZvcm1hdFN0cmluZ09yVXJsKGhyZWZQcm9wKVxuICAgIHJldHVybiB7XG4gICAgICBocmVmOiByZXNvbHZlZEhyZWYsXG4gICAgICBhczogYXNQcm9wID8gZm9ybWF0U3RyaW5nT3JVcmwoYXNQcm9wKSA6IHJlc29sdmVkSHJlZixcbiAgICB9XG4gIH0sIFtocmVmUHJvcCwgYXNQcm9wXSlcblxuICAvLyBUaGlzIHdpbGwgcmV0dXJuIHRoZSBmaXJzdCBjaGlsZCwgaWYgbXVsdGlwbGUgYXJlIHByb3ZpZGVkIGl0IHdpbGwgdGhyb3cgYW4gZXJyb3JcbiAgbGV0IGNoaWxkOiBhbnlcbiAgaWYgKGxlZ2FjeUJlaGF2aW9yKSB7XG4gICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnKSB7XG4gICAgICBpZiAob25DbGljaykge1xuICAgICAgICBjb25zb2xlLndhcm4oXG4gICAgICAgICAgYFwib25DbGlja1wiIHdhcyBwYXNzZWQgdG8gPExpbms+IHdpdGggXFxgaHJlZlxcYCBvZiBcXGAke2hyZWZQcm9wfVxcYCBidXQgXCJsZWdhY3lCZWhhdmlvclwiIHdhcyBzZXQuIFRoZSBsZWdhY3kgYmVoYXZpb3IgcmVxdWlyZXMgb25DbGljayBiZSBzZXQgb24gdGhlIGNoaWxkIG9mIG5leHQvbGlua2BcbiAgICAgICAgKVxuICAgICAgfVxuICAgICAgaWYgKG9uTW91c2VFbnRlclByb3ApIHtcbiAgICAgICAgY29uc29sZS53YXJuKFxuICAgICAgICAgIGBcIm9uTW91c2VFbnRlclwiIHdhcyBwYXNzZWQgdG8gPExpbms+IHdpdGggXFxgaHJlZlxcYCBvZiBcXGAke2hyZWZQcm9wfVxcYCBidXQgXCJsZWdhY3lCZWhhdmlvclwiIHdhcyBzZXQuIFRoZSBsZWdhY3kgYmVoYXZpb3IgcmVxdWlyZXMgb25Nb3VzZUVudGVyIGJlIHNldCBvbiB0aGUgY2hpbGQgb2YgbmV4dC9saW5rYFxuICAgICAgICApXG4gICAgICB9XG4gICAgICB0cnkge1xuICAgICAgICBjaGlsZCA9IFJlYWN0LkNoaWxkcmVuLm9ubHkoY2hpbGRyZW4pXG4gICAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgICAgaWYgKCFjaGlsZHJlbikge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICAgICAgIGBObyBjaGlsZHJlbiB3ZXJlIHBhc3NlZCB0byA8TGluaz4gd2l0aCBcXGBocmVmXFxgIG9mIFxcYCR7aHJlZlByb3B9XFxgIGJ1dCBvbmUgY2hpbGQgaXMgcmVxdWlyZWQgaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvbWVzc2FnZXMvbGluay1uby1jaGlsZHJlbmBcbiAgICAgICAgICApXG4gICAgICAgIH1cbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgICAgIGBNdWx0aXBsZSBjaGlsZHJlbiB3ZXJlIHBhc3NlZCB0byA8TGluaz4gd2l0aCBcXGBocmVmXFxgIG9mIFxcYCR7aHJlZlByb3B9XFxgIGJ1dCBvbmx5IG9uZSBjaGlsZCBpcyBzdXBwb3J0ZWQgaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvbWVzc2FnZXMvbGluay1tdWx0aXBsZS1jaGlsZHJlbmAgK1xuICAgICAgICAgICAgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnXG4gICAgICAgICAgICAgID8gXCIgXFxuT3BlbiB5b3VyIGJyb3dzZXIncyBjb25zb2xlIHRvIHZpZXcgdGhlIENvbXBvbmVudCBzdGFjayB0cmFjZS5cIlxuICAgICAgICAgICAgICA6ICcnKVxuICAgICAgICApXG4gICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgIGNoaWxkID0gUmVhY3QuQ2hpbGRyZW4ub25seShjaGlsZHJlbilcbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnKSB7XG4gICAgICBpZiAoKGNoaWxkcmVuIGFzIGFueSk/LnR5cGUgPT09ICdhJykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgICAgJ0ludmFsaWQgPExpbms+IHdpdGggPGE+IGNoaWxkLiBQbGVhc2UgcmVtb3ZlIDxhPiBvciB1c2UgPExpbmsgbGVnYWN5QmVoYXZpb3I+LlxcbkxlYXJuIG1vcmU6IGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL21lc3NhZ2VzL2ludmFsaWQtbmV3LWxpbmstd2l0aC1leHRyYS1hbmNob3InXG4gICAgICAgIClcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICBjb25zdCBjaGlsZFJlZjogYW55ID0gbGVnYWN5QmVoYXZpb3JcbiAgICA/IGNoaWxkICYmIHR5cGVvZiBjaGlsZCA9PT0gJ29iamVjdCcgJiYgY2hpbGQucmVmXG4gICAgOiBmb3J3YXJkZWRSZWZcblxuICAvLyBVc2UgYSBjYWxsYmFjayByZWYgdG8gYXR0YWNoIGFuIEludGVyc2VjdGlvbk9ic2VydmVyIHRvIHRoZSBhbmNob3IgdGFnIG9uXG4gIC8vIG1vdW50LiBJbiB0aGUgZnV0dXJlIHdlIHdpbGwgYWxzbyB1c2UgdGhpcyB0byBrZWVwIHRyYWNrIG9mIGFsbCB0aGVcbiAgLy8gY3VycmVudGx5IG1vdW50ZWQgPExpbms+IGluc3RhbmNlcywgZS5nLiBzbyB3ZSBjYW4gcmUtcHJlZmV0Y2ggdGhlbSBhZnRlclxuICAvLyBhIHJldmFsaWRhdGlvbiBvciByZWZyZXNoLlxuICBjb25zdCBvYnNlcnZlTGlua1Zpc2liaWxpdHlPbk1vdW50ID0gUmVhY3QudXNlQ2FsbGJhY2soXG4gICAgKGVsZW1lbnQ6IEhUTUxBbmNob3JFbGVtZW50IHwgU1ZHQUVsZW1lbnQpID0+IHtcbiAgICAgIGlmIChyb3V0ZXIgIT09IG51bGwpIHtcbiAgICAgICAgbGlua0luc3RhbmNlUmVmLmN1cnJlbnQgPSBtb3VudExpbmtJbnN0YW5jZShcbiAgICAgICAgICBlbGVtZW50LFxuICAgICAgICAgIGhyZWYsXG4gICAgICAgICAgcm91dGVyLFxuICAgICAgICAgIGFwcFByZWZldGNoS2luZCxcbiAgICAgICAgICBwcmVmZXRjaEVuYWJsZWQsXG4gICAgICAgICAgc2V0T3B0aW1pc3RpY0xpbmtTdGF0dXNcbiAgICAgICAgKVxuICAgICAgfVxuXG4gICAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgICBpZiAobGlua0luc3RhbmNlUmVmLmN1cnJlbnQpIHtcbiAgICAgICAgICB1bm1vdW50TGlua0ZvckN1cnJlbnROYXZpZ2F0aW9uKGxpbmtJbnN0YW5jZVJlZi5jdXJyZW50KVxuICAgICAgICAgIGxpbmtJbnN0YW5jZVJlZi5jdXJyZW50ID0gbnVsbFxuICAgICAgICB9XG4gICAgICAgIHVubW91bnRQcmVmZXRjaGFibGVJbnN0YW5jZShlbGVtZW50KVxuICAgICAgfVxuICAgIH0sXG4gICAgW3ByZWZldGNoRW5hYmxlZCwgaHJlZiwgcm91dGVyLCBhcHBQcmVmZXRjaEtpbmQsIHNldE9wdGltaXN0aWNMaW5rU3RhdHVzXVxuICApXG5cbiAgY29uc3QgbWVyZ2VkUmVmID0gdXNlTWVyZ2VkUmVmKG9ic2VydmVMaW5rVmlzaWJpbGl0eU9uTW91bnQsIGNoaWxkUmVmKVxuXG4gIGNvbnN0IGNoaWxkUHJvcHM6IHtcbiAgICBvblRvdWNoU3RhcnQ/OiBSZWFjdC5Ub3VjaEV2ZW50SGFuZGxlcjxIVE1MQW5jaG9yRWxlbWVudD5cbiAgICBvbk1vdXNlRW50ZXI6IFJlYWN0Lk1vdXNlRXZlbnRIYW5kbGVyPEhUTUxBbmNob3JFbGVtZW50PlxuICAgIG9uQ2xpY2s6IFJlYWN0Lk1vdXNlRXZlbnRIYW5kbGVyPEhUTUxBbmNob3JFbGVtZW50PlxuICAgIGhyZWY/OiBzdHJpbmdcbiAgICByZWY/OiBhbnlcbiAgfSA9IHtcbiAgICByZWY6IG1lcmdlZFJlZixcbiAgICBvbkNsaWNrKGUpIHtcbiAgICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gICAgICAgIGlmICghZSkge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICAgICAgIGBDb21wb25lbnQgcmVuZGVyZWQgaW5zaWRlIG5leHQvbGluayBoYXMgdG8gcGFzcyBjbGljayBldmVudCB0byBcIm9uQ2xpY2tcIiBwcm9wLmBcbiAgICAgICAgICApXG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgaWYgKCFsZWdhY3lCZWhhdmlvciAmJiB0eXBlb2Ygb25DbGljayA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICBvbkNsaWNrKGUpXG4gICAgICB9XG5cbiAgICAgIGlmIChcbiAgICAgICAgbGVnYWN5QmVoYXZpb3IgJiZcbiAgICAgICAgY2hpbGQucHJvcHMgJiZcbiAgICAgICAgdHlwZW9mIGNoaWxkLnByb3BzLm9uQ2xpY2sgPT09ICdmdW5jdGlvbidcbiAgICAgICkge1xuICAgICAgICBjaGlsZC5wcm9wcy5vbkNsaWNrKGUpXG4gICAgICB9XG5cbiAgICAgIGlmICghcm91dGVyKSB7XG4gICAgICAgIHJldHVyblxuICAgICAgfVxuXG4gICAgICBpZiAoZS5kZWZhdWx0UHJldmVudGVkKSB7XG4gICAgICAgIHJldHVyblxuICAgICAgfVxuXG4gICAgICBsaW5rQ2xpY2tlZChlLCBocmVmLCBhcywgbGlua0luc3RhbmNlUmVmLCByZXBsYWNlLCBzY3JvbGwsIG9uTmF2aWdhdGUpXG4gICAgfSxcbiAgICBvbk1vdXNlRW50ZXIoZSkge1xuICAgICAgaWYgKCFsZWdhY3lCZWhhdmlvciAmJiB0eXBlb2Ygb25Nb3VzZUVudGVyUHJvcCA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICBvbk1vdXNlRW50ZXJQcm9wKGUpXG4gICAgICB9XG5cbiAgICAgIGlmIChcbiAgICAgICAgbGVnYWN5QmVoYXZpb3IgJiZcbiAgICAgICAgY2hpbGQucHJvcHMgJiZcbiAgICAgICAgdHlwZW9mIGNoaWxkLnByb3BzLm9uTW91c2VFbnRlciA9PT0gJ2Z1bmN0aW9uJ1xuICAgICAgKSB7XG4gICAgICAgIGNoaWxkLnByb3BzLm9uTW91c2VFbnRlcihlKVxuICAgICAgfVxuXG4gICAgICBpZiAoIXJvdXRlcikge1xuICAgICAgICByZXR1cm5cbiAgICAgIH1cblxuICAgICAgaWYgKCFwcmVmZXRjaEVuYWJsZWQgfHwgcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcpIHtcbiAgICAgICAgcmV0dXJuXG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHVwZ3JhZGVUb0R5bmFtaWNQcmVmZXRjaCA9IHVuc3RhYmxlX2R5bmFtaWNPbkhvdmVyID09PSB0cnVlXG4gICAgICBvbk5hdmlnYXRpb25JbnRlbnQoXG4gICAgICAgIGUuY3VycmVudFRhcmdldCBhcyBIVE1MQW5jaG9yRWxlbWVudCB8IFNWR0FFbGVtZW50LFxuICAgICAgICB1cGdyYWRlVG9EeW5hbWljUHJlZmV0Y2hcbiAgICAgIClcbiAgICB9LFxuICAgIG9uVG91Y2hTdGFydDogcHJvY2Vzcy5lbnYuX19ORVhUX0xJTktfTk9fVE9VQ0hfU1RBUlRcbiAgICAgID8gdW5kZWZpbmVkXG4gICAgICA6IGZ1bmN0aW9uIG9uVG91Y2hTdGFydChlKSB7XG4gICAgICAgICAgaWYgKCFsZWdhY3lCZWhhdmlvciAmJiB0eXBlb2Ygb25Ub3VjaFN0YXJ0UHJvcCA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICAgICAgb25Ub3VjaFN0YXJ0UHJvcChlKVxuICAgICAgICAgIH1cblxuICAgICAgICAgIGlmIChcbiAgICAgICAgICAgIGxlZ2FjeUJlaGF2aW9yICYmXG4gICAgICAgICAgICBjaGlsZC5wcm9wcyAmJlxuICAgICAgICAgICAgdHlwZW9mIGNoaWxkLnByb3BzLm9uVG91Y2hTdGFydCA9PT0gJ2Z1bmN0aW9uJ1xuICAgICAgICAgICkge1xuICAgICAgICAgICAgY2hpbGQucHJvcHMub25Ub3VjaFN0YXJ0KGUpXG4gICAgICAgICAgfVxuXG4gICAgICAgICAgaWYgKCFyb3V0ZXIpIHtcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICAgIH1cblxuICAgICAgICAgIGlmICghcHJlZmV0Y2hFbmFibGVkKSB7XG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgICB9XG5cbiAgICAgICAgICBjb25zdCB1cGdyYWRlVG9EeW5hbWljUHJlZmV0Y2ggPSB1bnN0YWJsZV9keW5hbWljT25Ib3ZlciA9PT0gdHJ1ZVxuICAgICAgICAgIG9uTmF2aWdhdGlvbkludGVudChcbiAgICAgICAgICAgIGUuY3VycmVudFRhcmdldCBhcyBIVE1MQW5jaG9yRWxlbWVudCB8IFNWR0FFbGVtZW50LFxuICAgICAgICAgICAgdXBncmFkZVRvRHluYW1pY1ByZWZldGNoXG4gICAgICAgICAgKVxuICAgICAgICB9LFxuICB9XG5cbiAgLy8gSWYgY2hpbGQgaXMgYW4gPGE+IHRhZyBhbmQgZG9lc24ndCBoYXZlIGEgaHJlZiBhdHRyaWJ1dGUsIG9yIGlmIHRoZSAncGFzc0hyZWYnIHByb3BlcnR5IGlzXG4gIC8vIGRlZmluZWQsIHdlIHNwZWNpZnkgdGhlIGN1cnJlbnQgJ2hyZWYnLCBzbyB0aGF0IHJlcGV0aXRpb24gaXMgbm90IG5lZWRlZCBieSB0aGUgdXNlci5cbiAgLy8gSWYgdGhlIHVybCBpcyBhYnNvbHV0ZSwgd2UgY2FuIGJ5cGFzcyB0aGUgbG9naWMgdG8gcHJlcGVuZCB0aGUgYmFzZVBhdGguXG4gIGlmIChpc0Fic29sdXRlVXJsKGFzKSkge1xuICAgIGNoaWxkUHJvcHMuaHJlZiA9IGFzXG4gIH0gZWxzZSBpZiAoXG4gICAgIWxlZ2FjeUJlaGF2aW9yIHx8XG4gICAgcGFzc0hyZWYgfHxcbiAgICAoY2hpbGQudHlwZSA9PT0gJ2EnICYmICEoJ2hyZWYnIGluIGNoaWxkLnByb3BzKSlcbiAgKSB7XG4gICAgY2hpbGRQcm9wcy5ocmVmID0gYWRkQmFzZVBhdGgoYXMpXG4gIH1cblxuICBsZXQgbGluazogUmVhY3QuUmVhY3ROb2RlXG5cbiAgaWYgKGxlZ2FjeUJlaGF2aW9yKSB7XG4gICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnKSB7XG4gICAgICBlcnJvck9uY2UoXG4gICAgICAgICdgbGVnYWN5QmVoYXZpb3JgIGlzIGRlcHJlY2F0ZWQgYW5kIHdpbGwgYmUgcmVtb3ZlZCBpbiBhIGZ1dHVyZSAnICtcbiAgICAgICAgICAncmVsZWFzZS4gQSBjb2RlbW9kIGlzIGF2YWlsYWJsZSB0byB1cGdyYWRlIHlvdXIgY29tcG9uZW50czpcXG5cXG4nICtcbiAgICAgICAgICAnbnB4IEBuZXh0L2NvZGVtb2RAbGF0ZXN0IG5ldy1saW5rIC5cXG5cXG4nICtcbiAgICAgICAgICAnTGVhcm4gbW9yZTogaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBwL2J1aWxkaW5nLXlvdXItYXBwbGljYXRpb24vdXBncmFkaW5nL2NvZGVtb2RzI3JlbW92ZS1hLXRhZ3MtZnJvbS1saW5rLWNvbXBvbmVudHMnXG4gICAgICApXG4gICAgfVxuICAgIGxpbmsgPSBSZWFjdC5jbG9uZUVsZW1lbnQoY2hpbGQsIGNoaWxkUHJvcHMpXG4gIH0gZWxzZSB7XG4gICAgbGluayA9IChcbiAgICAgIDxhIHsuLi5yZXN0UHJvcHN9IHsuLi5jaGlsZFByb3BzfT5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9hPlxuICAgIClcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPExpbmtTdGF0dXNDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXtsaW5rU3RhdHVzfT5cbiAgICAgIHtsaW5rfVxuICAgIDwvTGlua1N0YXR1c0NvbnRleHQuUHJvdmlkZXI+XG4gIClcbn1cblxuY29uc3QgTGlua1N0YXR1c0NvbnRleHQgPSBjcmVhdGVDb250ZXh0PFxuICB0eXBlb2YgUEVORElOR19MSU5LX1NUQVRVUyB8IHR5cGVvZiBJRExFX0xJTktfU1RBVFVTXG4+KElETEVfTElOS19TVEFUVVMpXG5cbmV4cG9ydCBjb25zdCB1c2VMaW5rU3RhdHVzID0gKCkgPT4ge1xuICByZXR1cm4gdXNlQ29udGV4dChMaW5rU3RhdHVzQ29udGV4dClcbn1cbiJdLCJuYW1lcyI6WyJMaW5rQ29tcG9uZW50IiwidXNlTGlua1N0YXR1cyIsImlzTW9kaWZpZWRFdmVudCIsImV2ZW50IiwiZXZlbnRUYXJnZXQiLCJjdXJyZW50VGFyZ2V0IiwidGFyZ2V0IiwiZ2V0QXR0cmlidXRlIiwibWV0YUtleSIsImN0cmxLZXkiLCJzaGlmdEtleSIsImFsdEtleSIsIm5hdGl2ZUV2ZW50Iiwid2hpY2giLCJsaW5rQ2xpY2tlZCIsImUiLCJocmVmIiwiYXMiLCJsaW5rSW5zdGFuY2VSZWYiLCJyZXBsYWNlIiwic2Nyb2xsIiwib25OYXZpZ2F0ZSIsIm5vZGVOYW1lIiwiaXNBbmNob3JOb2RlTmFtZSIsInRvVXBwZXJDYXNlIiwiaGFzQXR0cmlidXRlIiwiaXNMb2NhbFVSTCIsInByZXZlbnREZWZhdWx0IiwibG9jYXRpb24iLCJuYXZpZ2F0ZSIsImlzRGVmYXVsdFByZXZlbnRlZCIsImRpc3BhdGNoTmF2aWdhdGVBY3Rpb24iLCJjdXJyZW50IiwiUmVhY3QiLCJzdGFydFRyYW5zaXRpb24iLCJmb3JtYXRTdHJpbmdPclVybCIsInVybE9iak9yU3RyaW5nIiwiZm9ybWF0VXJsIiwicHJvcHMiLCJsaW5rU3RhdHVzIiwic2V0T3B0aW1pc3RpY0xpbmtTdGF0dXMiLCJ1c2VPcHRpbWlzdGljIiwiSURMRV9MSU5LX1NUQVRVUyIsImNoaWxkcmVuIiwidXNlUmVmIiwiaHJlZlByb3AiLCJhc1Byb3AiLCJjaGlsZHJlblByb3AiLCJwcmVmZXRjaCIsInByZWZldGNoUHJvcCIsInBhc3NIcmVmIiwic2hhbGxvdyIsIm9uQ2xpY2siLCJvbk1vdXNlRW50ZXIiLCJvbk1vdXNlRW50ZXJQcm9wIiwib25Ub3VjaFN0YXJ0Iiwib25Ub3VjaFN0YXJ0UHJvcCIsImxlZ2FjeUJlaGF2aW9yIiwicmVmIiwiZm9yd2FyZGVkUmVmIiwidW5zdGFibGVfZHluYW1pY09uSG92ZXIiLCJyZXN0UHJvcHMiLCJhIiwicm91dGVyIiwidXNlQ29udGV4dCIsIkFwcFJvdXRlckNvbnRleHQiLCJwcmVmZXRjaEVuYWJsZWQiLCJhcHBQcmVmZXRjaEtpbmQiLCJQcmVmZXRjaEtpbmQiLCJBVVRPIiwiRlVMTCIsInByb2Nlc3MiLCJlbnYiLCJOT0RFX0VOViIsImNyZWF0ZVByb3BFcnJvciIsImFyZ3MiLCJFcnJvciIsImtleSIsImV4cGVjdGVkIiwiYWN0dWFsIiwid2luZG93IiwicmVxdWlyZWRQcm9wc0d1YXJkIiwicmVxdWlyZWRQcm9wcyIsIk9iamVjdCIsImtleXMiLCJmb3JFYWNoIiwiXyIsIm9wdGlvbmFsUHJvcHNHdWFyZCIsIm9wdGlvbmFsUHJvcHMiLCJ2YWxUeXBlIiwibG9jYWxlIiwid2Fybk9uY2UiLCJwYXRobmFtZSIsImhhc0R5bmFtaWNTZWdtZW50Iiwic3BsaXQiLCJzb21lIiwic2VnbWVudCIsInN0YXJ0c1dpdGgiLCJlbmRzV2l0aCIsInVzZU1lbW8iLCJyZXNvbHZlZEhyZWYiLCJjaGlsZCIsImNvbnNvbGUiLCJ3YXJuIiwiQ2hpbGRyZW4iLCJvbmx5IiwiZXJyIiwidHlwZSIsImNoaWxkUmVmIiwib2JzZXJ2ZUxpbmtWaXNpYmlsaXR5T25Nb3VudCIsInVzZUNhbGxiYWNrIiwiZWxlbWVudCIsIm1vdW50TGlua0luc3RhbmNlIiwidW5tb3VudExpbmtGb3JDdXJyZW50TmF2aWdhdGlvbiIsInVubW91bnRQcmVmZXRjaGFibGVJbnN0YW5jZSIsIm1lcmdlZFJlZiIsInVzZU1lcmdlZFJlZiIsImNoaWxkUHJvcHMiLCJkZWZhdWx0UHJldmVudGVkIiwidXBncmFkZVRvRHluYW1pY1ByZWZldGNoIiwib25OYXZpZ2F0aW9uSW50ZW50IiwiX19ORVhUX0xJTktfTk9fVE9VQ0hfU1RBUlQiLCJ1bmRlZmluZWQiLCJpc0Fic29sdXRlVXJsIiwiYWRkQmFzZVBhdGgiLCJsaW5rIiwiZXJyb3JPbmNlIiwiY2xvbmVFbGVtZW50IiwiTGlua1N0YXR1c0NvbnRleHQiLCJQcm92aWRlciIsInZhbHVlIiwiY3JlYXRlQ29udGV4dCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/format-url.js ***!
  \**********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    formatUrl: function() {\n        return formatUrl;\n    },\n    formatWithValidation: function() {\n        return formatWithValidation;\n    },\n    urlObjectKeys: function() {\n        return urlObjectKeys;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _querystring = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ./querystring */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js\"));\nconst slashedProtocols = /https?|ftp|gopher|file/;\nfunction formatUrl(urlObj) {\n    let { auth, hostname } = urlObj;\n    let protocol = urlObj.protocol || '';\n    let pathname = urlObj.pathname || '';\n    let hash = urlObj.hash || '';\n    let query = urlObj.query || '';\n    let host = false;\n    auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : '';\n    if (urlObj.host) {\n        host = auth + urlObj.host;\n    } else if (hostname) {\n        host = auth + (~hostname.indexOf(':') ? \"[\" + hostname + \"]\" : hostname);\n        if (urlObj.port) {\n            host += ':' + urlObj.port;\n        }\n    }\n    if (query && typeof query === 'object') {\n        query = String(_querystring.urlQueryToSearchParams(query));\n    }\n    let search = urlObj.search || query && \"?\" + query || '';\n    if (protocol && !protocol.endsWith(':')) protocol += ':';\n    if (urlObj.slashes || (!protocol || slashedProtocols.test(protocol)) && host !== false) {\n        host = '//' + (host || '');\n        if (pathname && pathname[0] !== '/') pathname = '/' + pathname;\n    } else if (!host) {\n        host = '';\n    }\n    if (hash && hash[0] !== '#') hash = '#' + hash;\n    if (search && search[0] !== '?') search = '?' + search;\n    pathname = pathname.replace(/[?#]/g, encodeURIComponent);\n    search = search.replace('#', '%23');\n    return \"\" + protocol + host + pathname + search + hash;\n}\nconst urlObjectKeys = [\n    'auth',\n    'hash',\n    'host',\n    'hostname',\n    'href',\n    'path',\n    'pathname',\n    'port',\n    'protocol',\n    'query',\n    'search',\n    'slashes'\n];\nfunction formatWithValidation(url) {\n    if (true) {\n        if (url !== null && typeof url === 'object') {\n            Object.keys(url).forEach((key)=>{\n                if (!urlObjectKeys.includes(key)) {\n                    console.warn(\"Unknown key passed via urlObject into url.format: \" + key);\n                }\n            });\n        }\n    }\n    return formatUrl(url);\n} //# sourceMappingURL=format-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js":
/*!************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/is-local-url.js ***!
  \************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"isLocalURL\", ({\n    enumerable: true,\n    get: function() {\n        return isLocalURL;\n    }\n}));\nconst _utils = __webpack_require__(/*! ../../utils */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\");\nconst _hasbasepath = __webpack_require__(/*! ../../../../client/has-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/has-base-path.js\");\nfunction isLocalURL(url) {\n    // prevent a hydration mismatch on href for url with anchor refs\n    if (!(0, _utils.isAbsoluteUrl)(url)) return true;\n    try {\n        // absolute urls can be local if they are on the same origin\n        const locationOrigin = (0, _utils.getLocationOrigin)();\n        const resolved = new URL(url, locationOrigin);\n        return resolved.origin === locationOrigin && (0, _hasbasepath.hasBasePath)(resolved.pathname);\n    } catch (_) {\n        return false;\n    }\n} //# sourceMappingURL=is-local-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvaXMtbG9jYWwtdXJsLmpzIiwibWFwcGluZ3MiOiI7Ozs7OENBTWdCQTs7O2VBQUFBOzs7bUNBTmlDO3lDQUNyQjtBQUtyQixTQUFTQSxXQUFXQyxHQUFXO0lBQ3BDLGdFQUFnRTtJQUNoRSxJQUFJLENBQUNDLENBQUFBLEdBQUFBLE9BQUFBLGFBQUFBLEVBQWNELE1BQU0sT0FBTztJQUNoQyxJQUFJO1FBQ0YsNERBQTREO1FBQzVELE1BQU1FLGlCQUFpQkMsQ0FBQUEsR0FBQUEsT0FBQUEsaUJBQUFBO1FBQ3ZCLE1BQU1DLFdBQVcsSUFBSUMsSUFBSUwsS0FBS0U7UUFDOUIsT0FBT0UsU0FBU0UsTUFBTSxLQUFLSixrQkFBa0JLLENBQUFBLEdBQUFBLGFBQUFBLFdBQUFBLEVBQVlILFNBQVNJLFFBQVE7SUFDNUUsRUFBRSxPQUFPQyxHQUFHO1FBQ1YsT0FBTztJQUNUO0FBQ0YiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zcmMvc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvaXMtbG9jYWwtdXJsLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzQWJzb2x1dGVVcmwsIGdldExvY2F0aW9uT3JpZ2luIH0gZnJvbSAnLi4vLi4vdXRpbHMnXG5pbXBvcnQgeyBoYXNCYXNlUGF0aCB9IGZyb20gJy4uLy4uLy4uLy4uL2NsaWVudC9oYXMtYmFzZS1wYXRoJ1xuXG4vKipcbiAqIERldGVjdHMgd2hldGhlciBhIGdpdmVuIHVybCBpcyByb3V0YWJsZSBieSB0aGUgTmV4dC5qcyByb3V0ZXIgKGJyb3dzZXIgb25seSkuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpc0xvY2FsVVJMKHVybDogc3RyaW5nKTogYm9vbGVhbiB7XG4gIC8vIHByZXZlbnQgYSBoeWRyYXRpb24gbWlzbWF0Y2ggb24gaHJlZiBmb3IgdXJsIHdpdGggYW5jaG9yIHJlZnNcbiAgaWYgKCFpc0Fic29sdXRlVXJsKHVybCkpIHJldHVybiB0cnVlXG4gIHRyeSB7XG4gICAgLy8gYWJzb2x1dGUgdXJscyBjYW4gYmUgbG9jYWwgaWYgdGhleSBhcmUgb24gdGhlIHNhbWUgb3JpZ2luXG4gICAgY29uc3QgbG9jYXRpb25PcmlnaW4gPSBnZXRMb2NhdGlvbk9yaWdpbigpXG4gICAgY29uc3QgcmVzb2x2ZWQgPSBuZXcgVVJMKHVybCwgbG9jYXRpb25PcmlnaW4pXG4gICAgcmV0dXJuIHJlc29sdmVkLm9yaWdpbiA9PT0gbG9jYXRpb25PcmlnaW4gJiYgaGFzQmFzZVBhdGgocmVzb2x2ZWQucGF0aG5hbWUpXG4gIH0gY2F0Y2ggKF8pIHtcbiAgICByZXR1cm4gZmFsc2VcbiAgfVxufVxuIl0sIm5hbWVzIjpbImlzTG9jYWxVUkwiLCJ1cmwiLCJpc0Fic29sdXRlVXJsIiwibG9jYXRpb25PcmlnaW4iLCJnZXRMb2NhdGlvbk9yaWdpbiIsInJlc29sdmVkIiwiVVJMIiwib3JpZ2luIiwiaGFzQmFzZVBhdGgiLCJwYXRobmFtZSIsIl8iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/querystring.js ***!
  \***********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    assign: function() {\n        return assign;\n    },\n    searchParamsToUrlQuery: function() {\n        return searchParamsToUrlQuery;\n    },\n    urlQueryToSearchParams: function() {\n        return urlQueryToSearchParams;\n    }\n});\nfunction searchParamsToUrlQuery(searchParams) {\n    const query = {};\n    for (const [key, value] of searchParams.entries()){\n        const existing = query[key];\n        if (typeof existing === 'undefined') {\n            query[key] = value;\n        } else if (Array.isArray(existing)) {\n            existing.push(value);\n        } else {\n            query[key] = [\n                existing,\n                value\n            ];\n        }\n    }\n    return query;\n}\nfunction stringifyUrlQueryParam(param) {\n    if (typeof param === 'string') {\n        return param;\n    }\n    if (typeof param === 'number' && !isNaN(param) || typeof param === 'boolean') {\n        return String(param);\n    } else {\n        return '';\n    }\n}\nfunction urlQueryToSearchParams(query) {\n    const searchParams = new URLSearchParams();\n    for (const [key, value] of Object.entries(query)){\n        if (Array.isArray(value)) {\n            for (const item of value){\n                searchParams.append(key, stringifyUrlQueryParam(item));\n            }\n        } else {\n            searchParams.set(key, stringifyUrlQueryParam(value));\n        }\n    }\n    return searchParams;\n}\nfunction assign(target) {\n    for(var _len = arguments.length, searchParamsList = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        searchParamsList[_key - 1] = arguments[_key];\n    }\n    for (const searchParams of searchParamsList){\n        for (const key of searchParams.keys()){\n            target.delete(key);\n        }\n        for (const [key, value] of searchParams.entries()){\n            target.append(key, value);\n        }\n    }\n    return target;\n} //# sourceMappingURL=querystring.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils.js ***!
  \****************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DecodeError: function() {\n        return DecodeError;\n    },\n    MiddlewareNotFoundError: function() {\n        return MiddlewareNotFoundError;\n    },\n    MissingStaticPage: function() {\n        return MissingStaticPage;\n    },\n    NormalizeError: function() {\n        return NormalizeError;\n    },\n    PageNotFoundError: function() {\n        return PageNotFoundError;\n    },\n    SP: function() {\n        return SP;\n    },\n    ST: function() {\n        return ST;\n    },\n    WEB_VITALS: function() {\n        return WEB_VITALS;\n    },\n    execOnce: function() {\n        return execOnce;\n    },\n    getDisplayName: function() {\n        return getDisplayName;\n    },\n    getLocationOrigin: function() {\n        return getLocationOrigin;\n    },\n    getURL: function() {\n        return getURL;\n    },\n    isAbsoluteUrl: function() {\n        return isAbsoluteUrl;\n    },\n    isResSent: function() {\n        return isResSent;\n    },\n    loadGetInitialProps: function() {\n        return loadGetInitialProps;\n    },\n    normalizeRepeatedSlashes: function() {\n        return normalizeRepeatedSlashes;\n    },\n    stringifyError: function() {\n        return stringifyError;\n    }\n});\nconst WEB_VITALS = [\n    'CLS',\n    'FCP',\n    'FID',\n    'INP',\n    'LCP',\n    'TTFB'\n];\nfunction execOnce(fn) {\n    let used = false;\n    let result;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        if (!used) {\n            used = true;\n            result = fn(...args);\n        }\n        return result;\n    };\n}\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/;\nconst isAbsoluteUrl = (url)=>ABSOLUTE_URL_REGEX.test(url);\nfunction getLocationOrigin() {\n    const { protocol, hostname, port } = window.location;\n    return protocol + \"//\" + hostname + (port ? ':' + port : '');\n}\nfunction getURL() {\n    const { href } = window.location;\n    const origin = getLocationOrigin();\n    return href.substring(origin.length);\n}\nfunction getDisplayName(Component) {\n    return typeof Component === 'string' ? Component : Component.displayName || Component.name || 'Unknown';\n}\nfunction isResSent(res) {\n    return res.finished || res.headersSent;\n}\nfunction normalizeRepeatedSlashes(url) {\n    const urlParts = url.split('?');\n    const urlNoQuery = urlParts[0];\n    return urlNoQuery // first we replace any non-encoded backslashes with forward\n    // then normalize repeated forward slashes\n    .replace(/\\\\/g, '/').replace(/\\/\\/+/g, '/') + (urlParts[1] ? \"?\" + urlParts.slice(1).join('?') : '');\n}\nasync function loadGetInitialProps(App, ctx) {\n    if (true) {\n        var _App_prototype;\n        if ((_App_prototype = App.prototype) == null ? void 0 : _App_prototype.getInitialProps) {\n            const message = '\"' + getDisplayName(App) + '.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.';\n            throw Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n                value: \"E394\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n    }\n    // when called from _app `ctx` is nested in `ctx`\n    const res = ctx.res || ctx.ctx && ctx.ctx.res;\n    if (!App.getInitialProps) {\n        if (ctx.ctx && ctx.Component) {\n            // @ts-ignore pageProps default\n            return {\n                pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx)\n            };\n        }\n        return {};\n    }\n    const props = await App.getInitialProps(ctx);\n    if (res && isResSent(res)) {\n        return props;\n    }\n    if (!props) {\n        const message = '\"' + getDisplayName(App) + '.getInitialProps()\" should resolve to an object. But found \"' + props + '\" instead.';\n        throw Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n            value: \"E394\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    if (true) {\n        if (Object.keys(props).length === 0 && !ctx.ctx) {\n            console.warn(\"\" + getDisplayName(App) + \" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps\");\n        }\n    }\n    return props;\n}\nconst SP = typeof performance !== 'undefined';\nconst ST = SP && [\n    'mark',\n    'measure',\n    'getEntriesByName'\n].every((method)=>typeof performance[method] === 'function');\nclass DecodeError extends Error {\n}\nclass NormalizeError extends Error {\n}\nclass PageNotFoundError extends Error {\n    constructor(page){\n        super();\n        this.code = 'ENOENT';\n        this.name = 'PageNotFoundError';\n        this.message = \"Cannot find module for page: \" + page;\n    }\n}\nclass MissingStaticPage extends Error {\n    constructor(page, message){\n        super();\n        this.message = \"Failed to load static file for page: \" + page + \" \" + message;\n    }\n}\nclass MiddlewareNotFoundError extends Error {\n    constructor(){\n        super();\n        this.code = 'ENOENT';\n        this.message = \"Cannot find the middleware module\";\n    }\n}\nfunction stringifyError(error) {\n    return JSON.stringify({\n        message: error.message,\n        stack: error.stack\n    });\n} //# sourceMappingURL=utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js":
/*!***************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils/error-once.js ***!
  \***************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"errorOnce\", ({\n    enumerable: true,\n    get: function() {\n        return errorOnce;\n    }\n}));\nlet errorOnce = (_)=>{};\nif (true) {\n    const errors = new Set();\n    errorOnce = (msg)=>{\n        if (!errors.has(msg)) {\n            console.error(msg);\n        }\n        errors.add(msg);\n    };\n} //# sourceMappingURL=error-once.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi91dGlscy9lcnJvci1vbmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7NkNBV1NBOzs7ZUFBQUE7OztBQVhULElBQUlBLFlBQVksQ0FBQ0MsS0FBZTtBQUNoQyxJQUFJQyxJQUFvQixFQUFtQjtJQUN6QyxNQUFNRyxTQUFTLElBQUlDO0lBQ25CTixZQUFZLENBQUNPO1FBQ1gsSUFBSSxDQUFDRixPQUFPRyxHQUFHLENBQUNELE1BQU07WUFDcEJFLFFBQVFDLEtBQUssQ0FBQ0g7UUFDaEI7UUFDQUYsT0FBT00sR0FBRyxDQUFDSjtJQUNiO0FBQ0YiLCJzb3VyY2VzIjpbIi9Vc2Vycy9uaXNoYXBhbmNoYWwvc3JjL3NoYXJlZC9saWIvdXRpbHMvZXJyb3Itb25jZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJsZXQgZXJyb3JPbmNlID0gKF86IHN0cmluZykgPT4ge31cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIGNvbnN0IGVycm9ycyA9IG5ldyBTZXQ8c3RyaW5nPigpXG4gIGVycm9yT25jZSA9IChtc2c6IHN0cmluZykgPT4ge1xuICAgIGlmICghZXJyb3JzLmhhcyhtc2cpKSB7XG4gICAgICBjb25zb2xlLmVycm9yKG1zZylcbiAgICB9XG4gICAgZXJyb3JzLmFkZChtc2cpXG4gIH1cbn1cblxuZXhwb3J0IHsgZXJyb3JPbmNlIH1cbiJdLCJuYW1lcyI6WyJlcnJvck9uY2UiLCJfIiwicHJvY2VzcyIsImVudiIsIk5PREVfRU5WIiwiZXJyb3JzIiwiU2V0IiwibXNnIiwiaGFzIiwiY29uc29sZSIsImVycm9yIiwiYWRkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx":
/*!***************************************************************************************************!*\
  !*** ./src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx ***!
  \***************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavigationBarSection: () => (/* binding */ NavigationBarSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_MenuIcon_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=MenuIcon,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_MenuIcon_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=MenuIcon,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/navigation-menu */ \"(app-pages-browser)/./src/components/ui/navigation-menu.tsx\");\n/* harmony import */ var _components_QuoteModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/QuoteModal */ \"(app-pages-browser)/./src/components/QuoteModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ NavigationBarSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst NavigationBarSection = ()=>{\n    _s();\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const navigationItems = [\n        {\n            name: \"Home\",\n            href: \"/\"\n        },\n        {\n            name: \"Products\",\n            href: \"/products\"\n        },\n        {\n            name: \"About Us\",\n            href: \"/about\"\n        },\n        {\n            name: \"Contact\",\n            href: \"/contact\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"flex flex-col items-center w-full bg-white border-b border-[#01010a26] sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-16 lg:h-[72px] items-center justify-between container-responsive w-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4 lg:gap-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            className: \"w-16 h-auto aspect-auto lg:w-[200px] lg:h-auto\",\n                            alt: \"Spring Solutions Company Logo\",\n                            src: \"/logo.png\",\n                            width: 84,\n                            height: 36,\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_6__.NavigationMenu, {\n                    className: \"hidden lg:flex absolute left-1/2 transform -translate-x-1/2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_6__.NavigationMenuList, {\n                        className: \"flex items-center gap-6 lg:gap-8\",\n                        children: navigationItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_6__.NavigationMenuItem, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        className: \"font-text-regular-normal text-[#01010a] hover:bg-[#01010a0d] transition-colors\",\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 lg:gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden sm:flex items-center gap-2 lg:gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/contact\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        className: \"px-3 lg:px-5 py-2 bg-[#01010a0d] rounded-[100px] border-b-4 border-[#01010a26] hover:bg-[#01010a1a] text-sm lg:text-base transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-text-regular-medium text-[#01010a] text-[length:var(--text-regular-medium-font-size)] tracking-[var(--text-regular-medium-letter-spacing)] leading-[var(--text-regular-medium-line-height)]\",\n                                            children: \"Contact\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QuoteModal__WEBPACK_IMPORTED_MODULE_7__.QuoteModal, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        className: \"px-3 lg:px-5 py-2 bg-[#1717c4] rounded-[100px] border-b-4 border-[#12129c] hover:bg-[#1414a8] text-sm lg:text-base transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-text-regular-medium text-white text-[length:var(--text-regular-medium-font-size)] tracking-[var(--text-regular-medium-letter-spacing)] leading-[var(--text-regular-medium-line-height)]\",\n                                            children: \"Get Quote\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.Sheet, {\n                            open: isMobileMenuOpen,\n                            onOpenChange: setIsMobileMenuOpen,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetTrigger, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"lg:hidden w-10 h-10 p-0 flex items-center justify-center rounded-full hover:bg-[#01010a0d] transition-all duration-300\",\n                                        \"aria-label\": \"Open mobile menu\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative w-6 h-6 flex items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuIcon_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-6 h-6 transition-all duration-300 \".concat(isMobileMenuOpen ? \"opacity-0 rotate-180 scale-0\" : \"opacity-100 rotate-0 scale-100\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuIcon_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-6 h-6 absolute transition-all duration-300 \".concat(isMobileMenuOpen ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 rotate-180 scale-0\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetContent, {\n                                    side: \"right\",\n                                    className: \"w-full h-full max-w-none rounded-none border-none shadow-none bg-white fixed inset-0 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetHeader, {\n                                            className: \"sr-only\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetTitle, {\n                                                children: \"Navigation Menu\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex h-16 items-center justify-between container-responsive w-full border-b border-[#01010a26]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/\",\n                                                className: \"flex-shrink-0\",\n                                                onClick: ()=>setIsMobileMenuOpen(false),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    className: \"w-16 h-7\",\n                                                    alt: \"Spring Solutions Company Logo\",\n                                                    src: \"/company-logo.svg\",\n                                                    width: 84,\n                                                    height: 36,\n                                                    priority: true\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col h-[calc(100vh-4rem)] justify-center items-center container-responsive py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center space-y-8 flex-1 justify-center\",\n                                                    children: navigationItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: item.href,\n                                                            className: \"text-center py-4 px-8 text-[#01010a] font-text-large-semi-bold hover:bg-[#01010a0d] rounded-xl transition-all duration-200 transform hover:scale-[1.05] active:scale-[0.95]\",\n                                                            onClick: ()=>setIsMobileMenuOpen(false),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-2xl\",\n                                                                children: item.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, index, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 21\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center gap-4 w-full max-w-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/contact\",\n                                                            className: \"w-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"w-full px-6 py-4 bg-[#01010a0d] rounded-2xl border-2 border-[#01010a26] hover:bg-[#01010a1a] transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]\",\n                                                                onClick: ()=>setIsMobileMenuOpen(false),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-text-regular-medium text-[#01010a] text-lg\",\n                                                                    children: \"Contact Us\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                                    lineNumber: 169,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                                lineNumber: 164,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QuoteModal__WEBPACK_IMPORTED_MODULE_7__.QuoteModal, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                className: \"w-full px-6 py-4 bg-[#1717c4] rounded-2xl border-2 border-[#12129c] hover:bg-[#1414a8] transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]\",\n                                                                onClick: ()=>setIsMobileMenuOpen(false),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-text-regular-medium text-white text-lg\",\n                                                                    children: \"Get Quote\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                                    lineNumber: 180,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                                lineNumber: 176,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, undefined);\n};\n_s(NavigationBarSection, \"QerECOS75+B7gv+k3q7FrDf39mc=\");\n_c = NavigationBarSection;\nvar _c;\n$RefreshReg$(_c, \"NavigationBarSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/navigation-menu.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/navigation-menu.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavigationMenu: () => (/* binding */ NavigationMenu),\n/* harmony export */   NavigationMenuContent: () => (/* binding */ NavigationMenuContent),\n/* harmony export */   NavigationMenuIndicator: () => (/* binding */ NavigationMenuIndicator),\n/* harmony export */   NavigationMenuItem: () => (/* binding */ NavigationMenuItem),\n/* harmony export */   NavigationMenuLink: () => (/* binding */ NavigationMenuLink),\n/* harmony export */   NavigationMenuList: () => (/* binding */ NavigationMenuList),\n/* harmony export */   NavigationMenuTrigger: () => (/* binding */ NavigationMenuTrigger),\n/* harmony export */   NavigationMenuViewport: () => (/* binding */ NavigationMenuViewport),\n/* harmony export */   navigationMenuTriggerStyle: () => (/* binding */ navigationMenuTriggerStyle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-navigation-menu */ \"(app-pages-browser)/./node_modules/@radix-ui/react-navigation-menu/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(app-pages-browser)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ navigationMenuTriggerStyle,NavigationMenu,NavigationMenuList,NavigationMenuItem,NavigationMenuContent,NavigationMenuTrigger,NavigationMenuLink,NavigationMenuIndicator,NavigationMenuViewport auto */ \n\n\n\n\n\nconst NavigationMenu = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"relative z-10 flex max-w-max flex-1 items-center justify-center\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavigationMenuViewport, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/navigation-menu.tsx\",\n                lineNumber: 23,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/navigation-menu.tsx\",\n        lineNumber: 14,\n        columnNumber: 3\n    }, undefined);\n});\n_c1 = NavigationMenu;\nNavigationMenu.displayName = _radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\nconst NavigationMenuList = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c2 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.List, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"group flex flex-1 list-none items-center justify-center space-x-1\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/navigation-menu.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined);\n});\n_c3 = NavigationMenuList;\nNavigationMenuList.displayName = _radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.List.displayName;\nconst NavigationMenuItem = _radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Item;\nconst navigationMenuTriggerStyle = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\");\nconst NavigationMenuTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c4 = (param, ref)=>{\n    let { className, children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Trigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(navigationMenuTriggerStyle(), \"group\", className),\n        ...props,\n        children: [\n            children,\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180\",\n                \"aria-hidden\": \"true\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/navigation-menu.tsx\",\n                lineNumber: 59,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/navigation-menu.tsx\",\n        lineNumber: 53,\n        columnNumber: 3\n    }, undefined);\n});\n_c5 = NavigationMenuTrigger;\nNavigationMenuTrigger.displayName = _radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Trigger.displayName;\nconst NavigationMenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c6 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Content, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"left-0 top-0 w-full data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 md:absolute md:w-auto \", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/navigation-menu.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined);\n});\n_c7 = NavigationMenuContent;\nNavigationMenuContent.displayName = _radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Content.displayName;\nconst NavigationMenuLink = _radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Link;\nconst NavigationMenuViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef((param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute left-0 top-full flex justify-center\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Viewport, {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"origin-top-center relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 md:w-[var(--radix-navigation-menu-viewport-width)]\", className),\n            ref: ref,\n            ...props\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/navigation-menu.tsx\",\n            lineNumber: 89,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/navigation-menu.tsx\",\n        lineNumber: 88,\n        columnNumber: 3\n    }, undefined);\n});\n_c8 = NavigationMenuViewport;\nNavigationMenuViewport.displayName = _radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Viewport.displayName;\nconst NavigationMenuIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c9 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Indicator, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm bg-border shadow-md\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/navigation-menu.tsx\",\n            lineNumber: 114,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/navigation-menu.tsx\",\n        lineNumber: 106,\n        columnNumber: 3\n    }, undefined);\n});\n_c10 = NavigationMenuIndicator;\nNavigationMenuIndicator.displayName = _radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Indicator.displayName;\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10;\n$RefreshReg$(_c, \"NavigationMenu$React.forwardRef\");\n$RefreshReg$(_c1, \"NavigationMenu\");\n$RefreshReg$(_c2, \"NavigationMenuList$React.forwardRef\");\n$RefreshReg$(_c3, \"NavigationMenuList\");\n$RefreshReg$(_c4, \"NavigationMenuTrigger$React.forwardRef\");\n$RefreshReg$(_c5, \"NavigationMenuTrigger\");\n$RefreshReg$(_c6, \"NavigationMenuContent$React.forwardRef\");\n$RefreshReg$(_c7, \"NavigationMenuContent\");\n$RefreshReg$(_c8, \"NavigationMenuViewport\");\n$RefreshReg$(_c9, \"NavigationMenuIndicator$React.forwardRef\");\n$RefreshReg$(_c10, \"NavigationMenuIndicator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/navigation-menu.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/sheet.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/sheet.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sheet: () => (/* binding */ Sheet),\n/* harmony export */   SheetClose: () => (/* binding */ SheetClose),\n/* harmony export */   SheetContent: () => (/* binding */ SheetContent),\n/* harmony export */   SheetDescription: () => (/* binding */ SheetDescription),\n/* harmony export */   SheetFooter: () => (/* binding */ SheetFooter),\n/* harmony export */   SheetHeader: () => (/* binding */ SheetHeader),\n/* harmony export */   SheetOverlay: () => (/* binding */ SheetOverlay),\n/* harmony export */   SheetPortal: () => (/* binding */ SheetPortal),\n/* harmony export */   SheetTitle: () => (/* binding */ SheetTitle),\n/* harmony export */   SheetTrigger: () => (/* binding */ SheetTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-dialog */ \"(app-pages-browser)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(app-pages-browser)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\n\n\n\nconst Sheet = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Root;\nconst SheetTrigger = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Trigger;\nconst SheetClose = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Close;\nconst SheetPortal = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Portal;\nconst SheetOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef((param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Overlay, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\", className),\n        ...props,\n        ref: ref\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/sheet.tsx\",\n        lineNumber: 20,\n        columnNumber: 3\n    }, undefined);\n});\n_c = SheetOverlay;\nSheetOverlay.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Overlay.displayName;\nconst sheetVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500\", {\n    variants: {\n        side: {\n            top: \"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top\",\n            bottom: \"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom\",\n            left: \"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm\",\n            right: \"inset-y-0 right-0 h-full w-3/4 border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm\"\n        }\n    },\n    defaultVariants: {\n        side: \"right\"\n    }\n});\nconst SheetContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c1 = (param, ref)=>{\n    let { side = \"right\", className, children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SheetPortal, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SheetOverlay, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/sheet.tsx\",\n                lineNumber: 59,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Content, {\n                ref: ref,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(sheetVariants({\n                    side\n                }), className),\n                ...props,\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Close, {\n                        className: \"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/sheet.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/sheet.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/sheet.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/sheet.tsx\",\n                lineNumber: 60,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/sheet.tsx\",\n        lineNumber: 58,\n        columnNumber: 3\n    }, undefined);\n});\n_c2 = SheetContent;\nSheetContent.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Content.displayName;\nconst SheetHeader = (param)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex flex-col space-y-2 text-center sm:text-left\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/sheet.tsx\",\n        lineNumber: 79,\n        columnNumber: 3\n    }, undefined);\n};\n_c3 = SheetHeader;\nSheetHeader.displayName = \"SheetHeader\";\nconst SheetFooter = (param)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/sheet.tsx\",\n        lineNumber: 93,\n        columnNumber: 3\n    }, undefined);\n};\n_c4 = SheetFooter;\nSheetFooter.displayName = \"SheetFooter\";\nconst SheetTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c5 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-lg font-semibold text-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/sheet.tsx\",\n        lineNumber: 107,\n        columnNumber: 3\n    }, undefined);\n});\n_c6 = SheetTitle;\nSheetTitle.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Title.displayName;\nconst SheetDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c7 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/sheet.tsx\",\n        lineNumber: 119,\n        columnNumber: 3\n    }, undefined);\n});\n_c8 = SheetDescription;\nSheetDescription.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Description.displayName;\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"SheetOverlay\");\n$RefreshReg$(_c1, \"SheetContent$React.forwardRef\");\n$RefreshReg$(_c2, \"SheetContent\");\n$RefreshReg$(_c3, \"SheetHeader\");\n$RefreshReg$(_c4, \"SheetFooter\");\n$RefreshReg$(_c5, \"SheetTitle$React.forwardRef\");\n$RefreshReg$(_c6, \"SheetTitle\");\n$RefreshReg$(_c7, \"SheetDescription$React.forwardRef\");\n$RefreshReg$(_c8, \"SheetDescription\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/sheet.tsx\n"));

/***/ })

});