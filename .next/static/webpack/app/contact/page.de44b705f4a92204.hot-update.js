"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contact/page",{

/***/ "(app-pages-browser)/./src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx":
/*!***************************************************************************************************!*\
  !*** ./src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx ***!
  \***************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavigationBarSection: () => (/* binding */ NavigationBarSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_MenuIcon_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=MenuIcon,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_MenuIcon_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=MenuIcon,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/navigation-menu */ \"(app-pages-browser)/./src/components/ui/navigation-menu.tsx\");\n/* harmony import */ var _components_QuoteModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/QuoteModal */ \"(app-pages-browser)/./src/components/QuoteModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ NavigationBarSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst NavigationBarSection = ()=>{\n    _s();\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const navigationItems = [\n        {\n            name: \"Home\",\n            href: \"/\"\n        },\n        {\n            name: \"Products\",\n            href: \"/products\"\n        },\n        {\n            name: \"About Us\",\n            href: \"/about\"\n        },\n        {\n            name: \"Contact\",\n            href: \"/contact\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"flex flex-col items-center w-full bg-white border-b border-[#01010a26] sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-16 lg:h-[72px] items-center justify-between container-responsive w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center gap-4 lg:gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    className: \"w-16 h-auto aspect-auto lg:w-[200px] lg:h-auto\",\n                                    alt: \"Spring Solutions Company Logo\",\n                                    src: \"/logo.png\",\n                                    width: 84,\n                                    height: 36,\n                                    priority: true\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_6__.NavigationMenu, {\n                                className: \"hidden lg:flex\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_6__.NavigationMenuList, {\n                                    className: \"flex items-center gap-6 lg:gap-8\",\n                                    children: navigationItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_6__.NavigationMenuItem, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: item.href,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"ghost\",\n                                                    className: \"font-text-regular-normal text-[#01010a] hover:bg-[#01010a0d] transition-colors\",\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                    lineNumber: 54,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, index, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 lg:gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden sm:flex items-center gap-2 lg:gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/contact\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            className: \"px-3 lg:px-5 py-2 bg-[#01010a0d] rounded-[100px] border-b-4 border-[#01010a26] hover:bg-[#01010a1a] text-sm lg:text-base transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-text-regular-medium text-[#01010a] text-[length:var(--text-regular-medium-font-size)] tracking-[var(--text-regular-medium-letter-spacing)] leading-[var(--text-regular-medium-line-height)]\",\n                                                children: \"Contact\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QuoteModal__WEBPACK_IMPORTED_MODULE_7__.QuoteModal, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            className: \"px-3 lg:px-5 py-2 bg-[#1717c4] rounded-[100px] border-b-4 border-[#12129c] hover:bg-[#1414a8] text-sm lg:text-base transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-text-regular-medium text-white text-[length:var(--text-regular-medium-font-size)] tracking-[var(--text-regular-medium-letter-spacing)] leading-[var(--text-regular-medium-line-height)]\",\n                                                children: \"Get Quote\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.Sheet, {\n                                open: isMobileMenuOpen,\n                                onOpenChange: setIsMobileMenuOpen,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            className: \"lg:hidden w-10 h-10 p-0 flex items-center justify-center rounded-full hover:bg-[#01010a0d] transition-all duration-300\",\n                                            \"aria-label\": \"Open mobile menu\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative w-6 h-6 flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuIcon_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-6 h-6 transition-all duration-300 \".concat(isMobileMenuOpen ? \"opacity-0 rotate-180 scale-0\" : \"opacity-100 rotate-0 scale-100\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuIcon_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-6 h-6 absolute transition-all duration-300 \".concat(isMobileMenuOpen ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 rotate-180 scale-0\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                        lineNumber: 107,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetContent, {\n                                        side: \"right\",\n                                        className: \"w-full h-full max-w-none rounded-none border-none shadow-none bg-white fixed inset-0 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetHeader, {\n                                                className: \"sr-only\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetTitle, {\n                                                    children: \"Navigation Menu\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex h-16 items-center justify-between container-responsive w-full border-b border-[#01010a26]\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/\",\n                                                    className: \"flex-shrink-0\",\n                                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                        className: \"w-16 h-7\",\n                                                        alt: \"Spring Solutions Company Logo\",\n                                                        src: \"/company-logo.svg\",\n                                                        width: 84,\n                                                        height: 36,\n                                                        priority: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col h-[calc(100vh-4rem)] justify-center items-center container-responsive py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center space-y-8 flex-1 justify-center\",\n                                                        children: navigationItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: item.href,\n                                                                className: \"text-center py-4 px-8 text-[#01010a] font-text-large-semi-bold hover:bg-[#01010a0d] rounded-xl transition-all duration-200 transform hover:scale-[1.05] active:scale-[0.95]\",\n                                                                onClick: ()=>setIsMobileMenuOpen(false),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-2xl\",\n                                                                    children: item.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                                    lineNumber: 155,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, index, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                                lineNumber: 149,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center gap-4 w-full max-w-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/contact\",\n                                                                className: \"w-full\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: \"outline\",\n                                                                    className: \"w-full px-6 py-4 bg-[#01010a0d] rounded-2xl border-2 border-[#01010a26] hover:bg-[#01010a1a] transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]\",\n                                                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-text-regular-medium text-[#01010a] text-lg\",\n                                                                        children: \"Contact Us\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                                        lineNumber: 168,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                                    lineNumber: 163,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                                lineNumber: 162,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QuoteModal__WEBPACK_IMPORTED_MODULE_7__.QuoteModal, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    className: \"w-full px-6 py-4 bg-[#1717c4] rounded-2xl border-2 border-[#12129c] hover:bg-[#1414a8] transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]\",\n                                                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-text-regular-medium text-white text-lg\",\n                                                                        children: \"Get Quote\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                                        lineNumber: 179,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                                    lineNumber: 175,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                                lineNumber: 174,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, undefined);\n};\n_s(NavigationBarSection, \"QerECOS75+B7gv+k3q7FrDf39mc=\");\n_c = NavigationBarSection;\nvar _c;\n$RefreshReg$(_c, \"NavigationBarSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3NjcmVlbnMvSG9tZURlc2t0b3Avc2VjdGlvbnMvTmF2aWdhdGlvbkJhclNlY3Rpb24vTmF2aWdhdGlvbkJhclNlY3Rpb24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUUyQztBQUNaO0FBQ0Y7QUFDVztBQUNRO0FBT2pCO0FBS1U7QUFDWTtBQUU5QyxNQUFNZ0IsdUJBQXVCOztJQUNsQyxNQUFNLENBQUNDLGtCQUFrQkMsb0JBQW9CLEdBQUdiLCtDQUFRQSxDQUFDO0lBRXpELE1BQU1jLGtCQUFrQjtRQUN0QjtZQUFFQyxNQUFNO1lBQVFDLE1BQU07UUFBSTtRQUMxQjtZQUFFRCxNQUFNO1lBQVlDLE1BQU07UUFBWTtRQUN0QztZQUFFRCxNQUFNO1lBQVlDLE1BQU07UUFBUztRQUNuQztZQUFFRCxNQUFNO1lBQVdDLE1BQU07UUFBVztLQUNyQztJQUVELHFCQUNFLDhEQUFDQztRQUFPQyxXQUFVO2tCQUNoQiw0RUFBQ0M7WUFBSUQsV0FBVTtzQkFDYiw0RUFBQ0M7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDQzt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUNwQixrREFBSUE7Z0NBQUNrQixNQUFLO2dDQUFJRSxXQUFVOzBDQUN2Qiw0RUFBQ3JCLGtEQUFLQTtvQ0FDSnFCLFdBQVU7b0NBQ1ZFLEtBQUk7b0NBQ0pDLEtBQUk7b0NBQ0pDLE9BQU87b0NBQ1BDLFFBQVE7b0NBQ1JDLFFBQVE7Ozs7Ozs7Ozs7OzBDQUtaLDhEQUFDakIsMEVBQWNBO2dDQUFDVyxXQUFVOzBDQUN4Qiw0RUFBQ1YsOEVBQWtCQTtvQ0FBQ1UsV0FBVTs4Q0FDM0JKLGdCQUFnQlcsR0FBRyxDQUFDLENBQUNDLE1BQU1DLHNCQUMxQiw4REFBQ2xCLDhFQUFrQkE7c0RBQ2pCLDRFQUFDWCxrREFBSUE7Z0RBQUNrQixNQUFNVSxLQUFLVixJQUFJOzBEQUNuQiw0RUFBQ2YseURBQU1BO29EQUNMMkIsU0FBUTtvREFDUlYsV0FBVTs4REFFVFEsS0FBS1gsSUFBSTs7Ozs7Ozs7Ozs7MkNBTlNZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBZWpDLDhEQUFDUjt3QkFBSUQsV0FBVTs7MENBRWIsOERBQUNDO2dDQUFJRCxXQUFVOztrREFDYiw4REFBQ3BCLGtEQUFJQTt3Q0FBQ2tCLE1BQUs7a0RBQ1QsNEVBQUNmLHlEQUFNQTs0Q0FDTDJCLFNBQVE7NENBQ1JWLFdBQVU7c0RBRVYsNEVBQUNXO2dEQUFLWCxXQUFVOzBEQUFtTTs7Ozs7Ozs7Ozs7Ozs7OztrREFNdk4sOERBQUNSLDhEQUFVQTtrREFDVCw0RUFBQ1QseURBQU1BOzRDQUFDaUIsV0FBVTtzREFDaEIsNEVBQUNXO2dEQUFLWCxXQUFVOzBEQUErTDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FRck4sOERBQUNoQix1REFBS0E7Z0NBQUM0QixNQUFNbEI7Z0NBQWtCbUIsY0FBY2xCOztrREFDM0MsOERBQUNQLDhEQUFZQTt3Q0FBQzBCLE9BQU87a0RBQ25CLDRFQUFDL0IseURBQU1BOzRDQUNMMkIsU0FBUTs0Q0FDUkssTUFBSzs0Q0FDTGYsV0FBVTs0Q0FDVmdCLGNBQVc7c0RBRVgsNEVBQUNmO2dEQUFJRCxXQUFVOztrRUFDYiw4REFBQ3ZCLHNGQUFRQTt3REFDUHVCLFdBQVcsdUNBSVYsT0FIQ04sbUJBQ0ksaUNBQ0E7Ozs7OztrRUFHUiw4REFBQ2hCLHNGQUFDQTt3REFDQXNCLFdBQVcsZ0RBSVYsT0FIQ04sbUJBQ0ksbUNBQ0E7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBTWQsOERBQUNULDhEQUFZQTt3Q0FDWGdDLE1BQUs7d0NBQ0xqQixXQUFVOzswREFHViw4REFBQ2QsNkRBQVdBO2dEQUFDYyxXQUFVOzBEQUNyQiw0RUFBQ2IsNERBQVVBOzhEQUFDOzs7Ozs7Ozs7OzswREFJZCw4REFBQ2M7Z0RBQUlELFdBQVU7MERBQ2IsNEVBQUNwQixrREFBSUE7b0RBQ0hrQixNQUFLO29EQUNMRSxXQUFVO29EQUNWa0IsU0FBUyxJQUFNdkIsb0JBQW9COzhEQUVuQyw0RUFBQ2hCLGtEQUFLQTt3REFDSnFCLFdBQVU7d0RBQ1ZFLEtBQUk7d0RBQ0pDLEtBQUk7d0RBQ0pDLE9BQU87d0RBQ1BDLFFBQVE7d0RBQ1JDLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7MERBTWQsOERBQUNMO2dEQUFJRCxXQUFVOztrRUFFYiw4REFBQ0M7d0RBQUlELFdBQVU7a0VBQ1pKLGdCQUFnQlcsR0FBRyxDQUFDLENBQUNDLE1BQU1DLHNCQUMxQiw4REFBQzdCLGtEQUFJQTtnRUFFSGtCLE1BQU1VLEtBQUtWLElBQUk7Z0VBQ2ZFLFdBQVU7Z0VBQ1ZrQixTQUFTLElBQU12QixvQkFBb0I7MEVBRW5DLDRFQUFDZ0I7b0VBQUtYLFdBQVU7OEVBQVlRLEtBQUtYLElBQUk7Ozs7OzsrREFMaENZOzs7Ozs7Ozs7O2tFQVdYLDhEQUFDUjt3REFBSUQsV0FBVTs7MEVBQ2IsOERBQUNwQixrREFBSUE7Z0VBQUNrQixNQUFLO2dFQUFXRSxXQUFVOzBFQUM5Qiw0RUFBQ2pCLHlEQUFNQTtvRUFDTDJCLFNBQVE7b0VBQ1JWLFdBQVU7b0VBQ1ZrQixTQUFTLElBQU12QixvQkFBb0I7OEVBRW5DLDRFQUFDZ0I7d0VBQUtYLFdBQVU7a0ZBQWtEOzs7Ozs7Ozs7Ozs7Ozs7OzBFQU10RSw4REFBQ1IsOERBQVVBOzBFQUNULDRFQUFDVCx5REFBTUE7b0VBQ0xpQixXQUFVO29FQUNWa0IsU0FBUyxJQUFNdkIsb0JBQW9COzhFQUVuQyw0RUFBQ2dCO3dFQUFLWCxXQUFVO2tGQUE4Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFjdEYsRUFBRTtHQTNLV1A7S0FBQUEiLCJzb3VyY2VzIjpbIi9Vc2Vycy9uaXNoYXBhbmNoYWwvRG9jdW1lbnRzL0dhdXJhdi9Qcm9qZWN0cy9zaGFrdGkvc3JjL2NvbXBvbmVudHMvc2NyZWVucy9Ib21lRGVza3RvcC9zZWN0aW9ucy9OYXZpZ2F0aW9uQmFyU2VjdGlvbi9OYXZpZ2F0aW9uQmFyU2VjdGlvbi50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IE1lbnVJY29uLCBYIH0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xuaW1wb3J0IEltYWdlIGZyb20gXCJuZXh0L2ltYWdlXCI7XG5pbXBvcnQgTGluayBmcm9tIFwibmV4dC9saW5rXCI7XG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYnV0dG9uXCI7XG5pbXBvcnQge1xuICBTaGVldCxcbiAgU2hlZXRDb250ZW50LFxuICBTaGVldEhlYWRlcixcbiAgU2hlZXRUaXRsZSxcbiAgU2hlZXRUcmlnZ2VyLFxufSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3NoZWV0XCI7XG5pbXBvcnQge1xuICBOYXZpZ2F0aW9uTWVudSxcbiAgTmF2aWdhdGlvbk1lbnVMaXN0LFxuICBOYXZpZ2F0aW9uTWVudUl0ZW0sXG59IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvbmF2aWdhdGlvbi1tZW51XCI7XG5pbXBvcnQgeyBRdW90ZU1vZGFsIH0gZnJvbSBcIkAvY29tcG9uZW50cy9RdW90ZU1vZGFsXCI7XG5cbmV4cG9ydCBjb25zdCBOYXZpZ2F0aW9uQmFyU2VjdGlvbiA9ICgpOiBKU1guRWxlbWVudCA9PiB7XG4gIGNvbnN0IFtpc01vYmlsZU1lbnVPcGVuLCBzZXRJc01vYmlsZU1lbnVPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICBjb25zdCBuYXZpZ2F0aW9uSXRlbXMgPSBbXG4gICAgeyBuYW1lOiBcIkhvbWVcIiwgaHJlZjogXCIvXCIgfSxcbiAgICB7IG5hbWU6IFwiUHJvZHVjdHNcIiwgaHJlZjogXCIvcHJvZHVjdHNcIiB9LFxuICAgIHsgbmFtZTogXCJBYm91dCBVc1wiLCBocmVmOiBcIi9hYm91dFwiIH0sXG4gICAgeyBuYW1lOiBcIkNvbnRhY3RcIiwgaHJlZjogXCIvY29udGFjdFwiIH0sXG4gIF07XG5cbiAgcmV0dXJuIChcbiAgICA8aGVhZGVyIGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIHctZnVsbCBiZy13aGl0ZSBib3JkZXItYiBib3JkZXItWyMwMTAxMGEyNl0gc3RpY2t5IHRvcC0wIHotNTBcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBoLTE2IGxnOmgtWzcycHhdIGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gY29udGFpbmVyLXJlc3BvbnNpdmUgdy1mdWxsXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIGZsZXgtMVwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIGdhcC00IGxnOmdhcC02XCI+XG4gICAgICAgICAgICA8TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICAgICAgPEltYWdlXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy0xNiBoLWF1dG8gYXNwZWN0LWF1dG8gbGc6dy1bMjAwcHhdIGxnOmgtYXV0b1wiXG4gICAgICAgICAgICAgICAgYWx0PVwiU3ByaW5nIFNvbHV0aW9ucyBDb21wYW55IExvZ29cIlxuICAgICAgICAgICAgICAgIHNyYz1cIi9sb2dvLnBuZ1wiXG4gICAgICAgICAgICAgICAgd2lkdGg9ezg0fVxuICAgICAgICAgICAgICAgIGhlaWdodD17MzZ9XG4gICAgICAgICAgICAgICAgcHJpb3JpdHlcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvTGluaz5cblxuICAgICAgICAgICAgey8qIERlc2t0b3AgTmF2aWdhdGlvbiAqL31cbiAgICAgICAgICAgIDxOYXZpZ2F0aW9uTWVudSBjbGFzc05hbWU9XCJoaWRkZW4gbGc6ZmxleFwiPlxuICAgICAgICAgICAgICA8TmF2aWdhdGlvbk1lbnVMaXN0IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC02IGxnOmdhcC04XCI+XG4gICAgICAgICAgICAgICAge25hdmlnYXRpb25JdGVtcy5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICA8TmF2aWdhdGlvbk1lbnVJdGVtIGtleT17aW5kZXh9PlxuICAgICAgICAgICAgICAgICAgICA8TGluayBocmVmPXtpdGVtLmhyZWZ9PlxuICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmb250LXRleHQtcmVndWxhci1ub3JtYWwgdGV4dC1bIzAxMDEwYV0gaG92ZXI6YmctWyMwMTAxMGEwZF0gdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtpdGVtLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICAgIDwvTmF2aWdhdGlvbk1lbnVJdGVtPlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L05hdmlnYXRpb25NZW51TGlzdD5cbiAgICAgICAgICAgIDwvTmF2aWdhdGlvbk1lbnU+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIGxnOmdhcC00XCI+XG4gICAgICAgICAgICB7LyogRGVza3RvcCBDVEEgQnV0dG9ucyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIHNtOmZsZXggaXRlbXMtY2VudGVyIGdhcC0yIGxnOmdhcC00XCI+XG4gICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvY29udGFjdFwiPlxuICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgbGc6cHgtNSBweS0yIGJnLVsjMDEwMTBhMGRdIHJvdW5kZWQtWzEwMHB4XSBib3JkZXItYi00IGJvcmRlci1bIzAxMDEwYTI2XSBob3ZlcjpiZy1bIzAxMDEwYTFhXSB0ZXh0LXNtIGxnOnRleHQtYmFzZSB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC10ZXh0LXJlZ3VsYXItbWVkaXVtIHRleHQtWyMwMTAxMGFdIHRleHQtW2xlbmd0aDp2YXIoLS10ZXh0LXJlZ3VsYXItbWVkaXVtLWZvbnQtc2l6ZSldIHRyYWNraW5nLVt2YXIoLS10ZXh0LXJlZ3VsYXItbWVkaXVtLWxldHRlci1zcGFjaW5nKV0gbGVhZGluZy1bdmFyKC0tdGV4dC1yZWd1bGFyLW1lZGl1bS1saW5lLWhlaWdodCldXCI+XG4gICAgICAgICAgICAgICAgICAgIENvbnRhY3RcbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPC9MaW5rPlxuXG4gICAgICAgICAgICAgIDxRdW90ZU1vZGFsPlxuICAgICAgICAgICAgICAgIDxCdXR0b24gY2xhc3NOYW1lPVwicHgtMyBsZzpweC01IHB5LTIgYmctWyMxNzE3YzRdIHJvdW5kZWQtWzEwMHB4XSBib3JkZXItYi00IGJvcmRlci1bIzEyMTI5Y10gaG92ZXI6YmctWyMxNDE0YThdIHRleHQtc20gbGc6dGV4dC1iYXNlIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LXRleHQtcmVndWxhci1tZWRpdW0gdGV4dC13aGl0ZSB0ZXh0LVtsZW5ndGg6dmFyKC0tdGV4dC1yZWd1bGFyLW1lZGl1bS1mb250LXNpemUpXSB0cmFja2luZy1bdmFyKC0tdGV4dC1yZWd1bGFyLW1lZGl1bS1sZXR0ZXItc3BhY2luZyldIGxlYWRpbmctW3ZhcigtLXRleHQtcmVndWxhci1tZWRpdW0tbGluZS1oZWlnaHQpXVwiPlxuICAgICAgICAgICAgICAgICAgICBHZXQgUXVvdGVcbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPC9RdW90ZU1vZGFsPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBNb2JpbGUgTWVudSBCdXR0b24gKi99XG4gICAgICAgICAgICA8U2hlZXQgb3Blbj17aXNNb2JpbGVNZW51T3Blbn0gb25PcGVuQ2hhbmdlPXtzZXRJc01vYmlsZU1lbnVPcGVufT5cbiAgICAgICAgICAgICAgPFNoZWV0VHJpZ2dlciBhc0NoaWxkPlxuICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICBzaXplPVwiaWNvblwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJsZzpoaWRkZW4gdy0xMCBoLTEwIHAtMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciByb3VuZGVkLWZ1bGwgaG92ZXI6YmctWyMwMTAxMGEwZF0gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCJcbiAgICAgICAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJPcGVuIG1vYmlsZSBtZW51XCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHctNiBoLTYgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgPE1lbnVJY29uXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy02IGgtNiB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzTW9iaWxlTWVudU9wZW5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcIm9wYWNpdHktMCByb3RhdGUtMTgwIHNjYWxlLTBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwib3BhY2l0eS0xMDAgcm90YXRlLTAgc2NhbGUtMTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPFhcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LTYgaC02IGFic29sdXRlIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCAke1xuICAgICAgICAgICAgICAgICAgICAgICAgaXNNb2JpbGVNZW51T3BlblxuICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwib3BhY2l0eS0xMDAgcm90YXRlLTAgc2NhbGUtMTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcIm9wYWNpdHktMCByb3RhdGUtMTgwIHNjYWxlLTBcIlxuICAgICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDwvU2hlZXRUcmlnZ2VyPlxuICAgICAgICAgICAgICA8U2hlZXRDb250ZW50XG4gICAgICAgICAgICAgICAgc2lkZT1cInJpZ2h0XCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIG1heC13LW5vbmUgcm91bmRlZC1ub25lIGJvcmRlci1ub25lIHNoYWRvdy1ub25lIGJnLXdoaXRlIGZpeGVkIGluc2V0LTAgZGF0YS1bc3RhdGU9b3Blbl06YW5pbWF0ZS1pbiBkYXRhLVtzdGF0ZT1jbG9zZWRdOmFuaW1hdGUtb3V0IGRhdGEtW3N0YXRlPWNsb3NlZF06c2xpZGUtb3V0LXRvLXJpZ2h0IGRhdGEtW3N0YXRlPW9wZW5dOnNsaWRlLWluLWZyb20tcmlnaHQgZGF0YS1bc3RhdGU9Y2xvc2VkXTpmYWRlLW91dC0wIGRhdGEtW3N0YXRlPW9wZW5dOmZhZGUtaW4tMCBkdXJhdGlvbi0zMDBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgey8qIEhpZGRlbiBTaGVldCBUaXRsZSBmb3IgQWNjZXNzaWJpbGl0eSAqL31cbiAgICAgICAgICAgICAgICA8U2hlZXRIZWFkZXIgY2xhc3NOYW1lPVwic3Itb25seVwiPlxuICAgICAgICAgICAgICAgICAgPFNoZWV0VGl0bGU+TmF2aWdhdGlvbiBNZW51PC9TaGVldFRpdGxlPlxuICAgICAgICAgICAgICAgIDwvU2hlZXRIZWFkZXI+XG5cbiAgICAgICAgICAgICAgICB7LyogTW9iaWxlIEhlYWRlciAtIEFsaWduZWQgd2l0aCBtYWluIGhlYWRlciAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaC0xNiBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIGNvbnRhaW5lci1yZXNwb25zaXZlIHctZnVsbCBib3JkZXItYiBib3JkZXItWyMwMTAxMGEyNl1cIj5cbiAgICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICAgIGhyZWY9XCIvXCJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMFwiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzTW9iaWxlTWVudU9wZW4oZmFsc2UpfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8SW1hZ2VcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTE2IGgtN1wiXG4gICAgICAgICAgICAgICAgICAgICAgYWx0PVwiU3ByaW5nIFNvbHV0aW9ucyBDb21wYW55IExvZ29cIlxuICAgICAgICAgICAgICAgICAgICAgIHNyYz1cIi9jb21wYW55LWxvZ28uc3ZnXCJcbiAgICAgICAgICAgICAgICAgICAgICB3aWR0aD17ODR9XG4gICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0PXszNn1cbiAgICAgICAgICAgICAgICAgICAgICBwcmlvcml0eVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qIE1vYmlsZSBNZW51IENvbnRlbnQgLSBDZW50ZXJlZCAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaC1bY2FsYygxMDB2aC00cmVtKV0ganVzdGlmeS1jZW50ZXIgaXRlbXMtY2VudGVyIGNvbnRhaW5lci1yZXNwb25zaXZlIHB5LThcIj5cbiAgICAgICAgICAgICAgICAgIHsvKiBNb2JpbGUgTmF2aWdhdGlvbiBMaW5rcyAtIENlbnRlcmVkICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlciBzcGFjZS15LTggZmxleC0xIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIHtuYXZpZ2F0aW9uSXRlbXMubWFwKChpdGVtLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj17aXRlbS5ocmVmfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktNCBweC04IHRleHQtWyMwMTAxMGFdIGZvbnQtdGV4dC1sYXJnZS1zZW1pLWJvbGQgaG92ZXI6YmctWyMwMTAxMGEwZF0gcm91bmRlZC14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgdHJhbnNmb3JtIGhvdmVyOnNjYWxlLVsxLjA1XSBhY3RpdmU6c2NhbGUtWzAuOTVdXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzTW9iaWxlTWVudU9wZW4oZmFsc2UpfVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtMnhsXCI+e2l0ZW0ubmFtZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7LyogTW9iaWxlIENUQSBCdXR0b25zIC0gQ2VudGVyZWQgKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGdhcC00IHctZnVsbCBtYXgtdy1zbVwiPlxuICAgICAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2NvbnRhY3RcIiBjbGFzc05hbWU9XCJ3LWZ1bGxcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNiBweS00IGJnLVsjMDEwMTBhMGRdIHJvdW5kZWQtMnhsIGJvcmRlci0yIGJvcmRlci1bIzAxMDEwYTI2XSBob3ZlcjpiZy1bIzAxMDEwYTFhXSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgdHJhbnNmb3JtIGhvdmVyOnNjYWxlLVsxLjAyXSBhY3RpdmU6c2NhbGUtWzAuOThdXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzTW9iaWxlTWVudU9wZW4oZmFsc2UpfVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtdGV4dC1yZWd1bGFyLW1lZGl1bSB0ZXh0LVsjMDEwMTBhXSB0ZXh0LWxnXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIENvbnRhY3QgVXNcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuXG4gICAgICAgICAgICAgICAgICAgIDxRdW90ZU1vZGFsPlxuICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC02IHB5LTQgYmctWyMxNzE3YzRdIHJvdW5kZWQtMnhsIGJvcmRlci0yIGJvcmRlci1bIzEyMTI5Y10gaG92ZXI6YmctWyMxNDE0YThdIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCB0cmFuc2Zvcm0gaG92ZXI6c2NhbGUtWzEuMDJdIGFjdGl2ZTpzY2FsZS1bMC45OF1cIlxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNNb2JpbGVNZW51T3BlbihmYWxzZSl9XG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC10ZXh0LXJlZ3VsYXItbWVkaXVtIHRleHQtd2hpdGUgdGV4dC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBHZXQgUXVvdGVcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9RdW90ZU1vZGFsPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvU2hlZXRDb250ZW50PlxuICAgICAgICAgICAgPC9TaGVldD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2hlYWRlcj5cbiAgKTtcbn07XG4iXSwibmFtZXMiOlsiTWVudUljb24iLCJYIiwiSW1hZ2UiLCJMaW5rIiwiUmVhY3QiLCJ1c2VTdGF0ZSIsIkJ1dHRvbiIsIlNoZWV0IiwiU2hlZXRDb250ZW50IiwiU2hlZXRIZWFkZXIiLCJTaGVldFRpdGxlIiwiU2hlZXRUcmlnZ2VyIiwiTmF2aWdhdGlvbk1lbnUiLCJOYXZpZ2F0aW9uTWVudUxpc3QiLCJOYXZpZ2F0aW9uTWVudUl0ZW0iLCJRdW90ZU1vZGFsIiwiTmF2aWdhdGlvbkJhclNlY3Rpb24iLCJpc01vYmlsZU1lbnVPcGVuIiwic2V0SXNNb2JpbGVNZW51T3BlbiIsIm5hdmlnYXRpb25JdGVtcyIsIm5hbWUiLCJocmVmIiwiaGVhZGVyIiwiY2xhc3NOYW1lIiwiZGl2IiwiYWx0Iiwic3JjIiwid2lkdGgiLCJoZWlnaHQiLCJwcmlvcml0eSIsIm1hcCIsIml0ZW0iLCJpbmRleCIsInZhcmlhbnQiLCJzcGFuIiwib3BlbiIsIm9uT3BlbkNoYW5nZSIsImFzQ2hpbGQiLCJzaXplIiwiYXJpYS1sYWJlbCIsInNpZGUiLCJvbkNsaWNrIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\n"));

/***/ })

});