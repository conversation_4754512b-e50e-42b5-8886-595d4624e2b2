"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contact/page",{

/***/ "(app-pages-browser)/./src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx":
/*!***********************************************************************************************!*\
  !*** ./src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx ***!
  \***********************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContactInfoSection: () => (/* binding */ ContactInfoSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _lib_email__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/email */ \"(app-pages-browser)/./src/lib/email.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _lib_content__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/content */ \"(app-pages-browser)/./src/lib/content.ts\");\n/* __next_internal_client_entry_do_not_use__ ContactInfoSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst ContactInfoSection = ()=>{\n    _s();\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get form content from centralized JSON\n    const formContent = (0,_lib_content__WEBPACK_IMPORTED_MODULE_9__.getContactForm)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        email: \"\",\n        company: \"\",\n        message: \"\"\n    });\n    const [agreeToTerms, setAgreeToTerms] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!agreeToTerms) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"Please agree to the terms and conditions\");\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            await (0,_lib_email__WEBPACK_IMPORTED_MODULE_7__.sendContactEmail)(formData);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"Message sent successfully! We'll get back to you soon.\");\n            // Reset form\n            setFormData({\n                name: \"\",\n                email: \"\",\n                company: \"\",\n                message: \"\"\n            });\n            setAgreeToTerms(false);\n        } catch (error) {\n            console.error(\"Error submitting contact form:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"Failed to send message. Please try again or contact us directly.\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"flex flex-col items-center gap-12 lg:gap-20 section-padding relative self-stretch w-full bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-col container-responsive items-start gap-12 lg:gap-20 w-full flex relative\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col lg:flex-row items-start gap-12 lg:gap-20 relative self-stretch w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 grow relative order-2 lg:order-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            className: \"w-full h-64 sm:h-96 lg:h-[734px] object-cover rounded-lg\",\n                            alt: \"Modern manufacturing facility with advanced spring production equipment, motors, and gear systems\",\n                            src: \"https://images.pexels.com/photos/1108572/pexels-photo-1108572.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2\",\n                            width: 600,\n                            height: 734,\n                            sizes: \"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 600px\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-start gap-6 lg:gap-8 relative flex-1 grow order-1 lg:order-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"gap-4 self-stretch w-full flex flex-col items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center self-stretch\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-heading-tagline font-[number:var(--heading-tagline-font-weight)] text-[#01010a] text-[length:var(--heading-tagline-font-size)] tracking-[var(--heading-tagline-letter-spacing)] leading-[var(--heading-tagline-line-height)] [font-style:var(--heading-tagline-font-style)]\",\n                                            children: formContent.tagline\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-start gap-4 lg:gap-6 self-stretch w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"self-stretch font-heading-h2 font-[number:var(--heading-h2-font-weight)] text-[#01010a] text-[length:var(--heading-h2-font-size)] tracking-[var(--heading-h2-letter-spacing)] leading-[var(--heading-h2-line-height)] [font-style:var(--heading-h2-font-style)] text-balance\",\n                                                children: \"Request a Quote\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"self-stretch font-text-medium-normal font-[number:var(--text-medium-normal-font-weight)] text-[#01010a] text-[length:var(--text-medium-normal-font-size)] tracking-[var(--text-medium-normal-letter-spacing)] leading-[var(--text-medium-normal-line-height)] [font-style:var(--text-medium-normal-font-style)]\",\n                                                children: \"We'd love to hear from you. Get in touch today for a custom quote or technical consultation!\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"flex flex-col items-start gap-4 lg:gap-6 self-stretch w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-start gap-2 self-stretch w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"name\",\n                                                className: \"self-stretch font-text-regular-normal font-[number:var(--text-regular-normal-font-weight)] text-[#01010a] text-[length:var(--text-regular-normal-font-size)] tracking-[var(--text-regular-normal-letter-spacing)] leading-[var(--text-regular-normal-line-height)] [font-style:var(--text-regular-normal-font-style)]\",\n                                                children: \"Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"name\",\n                                                required: true,\n                                                value: formData.name,\n                                                onChange: (e)=>handleInputChange(\"name\", e.target.value),\n                                                className: \"self-stretch bg-[#01010a0d] rounded-xl border-transparent focus:border-[#1717c4] focus:ring-[#1717c4]\",\n                                                placeholder: \"Enter your full name\",\n                                                disabled: isSubmitting\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-start gap-2 self-stretch w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"email\",\n                                                className: \"self-stretch font-text-regular-normal font-[number:var(--text-regular-normal-font-weight)] text-[#01010a] text-[length:var(--text-regular-normal-font-size)] tracking-[var(--text-regular-normal-letter-spacing)] leading-[var(--text-regular-normal-line-height)] [font-style:var(--text-regular-normal-font-style)]\",\n                                                children: \"Email *\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"email\",\n                                                type: \"email\",\n                                                required: true,\n                                                value: formData.email,\n                                                onChange: (e)=>handleInputChange(\"email\", e.target.value),\n                                                className: \"self-stretch bg-[#01010a0d] rounded-xl border-transparent focus:border-[#1717c4] focus:ring-[#1717c4]\",\n                                                placeholder: \"Enter your email address\",\n                                                disabled: isSubmitting\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-start gap-2 self-stretch w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"company\",\n                                                className: \"self-stretch font-text-regular-normal font-[number:var(--text-regular-normal-font-weight)] text-[#01010a] text-[length:var(--text-regular-normal-font-size)] tracking-[var(--text-regular-normal-letter-spacing)] leading-[var(--text-regular-normal-line-height)] [font-style:var(--text-regular-normal-font-style)]\",\n                                                children: \"Company\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"company\",\n                                                value: formData.company,\n                                                onChange: (e)=>handleInputChange(\"company\", e.target.value),\n                                                className: \"self-stretch bg-[#01010a0d] rounded-xl border-transparent focus:border-[#1717c4] focus:ring-[#1717c4]\",\n                                                placeholder: \"Enter your company name\",\n                                                disabled: isSubmitting\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-start gap-2 self-stretch w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"message\",\n                                                className: \"self-stretch font-text-regular-normal font-[number:var(--text-regular-normal-font-weight)] text-[#01010a] text-[length:var(--text-regular-normal-font-size)] tracking-[var(--text-regular-normal-letter-spacing)] leading-[var(--text-regular-normal-line-height)] [font-style:var(--text-regular-normal-font-style)]\",\n                                                children: \"Project Details *\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                id: \"message\",\n                                                required: true,\n                                                value: formData.message,\n                                                onChange: (e)=>handleInputChange(\"message\", e.target.value),\n                                                placeholder: \"Tell us about your spring requirements, specifications, quantities, and timeline...\",\n                                                className: \"h-32 lg:h-[182px] self-stretch bg-[#01010a0d] rounded-xl border-transparent font-text-regular-normal text-[#01010a99] focus:border-[#1717c4] focus:ring-[#1717c4] resize-none\",\n                                                disabled: isSubmitting\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start gap-3 pb-2 lg:pb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_4__.Checkbox, {\n                                                id: \"terms\",\n                                                checked: agreeToTerms,\n                                                onCheckedChange: (checked)=>setAgreeToTerms(checked),\n                                                className: \"w-5 h-5 bg-[#01010a0d] rounded border-transparent mt-0.5\",\n                                                disabled: isSubmitting\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"terms\",\n                                                className: \"font-text-small-normal font-[number:var(--text-small-normal-font-weight)] text-[#01010a] text-[length:var(--text-small-normal-font-size)] tracking-[var(--text-small-normal-letter-spacing)] leading-[var(--text-small-normal-line-height)] [font-style:var(--text-small-normal-font-style)] flex-1\",\n                                                children: \"I agree to the Terms of Service and Privacy Policy, and consent to receive communications about spring manufacturing services.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        type: \"submit\",\n                                        disabled: !agreeToTerms || isSubmitting,\n                                        className: \"w-full sm:w-auto px-6 py-2.5 bg-[#1717c4] rounded-[100px] border-b-4 border-[#12129c] font-text-regular-medium font-[number:var(--text-regular-medium-font-weight)] text-white text-[length:var(--text-regular-medium-font-size)] tracking-[var(--text-regular-medium-letter-spacing)] leading-[var(--text-regular-medium-line-height)] [font-style:var(--text-regular-medium-font-style)] hover:bg-[#1414a8] transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: isSubmitting ? \"Submitting...\" : \"Submit Request\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ContactInfoSection, \"vQQNjrvaiwIjgMSDuqNHYNxeAt8=\");\n_c = ContactInfoSection;\nvar _c;\n$RefreshReg$(_c, \"ContactInfoSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\n"));

/***/ })

});