"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contact/page",{

/***/ "(app-pages-browser)/./src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx":
/*!***********************************************************************************************!*\
  !*** ./src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx ***!
  \***********************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContactInfoSection: () => (/* binding */ ContactInfoSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _lib_email__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/email */ \"(app-pages-browser)/./src/lib/email.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _lib_content__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/content */ \"(app-pages-browser)/./src/lib/content.ts\");\n/* __next_internal_client_entry_do_not_use__ ContactInfoSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst ContactInfoSection = ()=>{\n    _s();\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get form content from centralized JSON\n    const formContent = (0,_lib_content__WEBPACK_IMPORTED_MODULE_9__.getContactForm)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        email: \"\",\n        company: \"\",\n        message: \"\"\n    });\n    const [agreeToTerms, setAgreeToTerms] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!agreeToTerms) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"Please agree to the terms and conditions\");\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            await (0,_lib_email__WEBPACK_IMPORTED_MODULE_7__.sendContactEmail)(formData);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"Message sent successfully! We'll get back to you soon.\");\n            // Reset form\n            setFormData({\n                name: \"\",\n                email: \"\",\n                company: \"\",\n                message: \"\"\n            });\n            setAgreeToTerms(false);\n        } catch (error) {\n            console.error(\"Error submitting contact form:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"Failed to send message. Please try again or contact us directly.\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"flex flex-col items-center gap-12 lg:gap-20 section-padding relative self-stretch w-full bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-col container-responsive items-start gap-12 lg:gap-20 w-full flex relative\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col lg:flex-row items-start gap-12 lg:gap-20 relative self-stretch w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 grow relative order-2 lg:order-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            className: \"w-full h-64 sm:h-96 lg:h-[734px] object-cover rounded-lg\",\n                            alt: \"Modern manufacturing facility with advanced spring production equipment, motors, and gear systems\",\n                            src: \"https://images.pexels.com/photos/1108572/pexels-photo-1108572.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2\",\n                            width: 600,\n                            height: 734,\n                            sizes: \"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 600px\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-start gap-6 lg:gap-8 relative flex-1 grow order-1 lg:order-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"gap-4 self-stretch w-full flex flex-col items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center self-stretch\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-heading-tagline font-[number:var(--heading-tagline-font-weight)] text-[#01010a] text-[length:var(--heading-tagline-font-size)] tracking-[var(--heading-tagline-letter-spacing)] leading-[var(--heading-tagline-line-height)] [font-style:var(--heading-tagline-font-style)]\",\n                                            children: \"Connect\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-start gap-4 lg:gap-6 self-stretch w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"self-stretch font-heading-h2 font-[number:var(--heading-h2-font-weight)] text-[#01010a] text-[length:var(--heading-h2-font-size)] tracking-[var(--heading-h2-letter-spacing)] leading-[var(--heading-h2-line-height)] [font-style:var(--heading-h2-font-style)] text-balance\",\n                                                children: \"Request a Quote\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"self-stretch font-text-medium-normal font-[number:var(--text-medium-normal-font-weight)] text-[#01010a] text-[length:var(--text-medium-normal-font-size)] tracking-[var(--text-medium-normal-letter-spacing)] leading-[var(--text-medium-normal-line-height)] [font-style:var(--text-medium-normal-font-style)]\",\n                                                children: \"We'd love to hear from you. Get in touch today for a custom quote or technical consultation!\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"flex flex-col items-start gap-4 lg:gap-6 self-stretch w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-start gap-2 self-stretch w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"name\",\n                                                className: \"self-stretch font-text-regular-normal font-[number:var(--text-regular-normal-font-weight)] text-[#01010a] text-[length:var(--text-regular-normal-font-size)] tracking-[var(--text-regular-normal-letter-spacing)] leading-[var(--text-regular-normal-line-height)] [font-style:var(--text-regular-normal-font-style)]\",\n                                                children: \"Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"name\",\n                                                required: true,\n                                                value: formData.name,\n                                                onChange: (e)=>handleInputChange(\"name\", e.target.value),\n                                                className: \"self-stretch bg-[#01010a0d] rounded-xl border-transparent focus:border-[#1717c4] focus:ring-[#1717c4]\",\n                                                placeholder: \"Enter your full name\",\n                                                disabled: isSubmitting\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-start gap-2 self-stretch w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"email\",\n                                                className: \"self-stretch font-text-regular-normal font-[number:var(--text-regular-normal-font-weight)] text-[#01010a] text-[length:var(--text-regular-normal-font-size)] tracking-[var(--text-regular-normal-letter-spacing)] leading-[var(--text-regular-normal-line-height)] [font-style:var(--text-regular-normal-font-style)]\",\n                                                children: \"Email *\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"email\",\n                                                type: \"email\",\n                                                required: true,\n                                                value: formData.email,\n                                                onChange: (e)=>handleInputChange(\"email\", e.target.value),\n                                                className: \"self-stretch bg-[#01010a0d] rounded-xl border-transparent focus:border-[#1717c4] focus:ring-[#1717c4]\",\n                                                placeholder: \"Enter your email address\",\n                                                disabled: isSubmitting\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-start gap-2 self-stretch w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"company\",\n                                                className: \"self-stretch font-text-regular-normal font-[number:var(--text-regular-normal-font-weight)] text-[#01010a] text-[length:var(--text-regular-normal-font-size)] tracking-[var(--text-regular-normal-letter-spacing)] leading-[var(--text-regular-normal-line-height)] [font-style:var(--text-regular-normal-font-style)]\",\n                                                children: \"Company\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"company\",\n                                                value: formData.company,\n                                                onChange: (e)=>handleInputChange(\"company\", e.target.value),\n                                                className: \"self-stretch bg-[#01010a0d] rounded-xl border-transparent focus:border-[#1717c4] focus:ring-[#1717c4]\",\n                                                placeholder: \"Enter your company name\",\n                                                disabled: isSubmitting\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-start gap-2 self-stretch w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"message\",\n                                                className: \"self-stretch font-text-regular-normal font-[number:var(--text-regular-normal-font-weight)] text-[#01010a] text-[length:var(--text-regular-normal-font-size)] tracking-[var(--text-regular-normal-letter-spacing)] leading-[var(--text-regular-normal-line-height)] [font-style:var(--text-regular-normal-font-style)]\",\n                                                children: \"Project Details *\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                id: \"message\",\n                                                required: true,\n                                                value: formData.message,\n                                                onChange: (e)=>handleInputChange(\"message\", e.target.value),\n                                                placeholder: \"Tell us about your spring requirements, specifications, quantities, and timeline...\",\n                                                className: \"h-32 lg:h-[182px] self-stretch bg-[#01010a0d] rounded-xl border-transparent font-text-regular-normal text-[#01010a99] focus:border-[#1717c4] focus:ring-[#1717c4] resize-none\",\n                                                disabled: isSubmitting\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start gap-3 pb-2 lg:pb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_4__.Checkbox, {\n                                                id: \"terms\",\n                                                checked: agreeToTerms,\n                                                onCheckedChange: (checked)=>setAgreeToTerms(checked),\n                                                className: \"w-5 h-5 bg-[#01010a0d] rounded border-transparent mt-0.5\",\n                                                disabled: isSubmitting\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"terms\",\n                                                className: \"font-text-small-normal font-[number:var(--text-small-normal-font-weight)] text-[#01010a] text-[length:var(--text-small-normal-font-size)] tracking-[var(--text-small-normal-letter-spacing)] leading-[var(--text-small-normal-line-height)] [font-style:var(--text-small-normal-font-style)] flex-1\",\n                                                children: \"I agree to the Terms of Service and Privacy Policy, and consent to receive communications about spring manufacturing services.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        type: \"submit\",\n                                        disabled: !agreeToTerms || isSubmitting,\n                                        className: \"w-full sm:w-auto px-6 py-2.5 bg-[#1717c4] rounded-[100px] border-b-4 border-[#12129c] font-text-regular-medium font-[number:var(--text-regular-medium-font-weight)] text-white text-[length:var(--text-regular-medium-font-size)] tracking-[var(--text-regular-medium-letter-spacing)] leading-[var(--text-regular-medium-line-height)] [font-style:var(--text-regular-medium-font-style)] hover:bg-[#1414a8] transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: isSubmitting ? \"Submitting...\" : \"Submit Request\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ContactInfoSection, \"vQQNjrvaiwIjgMSDuqNHYNxeAt8=\");\n_c = ContactInfoSection;\nvar _c;\n$RefreshReg$(_c, \"ContactInfoSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3NjcmVlbnMvSG9tZURlc2t0b3Avc2VjdGlvbnMvQ29udGFjdEluZm9TZWN0aW9uL0NvbnRhY3RJbmZvU2VjdGlvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFd0M7QUFDVDtBQUNpQjtBQUNJO0FBQ047QUFDTTtBQUNZO0FBQzVCO0FBQ1c7QUFFeEMsTUFBTVUscUJBQXFCOztJQUNoQyxNQUFNLENBQUNDLGNBQWNDLGdCQUFnQixHQUFHWCwrQ0FBUUEsQ0FBQztJQUVqRCx5Q0FBeUM7SUFDekMsTUFBTVksY0FBY0osNERBQWNBO0lBQ2xDLE1BQU0sQ0FBQ0ssVUFBVUMsWUFBWSxHQUFHZCwrQ0FBUUEsQ0FBa0I7UUFDeERlLE1BQU07UUFDTkMsT0FBTztRQUNQQyxTQUFTO1FBQ1RDLFNBQVM7SUFDWDtJQUNBLE1BQU0sQ0FBQ0MsY0FBY0MsZ0JBQWdCLEdBQUdwQiwrQ0FBUUEsQ0FBQztJQUVqRCxNQUFNcUIsZUFBZSxPQUFPQztRQUMxQkEsRUFBRUMsY0FBYztRQUVoQixJQUFJLENBQUNKLGNBQWM7WUFDakJaLHVEQUFLQSxDQUFDaUIsS0FBSyxDQUFDO1lBQ1o7UUFDRjtRQUVBYixnQkFBZ0I7UUFFaEIsSUFBSTtZQUNGLE1BQU1MLDREQUFnQkEsQ0FBQ087WUFDdkJOLHVEQUFLQSxDQUFDa0IsT0FBTyxDQUFDO1lBRWQsYUFBYTtZQUNiWCxZQUFZO2dCQUNWQyxNQUFNO2dCQUNOQyxPQUFPO2dCQUNQQyxTQUFTO2dCQUNUQyxTQUFTO1lBQ1g7WUFDQUUsZ0JBQWdCO1FBQ2xCLEVBQUUsT0FBT0ksT0FBTztZQUNkRSxRQUFRRixLQUFLLENBQUMsa0NBQWtDQTtZQUNoRGpCLHVEQUFLQSxDQUFDaUIsS0FBSyxDQUNUO1FBRUosU0FBVTtZQUNSYixnQkFBZ0I7UUFDbEI7SUFDRjtJQUVBLE1BQU1nQixvQkFBb0IsQ0FBQ0MsT0FBOEJDO1FBQ3ZEZixZQUFZLENBQUNnQixPQUFVO2dCQUFFLEdBQUdBLElBQUk7Z0JBQUUsQ0FBQ0YsTUFBTSxFQUFFQztZQUFNO0lBQ25EO0lBRUEscUJBQ0UsOERBQUNFO1FBQVFDLFdBQVU7a0JBQ2pCLDRFQUFDQztZQUFJRCxXQUFVO3NCQUNiLDRFQUFDQztnQkFBSUQsV0FBVTs7a0NBQ2IsOERBQUNDO3dCQUFJRCxXQUFVO2tDQUNiLDRFQUFDL0Isa0RBQUtBOzRCQUNKK0IsV0FBVTs0QkFDVkUsS0FBSTs0QkFDSkMsS0FBSTs0QkFDSkMsT0FBTzs0QkFDUEMsUUFBUTs0QkFDUkMsT0FBTTs7Ozs7Ozs7Ozs7a0NBSVYsOERBQUNMO3dCQUFJRCxXQUFVOzswQ0FDYiw4REFBQ0M7Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDQzt3Q0FBSUQsV0FBVTtrREFDYiw0RUFBQ087NENBQUtQLFdBQVU7c0RBQW1SOzs7Ozs7Ozs7OztrREFLclMsOERBQUNDO3dDQUFJRCxXQUFVOzswREFDYiw4REFBQ1E7Z0RBQUdSLFdBQVU7MERBQStROzs7Ozs7MERBSTdSLDhEQUFDUztnREFBRVQsV0FBVTswREFBa1Q7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FPblUsOERBQUNVO2dDQUNDQyxVQUFVdEI7Z0NBQ1ZXLFdBQVU7O2tEQUVWLDhEQUFDQzt3Q0FBSUQsV0FBVTs7MERBQ2IsOERBQUNZO2dEQUNDQyxTQUFRO2dEQUNSYixXQUFVOzBEQUNYOzs7Ozs7MERBR0QsOERBQUM1Qix1REFBS0E7Z0RBQ0owQyxJQUFHO2dEQUNIQyxRQUFRO2dEQUNSbEIsT0FBT2hCLFNBQVNFLElBQUk7Z0RBQ3BCaUMsVUFBVSxDQUFDMUIsSUFBTUssa0JBQWtCLFFBQVFMLEVBQUUyQixNQUFNLENBQUNwQixLQUFLO2dEQUN6REcsV0FBVTtnREFDVmtCLGFBQVk7Z0RBQ1pDLFVBQVV6Qzs7Ozs7Ozs7Ozs7O2tEQUlkLDhEQUFDdUI7d0NBQUlELFdBQVU7OzBEQUNiLDhEQUFDWTtnREFDQ0MsU0FBUTtnREFDUmIsV0FBVTswREFDWDs7Ozs7OzBEQUdELDhEQUFDNUIsdURBQUtBO2dEQUNKMEMsSUFBRztnREFDSE0sTUFBSztnREFDTEwsUUFBUTtnREFDUmxCLE9BQU9oQixTQUFTRyxLQUFLO2dEQUNyQmdDLFVBQVUsQ0FBQzFCLElBQU1LLGtCQUFrQixTQUFTTCxFQUFFMkIsTUFBTSxDQUFDcEIsS0FBSztnREFDMURHLFdBQVU7Z0RBQ1ZrQixhQUFZO2dEQUNaQyxVQUFVekM7Ozs7Ozs7Ozs7OztrREFJZCw4REFBQ3VCO3dDQUFJRCxXQUFVOzswREFDYiw4REFBQ1k7Z0RBQ0NDLFNBQVE7Z0RBQ1JiLFdBQVU7MERBQ1g7Ozs7OzswREFHRCw4REFBQzVCLHVEQUFLQTtnREFDSjBDLElBQUc7Z0RBQ0hqQixPQUFPaEIsU0FBU0ksT0FBTztnREFDdkIrQixVQUFVLENBQUMxQixJQUFNSyxrQkFBa0IsV0FBV0wsRUFBRTJCLE1BQU0sQ0FBQ3BCLEtBQUs7Z0RBQzVERyxXQUFVO2dEQUNWa0IsYUFBWTtnREFDWkMsVUFBVXpDOzs7Ozs7Ozs7Ozs7a0RBSWQsOERBQUN1Qjt3Q0FBSUQsV0FBVTs7MERBQ2IsOERBQUNZO2dEQUNDQyxTQUFRO2dEQUNSYixXQUFVOzBEQUNYOzs7Ozs7MERBR0QsOERBQUMzQiw2REFBUUE7Z0RBQ1B5QyxJQUFHO2dEQUNIQyxRQUFRO2dEQUNSbEIsT0FBT2hCLFNBQVNLLE9BQU87Z0RBQ3ZCOEIsVUFBVSxDQUFDMUIsSUFBTUssa0JBQWtCLFdBQVdMLEVBQUUyQixNQUFNLENBQUNwQixLQUFLO2dEQUM1RHFCLGFBQVk7Z0RBQ1psQixXQUFVO2dEQUNWbUIsVUFBVXpDOzs7Ozs7Ozs7Ozs7a0RBSWQsOERBQUN1Qjt3Q0FBSUQsV0FBVTs7MERBQ2IsOERBQUM3Qiw2REFBUUE7Z0RBQ1AyQyxJQUFHO2dEQUNITyxTQUFTbEM7Z0RBQ1RtQyxpQkFBaUIsQ0FBQ0QsVUFDaEJqQyxnQkFBZ0JpQztnREFFbEJyQixXQUFVO2dEQUNWbUIsVUFBVXpDOzs7Ozs7MERBRVosOERBQUNrQztnREFDQ0MsU0FBUTtnREFDUmIsV0FBVTswREFDWDs7Ozs7Ozs7Ozs7O2tEQU9ILDhEQUFDOUIseURBQU1BO3dDQUNMa0QsTUFBSzt3Q0FDTEQsVUFBVSxDQUFDaEMsZ0JBQWdCVDt3Q0FDM0JzQixXQUFVO2tEQUVUdEIsZUFBZSxrQkFBa0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRbEQsRUFBRTtHQWpNV0Q7S0FBQUEiLCJzb3VyY2VzIjpbIi9Vc2Vycy9uaXNoYXBhbmNoYWwvRG9jdW1lbnRzL0dhdXJhdi9Qcm9qZWN0cy9zaGFrdGkvc3JjL2NvbXBvbmVudHMvc2NyZWVucy9Ib21lRGVza3RvcC9zZWN0aW9ucy9Db250YWN0SW5mb1NlY3Rpb24vQ29udGFjdEluZm9TZWN0aW9uLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgSW1hZ2UgZnJvbSBcIm5leHQvaW1hZ2VcIjtcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYnV0dG9uXCI7XG5pbXBvcnQgeyBDaGVja2JveCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvY2hlY2tib3hcIjtcbmltcG9ydCB7IElucHV0IH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9pbnB1dFwiO1xuaW1wb3J0IHsgVGV4dGFyZWEgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3RleHRhcmVhXCI7XG5pbXBvcnQgeyBzZW5kQ29udGFjdEVtYWlsLCBDb250YWN0Rm9ybURhdGEgfSBmcm9tIFwiQC9saWIvZW1haWxcIjtcbmltcG9ydCB0b2FzdCBmcm9tIFwicmVhY3QtaG90LXRvYXN0XCI7XG5pbXBvcnQgeyBnZXRDb250YWN0Rm9ybSB9IGZyb20gXCJAL2xpYi9jb250ZW50XCI7XG5cbmV4cG9ydCBjb25zdCBDb250YWN0SW5mb1NlY3Rpb24gPSAoKTogSlNYLkVsZW1lbnQgPT4ge1xuICBjb25zdCBbaXNTdWJtaXR0aW5nLCBzZXRJc1N1Ym1pdHRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIC8vIEdldCBmb3JtIGNvbnRlbnQgZnJvbSBjZW50cmFsaXplZCBKU09OXG4gIGNvbnN0IGZvcm1Db250ZW50ID0gZ2V0Q29udGFjdEZvcm0oKTtcbiAgY29uc3QgW2Zvcm1EYXRhLCBzZXRGb3JtRGF0YV0gPSB1c2VTdGF0ZTxDb250YWN0Rm9ybURhdGE+KHtcbiAgICBuYW1lOiBcIlwiLFxuICAgIGVtYWlsOiBcIlwiLFxuICAgIGNvbXBhbnk6IFwiXCIsXG4gICAgbWVzc2FnZTogXCJcIixcbiAgfSk7XG4gIGNvbnN0IFthZ3JlZVRvVGVybXMsIHNldEFncmVlVG9UZXJtc10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgY29uc3QgaGFuZGxlU3VibWl0ID0gYXN5bmMgKGU6IFJlYWN0LkZvcm1FdmVudCkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcblxuICAgIGlmICghYWdyZWVUb1Rlcm1zKSB7XG4gICAgICB0b2FzdC5lcnJvcihcIlBsZWFzZSBhZ3JlZSB0byB0aGUgdGVybXMgYW5kIGNvbmRpdGlvbnNcIik7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgc2V0SXNTdWJtaXR0aW5nKHRydWUpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IHNlbmRDb250YWN0RW1haWwoZm9ybURhdGEpO1xuICAgICAgdG9hc3Quc3VjY2VzcyhcIk1lc3NhZ2Ugc2VudCBzdWNjZXNzZnVsbHkhIFdlJ2xsIGdldCBiYWNrIHRvIHlvdSBzb29uLlwiKTtcblxuICAgICAgLy8gUmVzZXQgZm9ybVxuICAgICAgc2V0Rm9ybURhdGEoe1xuICAgICAgICBuYW1lOiBcIlwiLFxuICAgICAgICBlbWFpbDogXCJcIixcbiAgICAgICAgY29tcGFueTogXCJcIixcbiAgICAgICAgbWVzc2FnZTogXCJcIixcbiAgICAgIH0pO1xuICAgICAgc2V0QWdyZWVUb1Rlcm1zKGZhbHNlKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIHN1Ym1pdHRpbmcgY29udGFjdCBmb3JtOlwiLCBlcnJvcik7XG4gICAgICB0b2FzdC5lcnJvcihcbiAgICAgICAgXCJGYWlsZWQgdG8gc2VuZCBtZXNzYWdlLiBQbGVhc2UgdHJ5IGFnYWluIG9yIGNvbnRhY3QgdXMgZGlyZWN0bHkuXCJcbiAgICAgICk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzU3VibWl0dGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUlucHV0Q2hhbmdlID0gKGZpZWxkOiBrZXlvZiBDb250YWN0Rm9ybURhdGEsIHZhbHVlOiBzdHJpbmcpID0+IHtcbiAgICBzZXRGb3JtRGF0YSgocHJldikgPT4gKHsgLi4ucHJldiwgW2ZpZWxkXTogdmFsdWUgfSkpO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIgZ2FwLTEyIGxnOmdhcC0yMCBzZWN0aW9uLXBhZGRpbmcgcmVsYXRpdmUgc2VsZi1zdHJldGNoIHctZnVsbCBiZy13aGl0ZVwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LWNvbCBjb250YWluZXItcmVzcG9uc2l2ZSBpdGVtcy1zdGFydCBnYXAtMTIgbGc6Z2FwLTIwIHctZnVsbCBmbGV4IHJlbGF0aXZlXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBsZzpmbGV4LXJvdyBpdGVtcy1zdGFydCBnYXAtMTIgbGc6Z2FwLTIwIHJlbGF0aXZlIHNlbGYtc3RyZXRjaCB3LWZ1bGxcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBncm93IHJlbGF0aXZlIG9yZGVyLTIgbGc6b3JkZXItMVwiPlxuICAgICAgICAgICAgPEltYWdlXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLTY0IHNtOmgtOTYgbGc6aC1bNzM0cHhdIG9iamVjdC1jb3ZlciByb3VuZGVkLWxnXCJcbiAgICAgICAgICAgICAgYWx0PVwiTW9kZXJuIG1hbnVmYWN0dXJpbmcgZmFjaWxpdHkgd2l0aCBhZHZhbmNlZCBzcHJpbmcgcHJvZHVjdGlvbiBlcXVpcG1lbnQsIG1vdG9ycywgYW5kIGdlYXIgc3lzdGVtc1wiXG4gICAgICAgICAgICAgIHNyYz1cImh0dHBzOi8vaW1hZ2VzLnBleGVscy5jb20vcGhvdG9zLzExMDg1NzIvcGV4ZWxzLXBob3RvLTExMDg1NzIuanBlZz9hdXRvPWNvbXByZXNzJmNzPXRpbnlzcmdiJnc9MTI2MCZoPTc1MCZkcHI9MlwiXG4gICAgICAgICAgICAgIHdpZHRoPXs2MDB9XG4gICAgICAgICAgICAgIGhlaWdodD17NzM0fVxuICAgICAgICAgICAgICBzaXplcz1cIihtYXgtd2lkdGg6IDc2OHB4KSAxMDB2dywgKG1heC13aWR0aDogMTIwMHB4KSA1MHZ3LCA2MDBweFwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGl0ZW1zLXN0YXJ0IGdhcC02IGxnOmdhcC04IHJlbGF0aXZlIGZsZXgtMSBncm93IG9yZGVyLTEgbGc6b3JkZXItMlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJnYXAtNCBzZWxmLXN0cmV0Y2ggdy1mdWxsIGZsZXggZmxleC1jb2wgaXRlbXMtc3RhcnRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgc2VsZi1zdHJldGNoXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1oZWFkaW5nLXRhZ2xpbmUgZm9udC1bbnVtYmVyOnZhcigtLWhlYWRpbmctdGFnbGluZS1mb250LXdlaWdodCldIHRleHQtWyMwMTAxMGFdIHRleHQtW2xlbmd0aDp2YXIoLS1oZWFkaW5nLXRhZ2xpbmUtZm9udC1zaXplKV0gdHJhY2tpbmctW3ZhcigtLWhlYWRpbmctdGFnbGluZS1sZXR0ZXItc3BhY2luZyldIGxlYWRpbmctW3ZhcigtLWhlYWRpbmctdGFnbGluZS1saW5lLWhlaWdodCldIFtmb250LXN0eWxlOnZhcigtLWhlYWRpbmctdGFnbGluZS1mb250LXN0eWxlKV1cIj5cbiAgICAgICAgICAgICAgICAgIENvbm5lY3RcbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1zdGFydCBnYXAtNCBsZzpnYXAtNiBzZWxmLXN0cmV0Y2ggdy1mdWxsXCI+XG4gICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInNlbGYtc3RyZXRjaCBmb250LWhlYWRpbmctaDIgZm9udC1bbnVtYmVyOnZhcigtLWhlYWRpbmctaDItZm9udC13ZWlnaHQpXSB0ZXh0LVsjMDEwMTBhXSB0ZXh0LVtsZW5ndGg6dmFyKC0taGVhZGluZy1oMi1mb250LXNpemUpXSB0cmFja2luZy1bdmFyKC0taGVhZGluZy1oMi1sZXR0ZXItc3BhY2luZyldIGxlYWRpbmctW3ZhcigtLWhlYWRpbmctaDItbGluZS1oZWlnaHQpXSBbZm9udC1zdHlsZTp2YXIoLS1oZWFkaW5nLWgyLWZvbnQtc3R5bGUpXSB0ZXh0LWJhbGFuY2VcIj5cbiAgICAgICAgICAgICAgICAgIFJlcXVlc3QgYSBRdW90ZVxuICAgICAgICAgICAgICAgIDwvaDI+XG5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJzZWxmLXN0cmV0Y2ggZm9udC10ZXh0LW1lZGl1bS1ub3JtYWwgZm9udC1bbnVtYmVyOnZhcigtLXRleHQtbWVkaXVtLW5vcm1hbC1mb250LXdlaWdodCldIHRleHQtWyMwMTAxMGFdIHRleHQtW2xlbmd0aDp2YXIoLS10ZXh0LW1lZGl1bS1ub3JtYWwtZm9udC1zaXplKV0gdHJhY2tpbmctW3ZhcigtLXRleHQtbWVkaXVtLW5vcm1hbC1sZXR0ZXItc3BhY2luZyldIGxlYWRpbmctW3ZhcigtLXRleHQtbWVkaXVtLW5vcm1hbC1saW5lLWhlaWdodCldIFtmb250LXN0eWxlOnZhcigtLXRleHQtbWVkaXVtLW5vcm1hbC1mb250LXN0eWxlKV1cIj5cbiAgICAgICAgICAgICAgICAgIFdlJiMzOTtkIGxvdmUgdG8gaGVhciBmcm9tIHlvdS4gR2V0IGluIHRvdWNoIHRvZGF5IGZvciBhXG4gICAgICAgICAgICAgICAgICBjdXN0b20gcXVvdGUgb3IgdGVjaG5pY2FsIGNvbnN1bHRhdGlvbiFcbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxmb3JtXG4gICAgICAgICAgICAgIG9uU3VibWl0PXtoYW5kbGVTdWJtaXR9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtc3RhcnQgZ2FwLTQgbGc6Z2FwLTYgc2VsZi1zdHJldGNoIHctZnVsbFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1zdGFydCBnYXAtMiBzZWxmLXN0cmV0Y2ggdy1mdWxsXCI+XG4gICAgICAgICAgICAgICAgPGxhYmVsXG4gICAgICAgICAgICAgICAgICBodG1sRm9yPVwibmFtZVwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJzZWxmLXN0cmV0Y2ggZm9udC10ZXh0LXJlZ3VsYXItbm9ybWFsIGZvbnQtW251bWJlcjp2YXIoLS10ZXh0LXJlZ3VsYXItbm9ybWFsLWZvbnQtd2VpZ2h0KV0gdGV4dC1bIzAxMDEwYV0gdGV4dC1bbGVuZ3RoOnZhcigtLXRleHQtcmVndWxhci1ub3JtYWwtZm9udC1zaXplKV0gdHJhY2tpbmctW3ZhcigtLXRleHQtcmVndWxhci1ub3JtYWwtbGV0dGVyLXNwYWNpbmcpXSBsZWFkaW5nLVt2YXIoLS10ZXh0LXJlZ3VsYXItbm9ybWFsLWxpbmUtaGVpZ2h0KV0gW2ZvbnQtc3R5bGU6dmFyKC0tdGV4dC1yZWd1bGFyLW5vcm1hbC1mb250LXN0eWxlKV1cIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIE5hbWUgKlxuICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICBpZD1cIm5hbWVcIlxuICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5uYW1lfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVJbnB1dENoYW5nZShcIm5hbWVcIiwgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwic2VsZi1zdHJldGNoIGJnLVsjMDEwMTBhMGRdIHJvdW5kZWQteGwgYm9yZGVyLXRyYW5zcGFyZW50IGZvY3VzOmJvcmRlci1bIzE3MTdjNF0gZm9jdXM6cmluZy1bIzE3MTdjNF1cIlxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciB5b3VyIGZ1bGwgbmFtZVwiXG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNTdWJtaXR0aW5nfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1zdGFydCBnYXAtMiBzZWxmLXN0cmV0Y2ggdy1mdWxsXCI+XG4gICAgICAgICAgICAgICAgPGxhYmVsXG4gICAgICAgICAgICAgICAgICBodG1sRm9yPVwiZW1haWxcIlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwic2VsZi1zdHJldGNoIGZvbnQtdGV4dC1yZWd1bGFyLW5vcm1hbCBmb250LVtudW1iZXI6dmFyKC0tdGV4dC1yZWd1bGFyLW5vcm1hbC1mb250LXdlaWdodCldIHRleHQtWyMwMTAxMGFdIHRleHQtW2xlbmd0aDp2YXIoLS10ZXh0LXJlZ3VsYXItbm9ybWFsLWZvbnQtc2l6ZSldIHRyYWNraW5nLVt2YXIoLS10ZXh0LXJlZ3VsYXItbm9ybWFsLWxldHRlci1zcGFjaW5nKV0gbGVhZGluZy1bdmFyKC0tdGV4dC1yZWd1bGFyLW5vcm1hbC1saW5lLWhlaWdodCldIFtmb250LXN0eWxlOnZhcigtLXRleHQtcmVndWxhci1ub3JtYWwtZm9udC1zdHlsZSldXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBFbWFpbCAqXG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgIGlkPVwiZW1haWxcIlxuICAgICAgICAgICAgICAgICAgdHlwZT1cImVtYWlsXCJcbiAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuZW1haWx9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUlucHV0Q2hhbmdlKFwiZW1haWxcIiwgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwic2VsZi1zdHJldGNoIGJnLVsjMDEwMTBhMGRdIHJvdW5kZWQteGwgYm9yZGVyLXRyYW5zcGFyZW50IGZvY3VzOmJvcmRlci1bIzE3MTdjNF0gZm9jdXM6cmluZy1bIzE3MTdjNF1cIlxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciB5b3VyIGVtYWlsIGFkZHJlc3NcIlxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzU3VibWl0dGluZ31cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtc3RhcnQgZ2FwLTIgc2VsZi1zdHJldGNoIHctZnVsbFwiPlxuICAgICAgICAgICAgICAgIDxsYWJlbFxuICAgICAgICAgICAgICAgICAgaHRtbEZvcj1cImNvbXBhbnlcIlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwic2VsZi1zdHJldGNoIGZvbnQtdGV4dC1yZWd1bGFyLW5vcm1hbCBmb250LVtudW1iZXI6dmFyKC0tdGV4dC1yZWd1bGFyLW5vcm1hbC1mb250LXdlaWdodCldIHRleHQtWyMwMTAxMGFdIHRleHQtW2xlbmd0aDp2YXIoLS10ZXh0LXJlZ3VsYXItbm9ybWFsLWZvbnQtc2l6ZSldIHRyYWNraW5nLVt2YXIoLS10ZXh0LXJlZ3VsYXItbm9ybWFsLWxldHRlci1zcGFjaW5nKV0gbGVhZGluZy1bdmFyKC0tdGV4dC1yZWd1bGFyLW5vcm1hbC1saW5lLWhlaWdodCldIFtmb250LXN0eWxlOnZhcigtLXRleHQtcmVndWxhci1ub3JtYWwtZm9udC1zdHlsZSldXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBDb21wYW55XG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgIGlkPVwiY29tcGFueVwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuY29tcGFueX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlSW5wdXRDaGFuZ2UoXCJjb21wYW55XCIsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInNlbGYtc3RyZXRjaCBiZy1bIzAxMDEwYTBkXSByb3VuZGVkLXhsIGJvcmRlci10cmFuc3BhcmVudCBmb2N1czpib3JkZXItWyMxNzE3YzRdIGZvY3VzOnJpbmctWyMxNzE3YzRdXCJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgeW91ciBjb21wYW55IG5hbWVcIlxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzU3VibWl0dGluZ31cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtc3RhcnQgZ2FwLTIgc2VsZi1zdHJldGNoIHctZnVsbFwiPlxuICAgICAgICAgICAgICAgIDxsYWJlbFxuICAgICAgICAgICAgICAgICAgaHRtbEZvcj1cIm1lc3NhZ2VcIlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwic2VsZi1zdHJldGNoIGZvbnQtdGV4dC1yZWd1bGFyLW5vcm1hbCBmb250LVtudW1iZXI6dmFyKC0tdGV4dC1yZWd1bGFyLW5vcm1hbC1mb250LXdlaWdodCldIHRleHQtWyMwMTAxMGFdIHRleHQtW2xlbmd0aDp2YXIoLS10ZXh0LXJlZ3VsYXItbm9ybWFsLWZvbnQtc2l6ZSldIHRyYWNraW5nLVt2YXIoLS10ZXh0LXJlZ3VsYXItbm9ybWFsLWxldHRlci1zcGFjaW5nKV0gbGVhZGluZy1bdmFyKC0tdGV4dC1yZWd1bGFyLW5vcm1hbC1saW5lLWhlaWdodCldIFtmb250LXN0eWxlOnZhcigtLXRleHQtcmVndWxhci1ub3JtYWwtZm9udC1zdHlsZSldXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBQcm9qZWN0IERldGFpbHMgKlxuICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPFRleHRhcmVhXG4gICAgICAgICAgICAgICAgICBpZD1cIm1lc3NhZ2VcIlxuICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5tZXNzYWdlfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVJbnB1dENoYW5nZShcIm1lc3NhZ2VcIiwgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJUZWxsIHVzIGFib3V0IHlvdXIgc3ByaW5nIHJlcXVpcmVtZW50cywgc3BlY2lmaWNhdGlvbnMsIHF1YW50aXRpZXMsIGFuZCB0aW1lbGluZS4uLlwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTMyIGxnOmgtWzE4MnB4XSBzZWxmLXN0cmV0Y2ggYmctWyMwMTAxMGEwZF0gcm91bmRlZC14bCBib3JkZXItdHJhbnNwYXJlbnQgZm9udC10ZXh0LXJlZ3VsYXItbm9ybWFsIHRleHQtWyMwMTAxMGE5OV0gZm9jdXM6Ym9yZGVyLVsjMTcxN2M0XSBmb2N1czpyaW5nLVsjMTcxN2M0XSByZXNpemUtbm9uZVwiXG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNTdWJtaXR0aW5nfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBnYXAtMyBwYi0yIGxnOnBiLTRcIj5cbiAgICAgICAgICAgICAgICA8Q2hlY2tib3hcbiAgICAgICAgICAgICAgICAgIGlkPVwidGVybXNcIlxuICAgICAgICAgICAgICAgICAgY2hlY2tlZD17YWdyZWVUb1Rlcm1zfVxuICAgICAgICAgICAgICAgICAgb25DaGVja2VkQ2hhbmdlPXsoY2hlY2tlZCkgPT5cbiAgICAgICAgICAgICAgICAgICAgc2V0QWdyZWVUb1Rlcm1zKGNoZWNrZWQgYXMgYm9vbGVhbilcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctNSBoLTUgYmctWyMwMTAxMGEwZF0gcm91bmRlZCBib3JkZXItdHJhbnNwYXJlbnQgbXQtMC41XCJcbiAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc1N1Ym1pdHRpbmd9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8bGFiZWxcbiAgICAgICAgICAgICAgICAgIGh0bWxGb3I9XCJ0ZXJtc1wiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmb250LXRleHQtc21hbGwtbm9ybWFsIGZvbnQtW251bWJlcjp2YXIoLS10ZXh0LXNtYWxsLW5vcm1hbC1mb250LXdlaWdodCldIHRleHQtWyMwMTAxMGFdIHRleHQtW2xlbmd0aDp2YXIoLS10ZXh0LXNtYWxsLW5vcm1hbC1mb250LXNpemUpXSB0cmFja2luZy1bdmFyKC0tdGV4dC1zbWFsbC1ub3JtYWwtbGV0dGVyLXNwYWNpbmcpXSBsZWFkaW5nLVt2YXIoLS10ZXh0LXNtYWxsLW5vcm1hbC1saW5lLWhlaWdodCldIFtmb250LXN0eWxlOnZhcigtLXRleHQtc21hbGwtbm9ybWFsLWZvbnQtc3R5bGUpXSBmbGV4LTFcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIEkgYWdyZWUgdG8gdGhlIFRlcm1zIG9mIFNlcnZpY2UgYW5kIFByaXZhY3kgUG9saWN5LCBhbmRcbiAgICAgICAgICAgICAgICAgIGNvbnNlbnQgdG8gcmVjZWl2ZSBjb21tdW5pY2F0aW9ucyBhYm91dCBzcHJpbmcgbWFudWZhY3R1cmluZ1xuICAgICAgICAgICAgICAgICAgc2VydmljZXMuXG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXshYWdyZWVUb1Rlcm1zIHx8IGlzU3VibWl0dGluZ31cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgc206dy1hdXRvIHB4LTYgcHktMi41IGJnLVsjMTcxN2M0XSByb3VuZGVkLVsxMDBweF0gYm9yZGVyLWItNCBib3JkZXItWyMxMjEyOWNdIGZvbnQtdGV4dC1yZWd1bGFyLW1lZGl1bSBmb250LVtudW1iZXI6dmFyKC0tdGV4dC1yZWd1bGFyLW1lZGl1bS1mb250LXdlaWdodCldIHRleHQtd2hpdGUgdGV4dC1bbGVuZ3RoOnZhcigtLXRleHQtcmVndWxhci1tZWRpdW0tZm9udC1zaXplKV0gdHJhY2tpbmctW3ZhcigtLXRleHQtcmVndWxhci1tZWRpdW0tbGV0dGVyLXNwYWNpbmcpXSBsZWFkaW5nLVt2YXIoLS10ZXh0LXJlZ3VsYXItbWVkaXVtLWxpbmUtaGVpZ2h0KV0gW2ZvbnQtc3R5bGU6dmFyKC0tdGV4dC1yZWd1bGFyLW1lZGl1bS1mb250LXN0eWxlKV0gaG92ZXI6YmctWyMxNDE0YThdIHRyYW5zaXRpb24tY29sb3JzIGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtpc1N1Ym1pdHRpbmcgPyBcIlN1Ym1pdHRpbmcuLi5cIiA6IFwiU3VibWl0IFJlcXVlc3RcIn1cbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Zvcm0+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9zZWN0aW9uPlxuICApO1xufTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwiSW1hZ2UiLCJCdXR0b24iLCJDaGVja2JveCIsIklucHV0IiwiVGV4dGFyZWEiLCJzZW5kQ29udGFjdEVtYWlsIiwidG9hc3QiLCJnZXRDb250YWN0Rm9ybSIsIkNvbnRhY3RJbmZvU2VjdGlvbiIsImlzU3VibWl0dGluZyIsInNldElzU3VibWl0dGluZyIsImZvcm1Db250ZW50IiwiZm9ybURhdGEiLCJzZXRGb3JtRGF0YSIsIm5hbWUiLCJlbWFpbCIsImNvbXBhbnkiLCJtZXNzYWdlIiwiYWdyZWVUb1Rlcm1zIiwic2V0QWdyZWVUb1Rlcm1zIiwiaGFuZGxlU3VibWl0IiwiZSIsInByZXZlbnREZWZhdWx0IiwiZXJyb3IiLCJzdWNjZXNzIiwiY29uc29sZSIsImhhbmRsZUlucHV0Q2hhbmdlIiwiZmllbGQiLCJ2YWx1ZSIsInByZXYiLCJzZWN0aW9uIiwiY2xhc3NOYW1lIiwiZGl2IiwiYWx0Iiwic3JjIiwid2lkdGgiLCJoZWlnaHQiLCJzaXplcyIsInNwYW4iLCJoMiIsInAiLCJmb3JtIiwib25TdWJtaXQiLCJsYWJlbCIsImh0bWxGb3IiLCJpZCIsInJlcXVpcmVkIiwib25DaGFuZ2UiLCJ0YXJnZXQiLCJwbGFjZWhvbGRlciIsImRpc2FibGVkIiwidHlwZSIsImNoZWNrZWQiLCJvbkNoZWNrZWRDaGFuZ2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\n"));

/***/ })

});