"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contact/page",{

/***/ "(app-pages-browser)/./content.json":
/*!**********************!*\
  !*** ./content.json ***!
  \**********************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

module.exports = /*#__PURE__*/JSON.parse('{"company":{"name":"Spring Solutions","tagline":"Precision Springs Manufacturing","description":"Leading manufacturer of precision springs for industrial applications. Custom compression, extension, and torsion springs with expert engineering and quality assurance.","logo":"/logo.png","companyLogo":"/company-logo.svg"},"contactInfo":{"email":"<EMAIL>","phone":"+91 9821034962","address":"8F, New Empire I.E. (A), Kondivita Lane,J.B.Nagar, Andheri (E), Mumbai - 400059","businessHours":{"weekdays":"Monday - Friday: 8:00 AM - 6:00 PM","saturday":"Saturday: 9:00 AM - 2:00 PM","sunday":"Sunday: Closed"},"parking":"Free parking available on-site for all visitors and customers.","facilityTours":"Schedule a tour to see our manufacturing process and meet our team."},"navigation":[{"name":"Home","href":"/"},{"name":"Products","href":"/products"},{"name":"About Us","href":"/about"},{"name":"Contact","href":"/contact"}],"socialMedia":[{"platform":"Facebook","href":"https://facebook.com"},{"platform":"Instagram","href":"https://instagram.com"},{"platform":"Twitter","href":"https://twitter.com"},{"platform":"LinkedIn","href":"https://linkedin.com"},{"platform":"YouTube","href":"https://youtube.com"}],"home":{"hero":{"tagline":"","title":"Precision Springs Crafted for Every Application","description":"Discover our expertly engineered springs designed to meet the highest industry standards. With a commitment to quality and innovation, we serve diverse sectors with precision and reliability.","buttons":[{"text":"Learn More","href":"/products","type":"primary"},{"text":"Get Quote","action":"quote-modal","type":"secondary"}],"images":[{"src":"https://images.pexels.com/photos/257736/pexels-photo-257736.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2","alt":"Precision compression springs and mechanical gears in industrial manufacturing setting"},{"src":"https://images.pexels.com/photos/159298/gears-cogs-machine-machinery-159298.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2","alt":"Industrial machinery gears and precision engineering components with helical motors"},{"src":"https://images.pexels.com/photos/1108572/pexels-photo-1108572.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2","alt":"Modern manufacturing facility with advanced motor systems and spring production equipment"}]},"features":[{"title":"Advanced Manufacturing Processes","description":"Our state-of-the-art manufacturing processes ensure precision and durability in every spring we produce.","icon":"/relume.svg"},{"title":"Global Service Network","description":"We proudly serve clients across multiple regions, ensuring timely delivery and comprehensive support worldwide.","icon":"/relume.svg"},{"title":"Complete Spring Solutions","description":"From compression to extension springs, we have the perfect solution for your specific application requirements.","icon":"/relume.svg"}],"mainContent":{"features":[{"icon":"/productivity.svg","alt":"Productivity enhancement icon","text":"Boost productivity and streamline your manufacturing workflow with our precision-engineered springs."},{"icon":"/support.svg","alt":"Expert support icon","text":"Access expert technical support and engineering consultation whenever you need assistance."},{"icon":"/integration-instructions.svg","alt":"System integration icon","text":"Enjoy seamless integration with your existing manufacturing systems and processes."}]},"services":{"tagline":"Our Services","title":"Comprehensive Manufacturing Services Tailored for You","description":"Our manufacturing services are designed to meet diverse industry needs. We specialize in producing high-quality springs with precision and efficiency, backed by decades of engineering expertise.","features":[{"icon":"/manufacturing.svg","alt":"State-of-the-art manufacturing facilities","title":"State-of-the-Art Production Facilities","description":"Our facilities are equipped with the latest technology and precision machinery for superior spring manufacturing."},{"icon":"/high-quality.svg","alt":"Expert quality control team","title":"Expert Team Committed to Excellence","description":"Our skilled engineering team ensures top-notch quality control and precision in every spring we produce."},{"icon":"/inbox-customize.svg","alt":"Custom spring solutions","title":"Custom Solutions for Every Requirement","description":"We provide tailored spring solutions designed to fit your specific application needs and performance requirements."}]},"testimonial":{"stars":"/stars.svg","quote":"The quality of springs we received exceeded our expectations, enhancing our production efficiency significantly. Their technical expertise and customer service are outstanding!","customer":{"avatar":"https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=200","name":"John Doe","position":"Manufacturing Manager, Acme Corp"},"companyLogo":"/placeholder-logo.svg"}},"products":{"hero":{"tagline":"Our Products","title":"Comprehensive Spring Solutions for Every Industry","description":"From compression and extension springs to custom wire forms, our extensive product range is engineered to meet the most demanding specifications across automotive, aerospace, industrial, and consumer applications.","buttons":[{"text":"Request Quote","action":"quote-modal","type":"primary"},{"text":"Download Catalog","href":"/catalog","type":"secondary"}],"image":{"src":"https://images.pexels.com/photos/257736/pexels-photo-257736.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2","alt":"Comprehensive display of various spring types with motors, gears, and helical components"}},"catalog":[{"id":1,"slug":"compression-springs","name":"Compression Springs","category":"Standard Springs","price":"$45","variant":"Heavy Duty","description":"High-quality compression springs designed for various load requirements and applications.","longDescription":"Our compression springs are engineered to provide reliable performance under compressive loads. Manufactured using premium materials and precision winding techniques, these springs offer consistent force characteristics and long service life. Available in a wide range of sizes, materials, and load specifications to meet your exact requirements.","features":["Custom wire diameters from 0.1mm to 25mm","Various materials available (steel, stainless, music wire)","Precision wound with tight tolerances","Load tested for consistent performance","Custom coatings and finishes available"],"applications":["Automotive suspension systems","Industrial machinery components","Consumer electronics devices","Medical equipment applications","Aerospace components"],"image":"https://images.pexels.com/photos/257736/pexels-photo-257736.jpeg?auto=compress&cs=tinysrgb&w=800"},{"id":2,"slug":"extension-springs","name":"Extension Springs","category":"Standard Springs","price":"$35","variant":"Standard","description":"Reliable extension springs for tension applications across multiple industries.","longDescription":"Extension springs are designed to absorb and store energy while resisting a pulling force. Our extension springs feature precision-formed hooks and loops for secure attachment, with consistent spring rates and excellent fatigue resistance.","features":["Various hook and loop configurations","Consistent spring rates","Excellent fatigue resistance","Custom lengths available","Multiple material options"],"applications":["Garage door systems","Agricultural equipment","Exercise equipment","Industrial machinery","Automotive applications"],"image":"https://images.pexels.com/photos/159298/gears-cogs-machine-machinery-159298.jpeg?auto=compress&cs=tinysrgb&w=800"},{"id":3,"slug":"torsion-springs","name":"Torsion Springs","category":"Specialty Springs","price":"$65","variant":"Custom","description":"Precision torsion springs for rotational force applications.","longDescription":"Torsion springs work by twisting around an axis to provide rotational force. Our torsion springs are manufactured with precise angular deflection characteristics and consistent torque output for reliable performance in demanding applications.","features":["Precise angular deflection","Consistent torque output","Custom leg configurations","Various mounting options","High-strength materials"],"applications":["Clothespins and clips","Garage door counterbalance","Hinges and latches","Automotive components","Industrial equipment"],"image":"https://images.pexels.com/photos/1108572/pexels-photo-1108572.jpeg?auto=compress&cs=tinysrgb&w=800"}],"specifications":{"tagline":"Specifications","title":"Technical Specifications & Standards","description":"Our springs are manufactured to the highest industry standards with precise specifications and quality control measures.","categories":[{"category":"Materials","items":["High Carbon Steel","Stainless Steel (304, 316)","Music Wire","Chrome Silicon","Inconel","Beryllium Copper"]},{"category":"Wire Diameters","items":["0.1mm - 25mm","Custom sizes available","Precision tolerance ±0.05mm","Various cross-sections","Round, square, rectangular","Special profiles on request"]},{"category":"Finishes","items":["Zinc Plating","Powder Coating","Passivation","Shot Peening","Stress Relieving","Custom Coatings"]},{"category":"Quality Standards","items":["ISO 9001:2015 Certified","ASTM Compliance","DIN Standards","100% Load Testing","Dimensional Inspection","Material Certification"]}]},"cta":{"title":"Need a Custom Spring Solution?","description":"Our engineering team specializes in creating custom spring solutions tailored to your exact specifications. From prototype to production, we\'ll work with you to develop the perfect spring for your application.","features":["Free engineering consultation","Rapid prototyping available","Competitive pricing for all volumes"],"buttons":[{"text":"Start Custom Project","action":"quote-modal","type":"primary"},{"text":"Download Catalog","href":"/catalog","type":"secondary"}],"quickQuote":{"title":"Quick Quote Request","description":"Get a quote in 24 hours or less","contact":{"email":"📧 <EMAIL>","phone":"📞 +****************"},"button":{"text":"Get Quote Now","action":"quote-modal","type":"primary"}}}},"about":{"hero":{"tagline":"About Us","title":"Precision Manufacturing Excellence Since 1998","description":"With over 25 years of expertise in spring manufacturing, we combine traditional craftsmanship with cutting-edge technology to deliver superior products that exceed industry standards.","buttons":[{"text":"Our Story","href":"#story","type":"primary"},{"text":"Meet Our Team","href":"#team","type":"secondary"}],"image":{"src":"https://images.pexels.com/photos/1108572/pexels-photo-1108572.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2","alt":"Spring Solutions manufacturing facility with advanced motor equipment, gears, and skilled technicians"}},"story":{"tagline":"Our Story","title":"Building Excellence Through Innovation","content":["Founded with a vision to revolutionize spring manufacturing, Spring Solutions began as a small workshop with big dreams. Our founders recognized the need for precision-engineered springs that could meet the demanding requirements of modern industry.","Over the decades, we have grown from a local manufacturer to a trusted partner for companies worldwide. Our commitment to quality, innovation, and customer satisfaction has remained unwavering throughout our journey.","Today, we continue to push the boundaries of what\'s possible in spring manufacturing, investing in the latest technology while maintaining the craftsmanship that has defined us from the beginning."],"image":{"src":"https://images.pexels.com/photos/257736/pexels-photo-257736.jpeg?auto=compress&cs=tinysrgb&w=800","alt":"Historical view of Spring Solutions manufacturing process"}},"values":[{"icon":"/high-quality.svg","title":"Quality Excellence","description":"We maintain the highest standards in every spring we manufacture, ensuring consistent quality and reliability."},{"icon":"/productivity.svg","title":"Innovation Drive","description":"Continuously investing in new technologies and processes to stay at the forefront of spring manufacturing."},{"icon":"/support.svg","title":"Customer Focus","description":"Building lasting partnerships through exceptional service, technical support, and customized solutions."},{"icon":"/manufacturing.svg","title":"Precision Engineering","description":"Leveraging decades of expertise to deliver springs that meet exact specifications and performance requirements."}],"stats":[{"number":"25+","label":"Years of Experience","description":"Decades of expertise in precision spring manufacturing"},{"number":"10M+","label":"Springs Manufactured","description":"Millions of high-quality springs delivered worldwide"},{"number":"500+","label":"Satisfied Clients","description":"Companies trust us for their spring manufacturing needs"},{"number":"99.8%","label":"Quality Rate","description":"Exceptional quality control and manufacturing precision"}],"team":[{"name":"Sarah Johnson","position":"Chief Executive Officer","image":"https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=400","description":"Leading Spring Solutions with over 20 years of manufacturing industry experience."},{"name":"Michael Chen","position":"Head of Engineering","image":"https://images.pexels.com/photos/2182970/pexels-photo-2182970.jpeg?auto=compress&cs=tinysrgb&w=400","description":"Expert in spring design and manufacturing processes with advanced engineering background."},{"name":"Emily Rodriguez","position":"Quality Assurance Manager","image":"https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=400","description":"Ensuring every spring meets our rigorous quality standards through comprehensive testing protocols."},{"name":"David Thompson","position":"Production Manager","image":"https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=400","description":"Overseeing manufacturing operations to ensure efficient production and timely delivery."}]},"contact":{"hero":{"tagline":"Contact Us","title":"Let\'s Discuss Your Spring Manufacturing Needs","description":"Ready to get started on your next project? Our expert team is here to provide technical consultation, custom quotes, and comprehensive support for all your spring manufacturing requirements.","buttons":[{"text":"Get Quote","action":"quote-modal","type":"primary"},{"text":"Call Us Now","href":"tel:+************","type":"secondary"}]},"methods":[{"title":"Email","description":"Reach out to us for any questions, quotes, or technical support.","contact":"<EMAIL>","href":"mailto:<EMAIL>"},{"title":"Phone","description":"Call us for immediate assistance and technical consultation.","contact":"+91 9821034962","href":"tel:+************"},{"title":"Office","description":"Visit us at our headquarters for facility tours and meetings.","contact":"8F, New Empire I.E. (A), Kondivita Lane,J.B.Nagar, Andheri (E), Mumbai - 400059, Maharashtra, India","href":"#"}],"location":{"tagline":"Visit Us","title":"Our Location","description":"Visit our state-of-the-art manufacturing facility to see our spring production process firsthand and meet our expert team.","address":"8F, New Empire I.E. (A), Kondivita Lane,J.B.Nagar, Andheri (E), Mumbai - 400059, Maharashtra, India","mapPlaceholder":"Interactive Map"}},"footer":{"companyLinks":[{"name":"About Us","href":"/about"},{"name":"Contact Us","href":"/contact"},{"name":"Our Services","href":"/services"},{"name":"Careers","href":"/careers"},{"name":"Blog","href":"/blog"}],"supportLinks":[{"name":"FAQs","href":"/faq"},{"name":"Support","href":"/support"},{"name":"Testimonials","href":"/testimonials"},{"name":"Privacy Policy","href":"/privacy"},{"name":"Terms of Use","href":"/terms"}],"bottomLinks":[{"name":"Privacy Policy","href":"/privacy"},{"name":"Terms of Service","href":"/terms"},{"name":"Cookie Settings","href":"/cookies"}]},"metadata":{"keywords":["spring manufacturing","precision springs","compression springs","extension springs","torsion springs","industrial springs","custom springs","wire forms","spring engineering"],"pages":{"home":{"title":"Home - Precision Springs Manufacturing","description":"Discover our expertly engineered springs designed to meet the highest industry standards. With a commitment to quality and innovation, we serve diverse sectors with precision and reliability.","ogTitle":"Spring Solutions - Precision Springs Manufacturing","ogDescription":"Discover our expertly engineered springs designed to meet the highest industry standards."},"products":{"title":"Products - Spring Solutions","description":"Explore our comprehensive range of precision springs including compression, extension, torsion springs, wire forms, and custom solutions for industrial applications.","ogTitle":"Spring Products - Compression, Extension & Torsion Springs","ogDescription":"Explore our comprehensive range of precision springs for industrial applications."},"about":{"title":"About Us - Spring Solutions","description":"Learn about Spring Solutions\' commitment to precision manufacturing, our experienced team, and decades of expertise in spring engineering and production.","ogTitle":"About Spring Solutions - Precision Manufacturing Experts","ogDescription":"Learn about our commitment to precision manufacturing and decades of expertise in spring engineering."},"contact":{"title":"Contact Us - Spring Solutions","description":"Get in touch with Spring Solutions for custom quotes, technical support, and spring manufacturing inquiries. Contact our expert team today.","ogTitle":"Contact Spring Solutions - Get Your Custom Quote","ogDescription":"Get in touch for custom quotes, technical support, and spring manufacturing inquiries."}}}}');

/***/ }),

/***/ "(app-pages-browser)/./src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx":
/*!***************************************************************************************************!*\
  !*** ./src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx ***!
  \***************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavigationBarSection: () => (/* binding */ NavigationBarSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_MenuIcon_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=MenuIcon,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_MenuIcon_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=MenuIcon,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/navigation-menu */ \"(app-pages-browser)/./src/components/ui/navigation-menu.tsx\");\n/* harmony import */ var _components_QuoteModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/QuoteModal */ \"(app-pages-browser)/./src/components/QuoteModal.tsx\");\n/* harmony import */ var _lib_content__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/content */ \"(app-pages-browser)/./src/lib/content.ts\");\n/* __next_internal_client_entry_do_not_use__ NavigationBarSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst NavigationBarSection = ()=>{\n    _s();\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // Get navigation items and company info from centralized content\n    const navigationItems = (0,_lib_content__WEBPACK_IMPORTED_MODULE_8__.getNavigation)();\n    const companyInfo = (0,_lib_content__WEBPACK_IMPORTED_MODULE_8__.getCompanyInfo)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"flex flex-col items-center w-full bg-white border-b border-[#01010a26] sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-16 lg:h-[72px] items-center justify-between container-responsive w-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4 lg:gap-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            className: \"w-16 h-auto aspect-auto lg:w-[220px] lg:h-auto\",\n                            alt: \"Spring Solutions Company Logo\",\n                            src: \"/logo.png\",\n                            width: 84,\n                            height: 36,\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_6__.NavigationMenu, {\n                    className: \"hidden lg:flex absolute left-1/2 transform -translate-x-1/2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_6__.NavigationMenuList, {\n                        className: \"flex items-center gap-6 lg:gap-8\",\n                        children: navigationItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_6__.NavigationMenuItem, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        className: \"font-text-regular-normal text-[#01010a] hover:bg-[#01010a0d] transition-colors\",\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 lg:gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden sm:flex items-center gap-2 lg:gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/contact\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        className: \"px-3 lg:px-5 py-2 bg-[#01010a0d] rounded-[100px] border-b-4 border-[#01010a26] hover:bg-[#01010a1a] text-sm lg:text-base transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-text-regular-medium text-[#01010a] text-[length:var(--text-regular-medium-font-size)] tracking-[var(--text-regular-medium-letter-spacing)] leading-[var(--text-regular-medium-line-height)]\",\n                                            children: \"Contact\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QuoteModal__WEBPACK_IMPORTED_MODULE_7__.QuoteModal, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        className: \"px-3 lg:px-5 py-2 bg-[#1717c4] rounded-[100px] border-b-4 border-[#12129c] hover:bg-[#1414a8] text-sm lg:text-base transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-text-regular-medium text-white text-[length:var(--text-regular-medium-font-size)] tracking-[var(--text-regular-medium-letter-spacing)] leading-[var(--text-regular-medium-line-height)]\",\n                                            children: \"Get Quote\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.Sheet, {\n                            open: isMobileMenuOpen,\n                            onOpenChange: setIsMobileMenuOpen,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetTrigger, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"lg:hidden w-10 h-10 p-0 flex items-center justify-center rounded-full hover:bg-[#01010a0d] transition-all duration-300\",\n                                        \"aria-label\": \"Open mobile menu\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative w-6 h-6 flex items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuIcon_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-6 h-6 transition-all duration-300 \".concat(isMobileMenuOpen ? \"opacity-0 rotate-180 scale-0\" : \"opacity-100 rotate-0 scale-100\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuIcon_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-6 h-6 absolute transition-all duration-300 \".concat(isMobileMenuOpen ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 rotate-180 scale-0\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetContent, {\n                                    side: \"right\",\n                                    className: \"w-full h-full max-w-none rounded-none border-none shadow-none bg-white fixed inset-0 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetHeader, {\n                                            className: \"sr-only\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetTitle, {\n                                                children: \"Navigation Menu\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex h-16 items-center justify-between container-responsive w-full border-b border-[#01010a26]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/\",\n                                                className: \"flex-shrink-0\",\n                                                onClick: ()=>setIsMobileMenuOpen(false),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    className: \"w-16 h-7\",\n                                                    alt: \"Spring Solutions Company Logo\",\n                                                    src: \"/company-logo.svg\",\n                                                    width: 84,\n                                                    height: 36,\n                                                    priority: true\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col h-[calc(100vh-4rem)] justify-center items-center container-responsive py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center space-y-8 flex-1 justify-center\",\n                                                    children: navigationItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: item.href,\n                                                            className: \"text-center py-4 px-8 text-[#01010a] font-text-large-semi-bold hover:bg-[#01010a0d] rounded-xl transition-all duration-200 transform hover:scale-[1.05] active:scale-[0.95]\",\n                                                            onClick: ()=>setIsMobileMenuOpen(false),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-2xl\",\n                                                                children: item.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                                lineNumber: 154,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, index, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                            lineNumber: 148,\n                                                            columnNumber: 21\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center gap-4 w-full max-w-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/contact\",\n                                                            className: \"w-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"w-full px-6 py-4 bg-[#01010a0d] rounded-2xl border-2 border-[#01010a26] hover:bg-[#01010a1a] transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]\",\n                                                                onClick: ()=>setIsMobileMenuOpen(false),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-text-regular-medium text-[#01010a] text-lg\",\n                                                                    children: \"Contact Us\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                                    lineNumber: 167,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                                lineNumber: 162,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QuoteModal__WEBPACK_IMPORTED_MODULE_7__.QuoteModal, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                className: \"w-full px-6 py-4 bg-[#1717c4] rounded-2xl border-2 border-[#12129c] hover:bg-[#1414a8] transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]\",\n                                                                onClick: ()=>setIsMobileMenuOpen(false),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-text-regular-medium text-white text-lg\",\n                                                                    children: \"Get Quote\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                                    lineNumber: 178,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                                lineNumber: 174,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, undefined);\n};\n_s(NavigationBarSection, \"QerECOS75+B7gv+k3q7FrDf39mc=\");\n_c = NavigationBarSection;\nvar _c;\n$RefreshReg$(_c, \"NavigationBarSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/content.ts":
/*!****************************!*\
  !*** ./src/lib/content.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addProduct: () => (/* binding */ addProduct),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getAboutHero: () => (/* binding */ getAboutHero),\n/* harmony export */   getAboutStats: () => (/* binding */ getAboutStats),\n/* harmony export */   getAboutStory: () => (/* binding */ getAboutStory),\n/* harmony export */   getAboutTeam: () => (/* binding */ getAboutTeam),\n/* harmony export */   getAboutValues: () => (/* binding */ getAboutValues),\n/* harmony export */   getCompanyInfo: () => (/* binding */ getCompanyInfo),\n/* harmony export */   getContactHero: () => (/* binding */ getContactHero),\n/* harmony export */   getContactInfo: () => (/* binding */ getContactInfo),\n/* harmony export */   getContactLocation: () => (/* binding */ getContactLocation),\n/* harmony export */   getContactMethods: () => (/* binding */ getContactMethods),\n/* harmony export */   getFooterLinks: () => (/* binding */ getFooterLinks),\n/* harmony export */   getHomeFeatures: () => (/* binding */ getHomeFeatures),\n/* harmony export */   getHomeHero: () => (/* binding */ getHomeHero),\n/* harmony export */   getHomeServices: () => (/* binding */ getHomeServices),\n/* harmony export */   getHomeTestimonial: () => (/* binding */ getHomeTestimonial),\n/* harmony export */   getMetadata: () => (/* binding */ getMetadata),\n/* harmony export */   getNavigation: () => (/* binding */ getNavigation),\n/* harmony export */   getPageMetadata: () => (/* binding */ getPageMetadata),\n/* harmony export */   getProductBySlug: () => (/* binding */ getProductBySlug),\n/* harmony export */   getProductsCTA: () => (/* binding */ getProductsCTA),\n/* harmony export */   getProductsCatalog: () => (/* binding */ getProductsCatalog),\n/* harmony export */   getProductsHero: () => (/* binding */ getProductsHero),\n/* harmony export */   getProductsSpecs: () => (/* binding */ getProductsSpecs),\n/* harmony export */   getSocialMedia: () => (/* binding */ getSocialMedia)\n/* harmony export */ });\n/* harmony import */ var _content_json__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../content.json */ \"(app-pages-browser)/./content.json\");\n\n// Content access functions\nconst getCompanyInfo = ()=>_content_json__WEBPACK_IMPORTED_MODULE_0__.company;\nconst getContactInfo = ()=>_content_json__WEBPACK_IMPORTED_MODULE_0__.contactInfo;\nconst getNavigation = ()=>_content_json__WEBPACK_IMPORTED_MODULE_0__.navigation;\nconst getSocialMedia = ()=>_content_json__WEBPACK_IMPORTED_MODULE_0__.socialMedia;\n// Home page content\nconst getHomeHero = ()=>_content_json__WEBPACK_IMPORTED_MODULE_0__.home.hero;\nconst getHomeFeatures = ()=>_content_json__WEBPACK_IMPORTED_MODULE_0__.home.features;\nconst getHomeServices = ()=>_content_json__WEBPACK_IMPORTED_MODULE_0__.home.services;\nconst getHomeTestimonial = ()=>_content_json__WEBPACK_IMPORTED_MODULE_0__.home.testimonial;\n// Products page content\nconst getProductsHero = ()=>_content_json__WEBPACK_IMPORTED_MODULE_0__.products.hero;\nconst getProductsCatalog = ()=>_content_json__WEBPACK_IMPORTED_MODULE_0__.products.catalog;\nconst getProductsSpecs = ()=>_content_json__WEBPACK_IMPORTED_MODULE_0__.products.specifications;\nconst getProductsCTA = ()=>_content_json__WEBPACK_IMPORTED_MODULE_0__.products.cta;\n// About page content\nconst getAboutHero = ()=>_content_json__WEBPACK_IMPORTED_MODULE_0__.about.hero;\nconst getAboutStory = ()=>_content_json__WEBPACK_IMPORTED_MODULE_0__.about.story;\nconst getAboutValues = ()=>_content_json__WEBPACK_IMPORTED_MODULE_0__.about.values;\nconst getAboutStats = ()=>_content_json__WEBPACK_IMPORTED_MODULE_0__.about.stats;\nconst getAboutTeam = ()=>_content_json__WEBPACK_IMPORTED_MODULE_0__.about.team;\n// Contact page content\nconst getContactHero = ()=>_content_json__WEBPACK_IMPORTED_MODULE_0__.contact.hero;\nconst getContactMethods = ()=>_content_json__WEBPACK_IMPORTED_MODULE_0__.contact.methods;\nconst getContactLocation = ()=>_content_json__WEBPACK_IMPORTED_MODULE_0__.contact.location;\n// Footer content\nconst getFooterLinks = ()=>_content_json__WEBPACK_IMPORTED_MODULE_0__.footer;\n// Metadata\nconst getMetadata = ()=>_content_json__WEBPACK_IMPORTED_MODULE_0__.metadata;\nconst getPageMetadata = (page)=>_content_json__WEBPACK_IMPORTED_MODULE_0__.metadata.pages[page];\n// Helper function to get product by slug\nconst getProductBySlug = (slug)=>_content_json__WEBPACK_IMPORTED_MODULE_0__.products.catalog.find((product)=>product.slug === slug);\n// Helper function to add new product\nconst addProduct = (product)=>{\n    const newId = Math.max(..._content_json__WEBPACK_IMPORTED_MODULE_0__.products.catalog.map((p)=>p.id)) + 1;\n    const newProduct = {\n        ...product,\n        id: newId\n    };\n    _content_json__WEBPACK_IMPORTED_MODULE_0__.products.catalog.push(newProduct);\n    return newProduct;\n};\n// Export the entire content data for direct access if needed\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_content_json__WEBPACK_IMPORTED_MODULE_0__);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvY29udGVudC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBNkM7QUF1RjdDLDJCQUEyQjtBQUNwQixNQUFNQyxpQkFBaUIsSUFBZUQsa0RBQW1CLENBQUM7QUFFMUQsTUFBTUcsaUJBQWlCLElBQW1CSCxzREFBdUIsQ0FBQztBQUVsRSxNQUFNSyxnQkFBZ0IsSUFBd0JMLHFEQUFzQixDQUFDO0FBRXJFLE1BQU1PLGlCQUFpQixJQUFxQlAsc0RBQXVCLENBQUM7QUFFM0Usb0JBQW9CO0FBQ2IsTUFBTVMsY0FBYyxJQUFNVCwrQ0FBZ0IsQ0FBQ1csSUFBSSxDQUFDO0FBRWhELE1BQU1DLGtCQUFrQixJQUFpQlosK0NBQWdCLENBQUNhLFFBQVEsQ0FBQztBQUVuRSxNQUFNQyxrQkFBa0IsSUFBTWQsK0NBQWdCLENBQUNlLFFBQVEsQ0FBQztBQUV4RCxNQUFNQyxxQkFBcUIsSUFDaENoQiwrQ0FBZ0IsQ0FBQ2lCLFdBQVcsQ0FBQztBQUUvQix3QkFBd0I7QUFDakIsTUFBTUMsa0JBQWtCLElBQU1sQixtREFBb0IsQ0FBQ1csSUFBSSxDQUFDO0FBRXhELE1BQU1TLHFCQUFxQixJQUFpQnBCLG1EQUFvQixDQUFDcUIsT0FBTyxDQUFDO0FBRXpFLE1BQU1DLG1CQUFtQixJQUFNdEIsbURBQW9CLENBQUN1QixjQUFjLENBQUM7QUFFbkUsTUFBTUMsaUJBQWlCLElBQU14QixtREFBb0IsQ0FBQ3lCLEdBQUcsQ0FBQztBQUU3RCxxQkFBcUI7QUFDZCxNQUFNQyxlQUFlLElBQU0xQixnREFBaUIsQ0FBQ1csSUFBSSxDQUFDO0FBRWxELE1BQU1pQixnQkFBZ0IsSUFBTTVCLGdEQUFpQixDQUFDNkIsS0FBSyxDQUFDO0FBRXBELE1BQU1DLGlCQUFpQixJQUFpQjlCLGdEQUFpQixDQUFDK0IsTUFBTSxDQUFDO0FBRWpFLE1BQU1DLGdCQUFnQixJQUFjaEMsZ0RBQWlCLENBQUNpQyxLQUFLLENBQUM7QUFFNUQsTUFBTUMsZUFBZSxJQUFvQmxDLGdEQUFpQixDQUFDbUMsSUFBSSxDQUFDO0FBRXZFLHVCQUF1QjtBQUNoQixNQUFNQyxpQkFBaUIsSUFBTXBDLGtEQUFtQixDQUFDVyxJQUFJLENBQUM7QUFFdEQsTUFBTTJCLG9CQUFvQixJQUFNdEMsa0RBQW1CLENBQUN1QyxPQUFPLENBQUM7QUFFNUQsTUFBTUMscUJBQXFCLElBQU14QyxrREFBbUIsQ0FBQ3lDLFFBQVEsQ0FBQztBQUVyRSxpQkFBaUI7QUFDVixNQUFNQyxpQkFBaUIsSUFBTTFDLGlEQUFrQixDQUFDO0FBRXZELFdBQVc7QUFDSixNQUFNNEMsY0FBYyxJQUFNNUMsbURBQW9CLENBQUM7QUFFL0MsTUFBTThDLGtCQUFrQixDQUM3QkMsT0FDRy9DLG1EQUFvQixDQUFDZ0QsS0FBSyxDQUFDRCxLQUFLLENBQUM7QUFFdEMseUNBQXlDO0FBQ2xDLE1BQU1FLG1CQUFtQixDQUFDQyxPQUMvQmxELG1EQUFvQixDQUFDcUIsT0FBTyxDQUFDOEIsSUFBSSxDQUFDLENBQUNDLFVBQVlBLFFBQVFGLElBQUksS0FBS0EsTUFBTTtBQUV4RSxxQ0FBcUM7QUFDOUIsTUFBTUcsYUFBYSxDQUFDRDtJQUN6QixNQUFNRSxRQUFRQyxLQUFLQyxHQUFHLElBQUl4RCxtREFBb0IsQ0FBQ3FCLE9BQU8sQ0FBQ29DLEdBQUcsQ0FBQyxDQUFDQyxJQUFNQSxFQUFFQyxFQUFFLEtBQUs7SUFDM0UsTUFBTUMsYUFBYTtRQUFFLEdBQUdSLE9BQU87UUFBRU8sSUFBSUw7SUFBTTtJQUMzQ3RELG1EQUFvQixDQUFDcUIsT0FBTyxDQUFDd0MsSUFBSSxDQUFDRDtJQUNsQyxPQUFPQTtBQUNULEVBQUU7QUFFRiw2REFBNkQ7QUFDN0QsaUVBQWU1RCwwQ0FBV0EsRUFBQyIsInNvdXJjZXMiOlsiL1VzZXJzL25pc2hhcGFuY2hhbC9Eb2N1bWVudHMvR2F1cmF2L1Byb2plY3RzL3NoYWt0aS9zcmMvbGliL2NvbnRlbnQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNvbnRlbnREYXRhIGZyb20gXCIuLi8uLi9jb250ZW50Lmpzb25cIjtcblxuLy8gVHlwZSBkZWZpbml0aW9ucyBmb3IgYmV0dGVyIFR5cGVTY3JpcHQgc3VwcG9ydFxuZXhwb3J0IGludGVyZmFjZSBDb21wYW55IHtcbiAgbmFtZTogc3RyaW5nO1xuICB0YWdsaW5lOiBzdHJpbmc7XG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XG4gIGxvZ286IHN0cmluZztcbiAgY29tcGFueUxvZ286IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBDb250YWN0SW5mbyB7XG4gIGVtYWlsOiBzdHJpbmc7XG4gIHBob25lOiBzdHJpbmc7XG4gIGFkZHJlc3M6IHN0cmluZztcbiAgYnVzaW5lc3NIb3Vyczoge1xuICAgIHdlZWtkYXlzOiBzdHJpbmc7XG4gICAgc2F0dXJkYXk6IHN0cmluZztcbiAgICBzdW5kYXk6IHN0cmluZztcbiAgfTtcbiAgcGFya2luZzogc3RyaW5nO1xuICBmYWNpbGl0eVRvdXJzOiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgTmF2aWdhdGlvbkl0ZW0ge1xuICBuYW1lOiBzdHJpbmc7XG4gIGhyZWY6IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBTb2NpYWxNZWRpYSB7XG4gIHBsYXRmb3JtOiBzdHJpbmc7XG4gIGhyZWY6IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBCdXR0b24ge1xuICB0ZXh0OiBzdHJpbmc7XG4gIGhyZWY/OiBzdHJpbmc7XG4gIGFjdGlvbj86IHN0cmluZztcbiAgdHlwZTogXCJwcmltYXJ5XCIgfCBcInNlY29uZGFyeVwiO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFByb2R1Y3Qge1xuICBpZDogbnVtYmVyO1xuICBzbHVnOiBzdHJpbmc7XG4gIG5hbWU6IHN0cmluZztcbiAgY2F0ZWdvcnk6IHN0cmluZztcbiAgcHJpY2U6IHN0cmluZztcbiAgdmFyaWFudDogc3RyaW5nO1xuICBkZXNjcmlwdGlvbjogc3RyaW5nO1xuICBsb25nRGVzY3JpcHRpb246IHN0cmluZztcbiAgZmVhdHVyZXM6IHN0cmluZ1tdO1xuICBhcHBsaWNhdGlvbnM6IHN0cmluZ1tdO1xuICBpbWFnZTogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFRlYW1NZW1iZXIge1xuICBuYW1lOiBzdHJpbmc7XG4gIHBvc2l0aW9uOiBzdHJpbmc7XG4gIGltYWdlOiBzdHJpbmc7XG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgRmVhdHVyZSB7XG4gIHRpdGxlOiBzdHJpbmc7XG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XG4gIGljb246IHN0cmluZztcbiAgYWx0Pzogc3RyaW5nO1xuICB0ZXh0Pzogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFRlc3RpbW9uaWFsIHtcbiAgc3RhcnM6IHN0cmluZztcbiAgcXVvdGU6IHN0cmluZztcbiAgY3VzdG9tZXI6IHtcbiAgICBhdmF0YXI6IHN0cmluZztcbiAgICBuYW1lOiBzdHJpbmc7XG4gICAgcG9zaXRpb246IHN0cmluZztcbiAgfTtcbiAgY29tcGFueUxvZ286IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBTdGF0IHtcbiAgbnVtYmVyOiBzdHJpbmc7XG4gIGxhYmVsOiBzdHJpbmc7XG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XG59XG5cbi8vIENvbnRlbnQgYWNjZXNzIGZ1bmN0aW9uc1xuZXhwb3J0IGNvbnN0IGdldENvbXBhbnlJbmZvID0gKCk6IENvbXBhbnkgPT4gY29udGVudERhdGEuY29tcGFueTtcblxuZXhwb3J0IGNvbnN0IGdldENvbnRhY3RJbmZvID0gKCk6IENvbnRhY3RJbmZvID0+IGNvbnRlbnREYXRhLmNvbnRhY3RJbmZvO1xuXG5leHBvcnQgY29uc3QgZ2V0TmF2aWdhdGlvbiA9ICgpOiBOYXZpZ2F0aW9uSXRlbVtdID0+IGNvbnRlbnREYXRhLm5hdmlnYXRpb247XG5cbmV4cG9ydCBjb25zdCBnZXRTb2NpYWxNZWRpYSA9ICgpOiBTb2NpYWxNZWRpYVtdID0+IGNvbnRlbnREYXRhLnNvY2lhbE1lZGlhO1xuXG4vLyBIb21lIHBhZ2UgY29udGVudFxuZXhwb3J0IGNvbnN0IGdldEhvbWVIZXJvID0gKCkgPT4gY29udGVudERhdGEuaG9tZS5oZXJvO1xuXG5leHBvcnQgY29uc3QgZ2V0SG9tZUZlYXR1cmVzID0gKCk6IEZlYXR1cmVbXSA9PiBjb250ZW50RGF0YS5ob21lLmZlYXR1cmVzO1xuXG5leHBvcnQgY29uc3QgZ2V0SG9tZVNlcnZpY2VzID0gKCkgPT4gY29udGVudERhdGEuaG9tZS5zZXJ2aWNlcztcblxuZXhwb3J0IGNvbnN0IGdldEhvbWVUZXN0aW1vbmlhbCA9ICgpOiBUZXN0aW1vbmlhbCA9PlxuICBjb250ZW50RGF0YS5ob21lLnRlc3RpbW9uaWFsO1xuXG4vLyBQcm9kdWN0cyBwYWdlIGNvbnRlbnRcbmV4cG9ydCBjb25zdCBnZXRQcm9kdWN0c0hlcm8gPSAoKSA9PiBjb250ZW50RGF0YS5wcm9kdWN0cy5oZXJvO1xuXG5leHBvcnQgY29uc3QgZ2V0UHJvZHVjdHNDYXRhbG9nID0gKCk6IFByb2R1Y3RbXSA9PiBjb250ZW50RGF0YS5wcm9kdWN0cy5jYXRhbG9nO1xuXG5leHBvcnQgY29uc3QgZ2V0UHJvZHVjdHNTcGVjcyA9ICgpID0+IGNvbnRlbnREYXRhLnByb2R1Y3RzLnNwZWNpZmljYXRpb25zO1xuXG5leHBvcnQgY29uc3QgZ2V0UHJvZHVjdHNDVEEgPSAoKSA9PiBjb250ZW50RGF0YS5wcm9kdWN0cy5jdGE7XG5cbi8vIEFib3V0IHBhZ2UgY29udGVudFxuZXhwb3J0IGNvbnN0IGdldEFib3V0SGVybyA9ICgpID0+IGNvbnRlbnREYXRhLmFib3V0Lmhlcm87XG5cbmV4cG9ydCBjb25zdCBnZXRBYm91dFN0b3J5ID0gKCkgPT4gY29udGVudERhdGEuYWJvdXQuc3Rvcnk7XG5cbmV4cG9ydCBjb25zdCBnZXRBYm91dFZhbHVlcyA9ICgpOiBGZWF0dXJlW10gPT4gY29udGVudERhdGEuYWJvdXQudmFsdWVzO1xuXG5leHBvcnQgY29uc3QgZ2V0QWJvdXRTdGF0cyA9ICgpOiBTdGF0W10gPT4gY29udGVudERhdGEuYWJvdXQuc3RhdHM7XG5cbmV4cG9ydCBjb25zdCBnZXRBYm91dFRlYW0gPSAoKTogVGVhbU1lbWJlcltdID0+IGNvbnRlbnREYXRhLmFib3V0LnRlYW07XG5cbi8vIENvbnRhY3QgcGFnZSBjb250ZW50XG5leHBvcnQgY29uc3QgZ2V0Q29udGFjdEhlcm8gPSAoKSA9PiBjb250ZW50RGF0YS5jb250YWN0Lmhlcm87XG5cbmV4cG9ydCBjb25zdCBnZXRDb250YWN0TWV0aG9kcyA9ICgpID0+IGNvbnRlbnREYXRhLmNvbnRhY3QubWV0aG9kcztcblxuZXhwb3J0IGNvbnN0IGdldENvbnRhY3RMb2NhdGlvbiA9ICgpID0+IGNvbnRlbnREYXRhLmNvbnRhY3QubG9jYXRpb247XG5cbi8vIEZvb3RlciBjb250ZW50XG5leHBvcnQgY29uc3QgZ2V0Rm9vdGVyTGlua3MgPSAoKSA9PiBjb250ZW50RGF0YS5mb290ZXI7XG5cbi8vIE1ldGFkYXRhXG5leHBvcnQgY29uc3QgZ2V0TWV0YWRhdGEgPSAoKSA9PiBjb250ZW50RGF0YS5tZXRhZGF0YTtcblxuZXhwb3J0IGNvbnN0IGdldFBhZ2VNZXRhZGF0YSA9IChcbiAgcGFnZTogXCJob21lXCIgfCBcInByb2R1Y3RzXCIgfCBcImFib3V0XCIgfCBcImNvbnRhY3RcIlxuKSA9PiBjb250ZW50RGF0YS5tZXRhZGF0YS5wYWdlc1twYWdlXTtcblxuLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGdldCBwcm9kdWN0IGJ5IHNsdWdcbmV4cG9ydCBjb25zdCBnZXRQcm9kdWN0QnlTbHVnID0gKHNsdWc6IHN0cmluZyk6IFByb2R1Y3QgfCB1bmRlZmluZWQgPT5cbiAgY29udGVudERhdGEucHJvZHVjdHMuY2F0YWxvZy5maW5kKChwcm9kdWN0KSA9PiBwcm9kdWN0LnNsdWcgPT09IHNsdWcpO1xuXG4vLyBIZWxwZXIgZnVuY3Rpb24gdG8gYWRkIG5ldyBwcm9kdWN0XG5leHBvcnQgY29uc3QgYWRkUHJvZHVjdCA9IChwcm9kdWN0OiBPbWl0PFByb2R1Y3QsIFwiaWRcIj4pOiBQcm9kdWN0ID0+IHtcbiAgY29uc3QgbmV3SWQgPSBNYXRoLm1heCguLi5jb250ZW50RGF0YS5wcm9kdWN0cy5jYXRhbG9nLm1hcCgocCkgPT4gcC5pZCkpICsgMTtcbiAgY29uc3QgbmV3UHJvZHVjdCA9IHsgLi4ucHJvZHVjdCwgaWQ6IG5ld0lkIH07XG4gIGNvbnRlbnREYXRhLnByb2R1Y3RzLmNhdGFsb2cucHVzaChuZXdQcm9kdWN0KTtcbiAgcmV0dXJuIG5ld1Byb2R1Y3Q7XG59O1xuXG4vLyBFeHBvcnQgdGhlIGVudGlyZSBjb250ZW50IGRhdGEgZm9yIGRpcmVjdCBhY2Nlc3MgaWYgbmVlZGVkXG5leHBvcnQgZGVmYXVsdCBjb250ZW50RGF0YTtcbiJdLCJuYW1lcyI6WyJjb250ZW50RGF0YSIsImdldENvbXBhbnlJbmZvIiwiY29tcGFueSIsImdldENvbnRhY3RJbmZvIiwiY29udGFjdEluZm8iLCJnZXROYXZpZ2F0aW9uIiwibmF2aWdhdGlvbiIsImdldFNvY2lhbE1lZGlhIiwic29jaWFsTWVkaWEiLCJnZXRIb21lSGVybyIsImhvbWUiLCJoZXJvIiwiZ2V0SG9tZUZlYXR1cmVzIiwiZmVhdHVyZXMiLCJnZXRIb21lU2VydmljZXMiLCJzZXJ2aWNlcyIsImdldEhvbWVUZXN0aW1vbmlhbCIsInRlc3RpbW9uaWFsIiwiZ2V0UHJvZHVjdHNIZXJvIiwicHJvZHVjdHMiLCJnZXRQcm9kdWN0c0NhdGFsb2ciLCJjYXRhbG9nIiwiZ2V0UHJvZHVjdHNTcGVjcyIsInNwZWNpZmljYXRpb25zIiwiZ2V0UHJvZHVjdHNDVEEiLCJjdGEiLCJnZXRBYm91dEhlcm8iLCJhYm91dCIsImdldEFib3V0U3RvcnkiLCJzdG9yeSIsImdldEFib3V0VmFsdWVzIiwidmFsdWVzIiwiZ2V0QWJvdXRTdGF0cyIsInN0YXRzIiwiZ2V0QWJvdXRUZWFtIiwidGVhbSIsImdldENvbnRhY3RIZXJvIiwiY29udGFjdCIsImdldENvbnRhY3RNZXRob2RzIiwibWV0aG9kcyIsImdldENvbnRhY3RMb2NhdGlvbiIsImxvY2F0aW9uIiwiZ2V0Rm9vdGVyTGlua3MiLCJmb290ZXIiLCJnZXRNZXRhZGF0YSIsIm1ldGFkYXRhIiwiZ2V0UGFnZU1ldGFkYXRhIiwicGFnZSIsInBhZ2VzIiwiZ2V0UHJvZHVjdEJ5U2x1ZyIsInNsdWciLCJmaW5kIiwicHJvZHVjdCIsImFkZFByb2R1Y3QiLCJuZXdJZCIsIk1hdGgiLCJtYXgiLCJtYXAiLCJwIiwiaWQiLCJuZXdQcm9kdWN0IiwicHVzaCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/content.ts\n"));

/***/ })

});