"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx":
/*!*******************************************************************************************************!*\
  !*** ./src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx ***!
  \*******************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductShowcaseSection: () => (/* binding */ ProductShowcaseSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_QuoteModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/QuoteModal */ \"(app-pages-browser)/./src/components/QuoteModal.tsx\");\n/* harmony import */ var _components_ui_carousel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/carousel */ \"(app-pages-browser)/./src/components/ui/carousel.tsx\");\n/* harmony import */ var _lib_content__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/content */ \"(app-pages-browser)/./src/lib/content.ts\");\n/* __next_internal_client_entry_do_not_use__ ProductShowcaseSection auto */ \n\n\n\n\n\n\n\n\nconst ProductCard = (param)=>{\n    let { product } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n        className: \"border-none shadow-none bg-transparent group cursor-pointer\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n            className: \"p-0 flex flex-col items-start gap-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                    href: \"/products/\".concat(product.slug),\n                    className: \"w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full relative overflow-hidden rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            className: \"w-full h-64 sm:h-80 lg:h-[486px] object-cover transition-transform duration-300 group-hover:scale-105\",\n                            alt: \"\".concat(product.name, \" - \").concat(product.variant, \" spring manufacturing with motor components\"),\n                            src: product.image,\n                            width: 400,\n                            height: 486,\n                            sizes: \"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-start gap-2 w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-start w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/products/\".concat(product.slug),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"w-full mt-[-1.00px] font-text-medium-semi-bold font-[number:var(--text-medium-semi-bold-font-weight)] text-[#01010a] text-[length:var(--text-medium-semi-bold-font-size)] tracking-[var(--text-medium-semi-bold-letter-spacing)] leading-[var(--text-medium-semi-bold-line-height)] [font-style:var(--text-medium-semi-bold-font-style)] hover:text-[#1717c4] transition-colors\",\n                                        children: product.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"w-full font-text-small-normal font-[number:var(--text-small-normal-font-weight)] text-[#01010a] text-[length:var(--text-small-normal-font-size)] tracking-[var(--text-small-normal-letter-spacing)] leading-[var(--text-small-normal-line-height)] [font-style:var(--text-small-normal-font-style)]\",\n                                    children: product.variant\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"w-full font-text-large-semi-bold font-[number:var(--text-large-semi-bold-font-weight)] text-[#01010a] text-[length:var(--text-large-semi-bold-font-size)] tracking-[var(--text-large-semi-bold-letter-spacing)] leading-[var(--text-large-semi-bold-line-height)] [font-style:var(--text-large-semi-bold-font-style)]\",\n                            children: product.price\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QuoteModal__WEBPACK_IMPORTED_MODULE_6__.QuoteModal, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                className: \"w-full mt-2 px-4 py-2 bg-[#1717c4] rounded-[100px] border-b-4 border-[#12129c] font-text-regular-medium font-[number:var(--text-regular-medium-font-weight)] text-white text-[length:var(--text-regular-medium-font-size)] tracking-[var(--text-regular-medium-letter-spacing)] leading-[var(--text-regular-medium-line-height)] [font-style:var(--text-regular-medium-font-style)] hover:bg-[#1414a8] transition-colors\",\n                                children: \"Get Quote\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n            lineNumber: 19,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n        lineNumber: 18,\n        columnNumber: 3\n    }, undefined);\n};\n_c = ProductCard;\nconst ProductShowcaseSection = ()=>{\n    // Get products from centralized content\n    const products = (0,_lib_content__WEBPACK_IMPORTED_MODULE_8__.getProductsCatalog)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"flex flex-col items-center gap-12 lg:gap-20 section-padding w-full bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-col container-responsive items-start gap-12 lg:gap-20 w-full flex\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row items-start lg:items-end justify-between gap-6 lg:gap-8 w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-2xl gap-4 flex-1 flex flex-col items-start\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-flex items-center w-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-heading-tagline font-[number:var(--heading-tagline-font-weight)] text-[#01010a] text-[length:var(--heading-tagline-font-size)] tracking-[var(--heading-tagline-letter-spacing)] leading-[var(--heading-tagline-line-height)] [font-style:var(--heading-tagline-font-style)]\",\n                                        children: \"Innovative\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-start gap-4 w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"w-full font-heading-h2 font-[number:var(--heading-h2-font-weight)] text-[#01010a] text-[length:var(--heading-h2-font-size)] tracking-[var(--heading-h2-letter-spacing)] leading-[var(--heading-h2-line-height)] [font-style:var(--heading-h2-font-style)] text-balance\",\n                                            children: \"Our Product Range\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"w-full font-text-medium-normal font-[number:var(--text-medium-normal-font-weight)] text-[#01010a] text-[length:var(--text-medium-normal-font-size)] tracking-[var(--text-medium-normal-letter-spacing)] leading-[var(--text-medium-normal-line-height)] [font-style:var(--text-medium-normal-font-style)]\",\n                                            children: \"Explore our comprehensive range of high-quality, innovative spring solutions designed for various industrial applications and engineered for superior performance.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/products\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                className: \"w-full lg:w-auto px-6 py-2.5 bg-[#01010a0d] rounded-[100px] border-b-4 [border-bottom-style:solid] border-[#01010a26] hover:bg-[#01010a1a] transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-text-regular-medium font-[number:var(--text-regular-medium-font-weight)] text-[#01010a] text-[length:var(--text-regular-medium-font-size)] tracking-[var(--text-regular-medium-letter-spacing)] leading-[var(--text-regular-medium-line-height)] [font-style:var(--text-regular-medium-font-style)]\",\n                                    children: \"View All Products\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-start gap-8 lg:gap-16 w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"block md:hidden w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_carousel__WEBPACK_IMPORTED_MODULE_7__.Carousel, {\n                                opts: {\n                                    align: \"start\",\n                                    loop: true\n                                },\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_carousel__WEBPACK_IMPORTED_MODULE_7__.CarouselContent, {\n                                        className: \"-ml-2 md:-ml-4\",\n                                        children: products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_carousel__WEBPACK_IMPORTED_MODULE_7__.CarouselItem, {\n                                                className: \"pl-2 md:pl-4 basis-4/5 sm:basis-3/5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCard, {\n                                                    product: product\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, product.id, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_carousel__WEBPACK_IMPORTED_MODULE_7__.CarouselPrevious, {\n                                        className: \"left-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_carousel__WEBPACK_IMPORTED_MODULE_7__.CarouselNext, {\n                                        className: \"right-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex flex-col items-start gap-8 lg:gap-16 w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-12 w-full\",\n                                    children: products.slice(0, 3).map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCard, {\n                                            product: product\n                                        }, product.id, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-12 w-full\",\n                                    children: products.slice(3, 6).map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCard, {\n                                            product: product\n                                        }, product.id, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = ProductShowcaseSection;\nvar _c, _c1;\n$RefreshReg$(_c, \"ProductCard\");\n$RefreshReg$(_c1, \"ProductShowcaseSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\n"));

/***/ })

});