/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fscreens%2FHomeDesktop%2Fsections%2FContactInfoSection%2FContactInfoSection.tsx%22%2C%22ids%22%3A%5B%22ContactInfoSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fscreens%2FHomeDesktop%2Fsections%2FHeaderSection%2FHeaderSection.tsx%22%2C%22ids%22%3A%5B%22HeaderSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fscreens%2FHomeDesktop%2Fsections%2FNavigationBarSection%2FNavigationBarSection.tsx%22%2C%22ids%22%3A%5B%22NavigationBarSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fscreens%2FHomeDesktop%2Fsections%2FProductShowcaseSection%2FProductShowcaseSection.tsx%22%2C%22ids%22%3A%5B%22ProductShowcaseSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fui%2Favatar.tsx%22%2C%22ids%22%3A%5B%22Avatar%22%2C%22AvatarImage%22%2C%22AvatarFallback%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fui%2Fseparator.tsx%22%2C%22ids%22%3A%5B%22Separator%22%5D%7D&server=false!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fscreens%2FHomeDesktop%2Fsections%2FContactInfoSection%2FContactInfoSection.tsx%22%2C%22ids%22%3A%5B%22ContactInfoSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fscreens%2FHomeDesktop%2Fsections%2FHeaderSection%2FHeaderSection.tsx%22%2C%22ids%22%3A%5B%22HeaderSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fscreens%2FHomeDesktop%2Fsections%2FNavigationBarSection%2FNavigationBarSection.tsx%22%2C%22ids%22%3A%5B%22NavigationBarSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fscreens%2FHomeDesktop%2Fsections%2FProductShowcaseSection%2FProductShowcaseSection.tsx%22%2C%22ids%22%3A%5B%22ProductShowcaseSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fui%2Favatar.tsx%22%2C%22ids%22%3A%5B%22Avatar%22%2C%22AvatarImage%22%2C%22AvatarFallback%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fui%2Fseparator.tsx%22%2C%22ids%22%3A%5B%22Separator%22%5D%7D&server=false! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(app-pages-browser)/./node_modules/next/dist/client/image-component.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx */ \"(app-pages-browser)/./src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/screens/HomeDesktop/sections/HeaderSection/HeaderSection.tsx */ \"(app-pages-browser)/./src/components/screens/HomeDesktop/sections/HeaderSection/HeaderSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx */ \"(app-pages-browser)/./src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx */ \"(app-pages-browser)/./src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/avatar.tsx */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/separator.tsx */ \"(app-pages-browser)/./src/components/ui/separator.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZuaXNoYXBhbmNoYWwlMkZEb2N1bWVudHMlMkZHYXVyYXYlMkZQcm9qZWN0cyUyRnNoYWt0aSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGaW1hZ2UtY29tcG9uZW50LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbmlzaGFwYW5jaGFsJTJGRG9jdW1lbnRzJTJGR2F1cmF2JTJGUHJvamVjdHMlMkZzaGFrdGklMkZzcmMlMkZjb21wb25lbnRzJTJGc2NyZWVucyUyRkhvbWVEZXNrdG9wJTJGc2VjdGlvbnMlMkZDb250YWN0SW5mb1NlY3Rpb24lMkZDb250YWN0SW5mb1NlY3Rpb24udHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQ29udGFjdEluZm9TZWN0aW9uJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbmlzaGFwYW5jaGFsJTJGRG9jdW1lbnRzJTJGR2F1cmF2JTJGUHJvamVjdHMlMkZzaGFrdGklMkZzcmMlMkZjb21wb25lbnRzJTJGc2NyZWVucyUyRkhvbWVEZXNrdG9wJTJGc2VjdGlvbnMlMkZIZWFkZXJTZWN0aW9uJTJGSGVhZGVyU2VjdGlvbi50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJIZWFkZXJTZWN0aW9uJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbmlzaGFwYW5jaGFsJTJGRG9jdW1lbnRzJTJGR2F1cmF2JTJGUHJvamVjdHMlMkZzaGFrdGklMkZzcmMlMkZjb21wb25lbnRzJTJGc2NyZWVucyUyRkhvbWVEZXNrdG9wJTJGc2VjdGlvbnMlMkZOYXZpZ2F0aW9uQmFyU2VjdGlvbiUyRk5hdmlnYXRpb25CYXJTZWN0aW9uLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMk5hdmlnYXRpb25CYXJTZWN0aW9uJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbmlzaGFwYW5jaGFsJTJGRG9jdW1lbnRzJTJGR2F1cmF2JTJGUHJvamVjdHMlMkZzaGFrdGklMkZzcmMlMkZjb21wb25lbnRzJTJGc2NyZWVucyUyRkhvbWVEZXNrdG9wJTJGc2VjdGlvbnMlMkZQcm9kdWN0U2hvd2Nhc2VTZWN0aW9uJTJGUHJvZHVjdFNob3djYXNlU2VjdGlvbi50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJQcm9kdWN0U2hvd2Nhc2VTZWN0aW9uJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbmlzaGFwYW5jaGFsJTJGRG9jdW1lbnRzJTJGR2F1cmF2JTJGUHJvamVjdHMlMkZzaGFrdGklMkZzcmMlMkZjb21wb25lbnRzJTJGdWklMkZhdmF0YXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXZhdGFyJTIyJTJDJTIyQXZhdGFySW1hZ2UlMjIlMkMlMjJBdmF0YXJGYWxsYmFjayUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRm5pc2hhcGFuY2hhbCUyRkRvY3VtZW50cyUyRkdhdXJhdiUyRlByb2plY3RzJTJGc2hha3RpJTJGc3JjJTJGY29tcG9uZW50cyUyRnVpJTJGc2VwYXJhdG9yLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlNlcGFyYXRvciUyMiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLG9PQUEwSTtBQUMxSTtBQUNBLHdTQUF1TjtBQUN2TjtBQUNBLG9SQUF3TTtBQUN4TTtBQUNBLGdUQUE2TjtBQUM3TjtBQUNBLHdUQUFtTztBQUNuTztBQUNBLHNMQUFpTDtBQUNqTDtBQUNBLDRMQUF3SiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL25pc2hhcGFuY2hhbC9Eb2N1bWVudHMvR2F1cmF2L1Byb2plY3RzL3NoYWt0aS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9pbWFnZS1jb21wb25lbnQuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkNvbnRhY3RJbmZvU2VjdGlvblwiXSAqLyBcIi9Vc2Vycy9uaXNoYXBhbmNoYWwvRG9jdW1lbnRzL0dhdXJhdi9Qcm9qZWN0cy9zaGFrdGkvc3JjL2NvbXBvbmVudHMvc2NyZWVucy9Ib21lRGVza3RvcC9zZWN0aW9ucy9Db250YWN0SW5mb1NlY3Rpb24vQ29udGFjdEluZm9TZWN0aW9uLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiSGVhZGVyU2VjdGlvblwiXSAqLyBcIi9Vc2Vycy9uaXNoYXBhbmNoYWwvRG9jdW1lbnRzL0dhdXJhdi9Qcm9qZWN0cy9zaGFrdGkvc3JjL2NvbXBvbmVudHMvc2NyZWVucy9Ib21lRGVza3RvcC9zZWN0aW9ucy9IZWFkZXJTZWN0aW9uL0hlYWRlclNlY3Rpb24udHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJOYXZpZ2F0aW9uQmFyU2VjdGlvblwiXSAqLyBcIi9Vc2Vycy9uaXNoYXBhbmNoYWwvRG9jdW1lbnRzL0dhdXJhdi9Qcm9qZWN0cy9zaGFrdGkvc3JjL2NvbXBvbmVudHMvc2NyZWVucy9Ib21lRGVza3RvcC9zZWN0aW9ucy9OYXZpZ2F0aW9uQmFyU2VjdGlvbi9OYXZpZ2F0aW9uQmFyU2VjdGlvbi50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlByb2R1Y3RTaG93Y2FzZVNlY3Rpb25cIl0gKi8gXCIvVXNlcnMvbmlzaGFwYW5jaGFsL0RvY3VtZW50cy9HYXVyYXYvUHJvamVjdHMvc2hha3RpL3NyYy9jb21wb25lbnRzL3NjcmVlbnMvSG9tZURlc2t0b3Avc2VjdGlvbnMvUHJvZHVjdFNob3djYXNlU2VjdGlvbi9Qcm9kdWN0U2hvd2Nhc2VTZWN0aW9uLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXZhdGFyXCIsXCJBdmF0YXJJbWFnZVwiLFwiQXZhdGFyRmFsbGJhY2tcIl0gKi8gXCIvVXNlcnMvbmlzaGFwYW5jaGFsL0RvY3VtZW50cy9HYXVyYXYvUHJvamVjdHMvc2hha3RpL3NyYy9jb21wb25lbnRzL3VpL2F2YXRhci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlNlcGFyYXRvclwiXSAqLyBcIi9Vc2Vycy9uaXNoYXBhbmNoYWwvRG9jdW1lbnRzL0dhdXJhdi9Qcm9qZWN0cy9zaGFrdGkvc3JjL2NvbXBvbmVudHMvdWkvc2VwYXJhdG9yLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fscreens%2FHomeDesktop%2Fsections%2FContactInfoSection%2FContactInfoSection.tsx%22%2C%22ids%22%3A%5B%22ContactInfoSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fscreens%2FHomeDesktop%2Fsections%2FHeaderSection%2FHeaderSection.tsx%22%2C%22ids%22%3A%5B%22HeaderSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fscreens%2FHomeDesktop%2Fsections%2FNavigationBarSection%2FNavigationBarSection.tsx%22%2C%22ids%22%3A%5B%22NavigationBarSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fscreens%2FHomeDesktop%2Fsections%2FProductShowcaseSection%2FProductShowcaseSection.tsx%22%2C%22ids%22%3A%5B%22ProductShowcaseSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fui%2Favatar.tsx%22%2C%22ids%22%3A%5B%22Avatar%22%2C%22AvatarImage%22%2C%22AvatarFallback%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fui%2Fseparator.tsx%22%2C%22ids%22%3A%5B%22Separator%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx":
/*!*******************************************************************************************************!*\
  !*** ./src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx ***!
  \*******************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductShowcaseSection: () => (/* binding */ ProductShowcaseSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_QuoteModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/QuoteModal */ \"(app-pages-browser)/./src/components/QuoteModal.tsx\");\n/* harmony import */ var _components_ui_carousel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/carousel */ \"(app-pages-browser)/./src/components/ui/carousel.tsx\");\n/* __next_internal_client_entry_do_not_use__ ProductShowcaseSection auto */ \n\n\n\n\n\n\n\nconst ProductCard = (param)=>{\n    let { product } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n        className: \"border-none shadow-none bg-transparent group cursor-pointer\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n            className: \"p-0 flex flex-col items-start gap-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                    href: \"/products/\".concat(product.slug),\n                    className: \"w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full relative overflow-hidden rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            className: \"w-full h-64 sm:h-80 lg:h-[486px] object-cover transition-transform duration-300 group-hover:scale-105\",\n                            alt: \"\".concat(product.name, \" - \").concat(product.variant, \" spring manufacturing with motor components\"),\n                            src: product.image,\n                            width: 400,\n                            height: 486,\n                            sizes: \"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-start gap-2 w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-start w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/products/\".concat(product.slug),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"w-full mt-[-1.00px] font-text-medium-semi-bold font-[number:var(--text-medium-semi-bold-font-weight)] text-[#01010a] text-[length:var(--text-medium-semi-bold-font-size)] tracking-[var(--text-medium-semi-bold-letter-spacing)] leading-[var(--text-medium-semi-bold-line-height)] [font-style:var(--text-medium-semi-bold-font-style)] hover:text-[#1717c4] transition-colors\",\n                                        children: product.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"w-full font-text-small-normal font-[number:var(--text-small-normal-font-weight)] text-[#01010a] text-[length:var(--text-small-normal-font-size)] tracking-[var(--text-small-normal-letter-spacing)] leading-[var(--text-small-normal-line-height)] [font-style:var(--text-small-normal-font-style)]\",\n                                    children: product.variant\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"w-full font-text-large-semi-bold font-[number:var(--text-large-semi-bold-font-weight)] text-[#01010a] text-[length:var(--text-large-semi-bold-font-size)] tracking-[var(--text-large-semi-bold-letter-spacing)] leading-[var(--text-large-semi-bold-line-height)] [font-style:var(--text-large-semi-bold-font-style)]\",\n                            children: product.price\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QuoteModal__WEBPACK_IMPORTED_MODULE_6__.QuoteModal, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                className: \"w-full mt-2 px-4 py-2 bg-[#1717c4] rounded-[100px] border-b-4 border-[#12129c] font-text-regular-medium font-[number:var(--text-regular-medium-font-weight)] text-white text-[length:var(--text-regular-medium-font-size)] tracking-[var(--text-regular-medium-letter-spacing)] leading-[var(--text-regular-medium-line-height)] [font-style:var(--text-regular-medium-font-style)] hover:bg-[#1414a8] transition-colors\",\n                                children: \"Get Quote\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n            lineNumber: 19,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n        lineNumber: 18,\n        columnNumber: 3\n    }, undefined);\n};\n_c = ProductCard;\nconst ProductShowcaseSection = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"flex flex-col items-center gap-12 lg:gap-20 section-padding w-full bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-col container-responsive items-start gap-12 lg:gap-20 w-full flex\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row items-start lg:items-end justify-between gap-6 lg:gap-8 w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-2xl gap-4 flex-1 flex flex-col items-start\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-flex items-center w-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-heading-tagline font-[number:var(--heading-tagline-font-weight)] text-[#01010a] text-[length:var(--heading-tagline-font-size)] tracking-[var(--heading-tagline-letter-spacing)] leading-[var(--heading-tagline-line-height)] [font-style:var(--heading-tagline-font-style)]\",\n                                        children: \"Innovative\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-start gap-4 w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"w-full font-heading-h2 font-[number:var(--heading-h2-font-weight)] text-[#01010a] text-[length:var(--heading-h2-font-size)] tracking-[var(--heading-h2-letter-spacing)] leading-[var(--heading-h2-line-height)] [font-style:var(--heading-h2-font-style)] text-balance\",\n                                            children: \"Our Product Range\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"w-full font-text-medium-normal font-[number:var(--text-medium-normal-font-weight)] text-[#01010a] text-[length:var(--text-medium-normal-font-size)] tracking-[var(--text-medium-normal-letter-spacing)] leading-[var(--text-medium-normal-line-height)] [font-style:var(--text-medium-normal-font-style)]\",\n                                            children: \"Explore our comprehensive range of high-quality, innovative spring solutions designed for various industrial applications and engineered for superior performance.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/products\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                className: \"w-full lg:w-auto px-6 py-2.5 bg-[#01010a0d] rounded-[100px] border-b-4 [border-bottom-style:solid] border-[#01010a26] hover:bg-[#01010a1a] transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-text-regular-medium font-[number:var(--text-regular-medium-font-weight)] text-[#01010a] text-[length:var(--text-regular-medium-font-size)] tracking-[var(--text-regular-medium-letter-spacing)] leading-[var(--text-regular-medium-line-height)] [font-style:var(--text-regular-medium-font-style)]\",\n                                    children: \"View All Products\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-start gap-8 lg:gap-16 w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"block md:hidden w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_carousel__WEBPACK_IMPORTED_MODULE_7__.Carousel, {\n                                opts: {\n                                    align: \"start\",\n                                    loop: true\n                                },\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_carousel__WEBPACK_IMPORTED_MODULE_7__.CarouselContent, {\n                                        className: \"-ml-2 md:-ml-4\",\n                                        children: products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_carousel__WEBPACK_IMPORTED_MODULE_7__.CarouselItem, {\n                                                className: \"pl-2 md:pl-4 basis-4/5 sm:basis-3/5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCard, {\n                                                    product: product\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, product.id, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_carousel__WEBPACK_IMPORTED_MODULE_7__.CarouselPrevious, {\n                                        className: \"left-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_carousel__WEBPACK_IMPORTED_MODULE_7__.CarouselNext, {\n                                        className: \"right-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex flex-col items-start gap-8 lg:gap-16 w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-12 w-full\",\n                                    children: products.slice(0, 3).map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCard, {\n                                            product: product\n                                        }, product.id, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-12 w-full\",\n                                    children: products.slice(3, 6).map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCard, {\n                                            product: product\n                                        }, product.id, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n            lineNumber: 62,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = ProductShowcaseSection;\nvar _c, _c1;\n$RefreshReg$(_c, \"ProductCard\");\n$RefreshReg$(_c1, \"ProductShowcaseSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined);\n});\n_c1 = Card;\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c2 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined);\n});\n_c3 = CardHeader;\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c4 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/card.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined);\n});\n_c5 = CardTitle;\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c6 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/card.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined);\n});\n_c7 = CardDescription;\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c8 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/card.tsx\",\n        lineNumber: 59,\n        columnNumber: 3\n    }, undefined);\n});\n_c9 = CardContent;\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c10 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/ui/card.tsx\",\n        lineNumber: 67,\n        columnNumber: 3\n    }, undefined);\n});\n_c11 = CardFooter;\nCardFooter.displayName = \"CardFooter\";\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11;\n$RefreshReg$(_c, \"Card$React.forwardRef\");\n$RefreshReg$(_c1, \"Card\");\n$RefreshReg$(_c2, \"CardHeader$React.forwardRef\");\n$RefreshReg$(_c3, \"CardHeader\");\n$RefreshReg$(_c4, \"CardTitle$React.forwardRef\");\n$RefreshReg$(_c5, \"CardTitle\");\n$RefreshReg$(_c6, \"CardDescription$React.forwardRef\");\n$RefreshReg$(_c7, \"CardDescription\");\n$RefreshReg$(_c8, \"CardContent$React.forwardRef\");\n$RefreshReg$(_c9, \"CardContent\");\n$RefreshReg$(_c10, \"CardFooter$React.forwardRef\");\n$RefreshReg$(_c11, \"CardFooter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/card.tsx\n"));

/***/ })

});