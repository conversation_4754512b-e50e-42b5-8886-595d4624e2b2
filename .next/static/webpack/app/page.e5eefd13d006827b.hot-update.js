/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fscreens%2FHomeDesktop%2Fsections%2FContactInfoSection%2FContactInfoSection.tsx%22%2C%22ids%22%3A%5B%22ContactInfoSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fscreens%2FHomeDesktop%2Fsections%2FHeaderSection%2FHeaderSection.tsx%22%2C%22ids%22%3A%5B%22HeaderSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fscreens%2FHomeDesktop%2Fsections%2FProductShowcaseSection%2FProductShowcaseSection.tsx%22%2C%22ids%22%3A%5B%22ProductShowcaseSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fui%2Favatar.tsx%22%2C%22ids%22%3A%5B%22Avatar%22%2C%22AvatarImage%22%2C%22AvatarFallback%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fui%2Fseparator.tsx%22%2C%22ids%22%3A%5B%22Separator%22%5D%7D&server=false!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fscreens%2FHomeDesktop%2Fsections%2FContactInfoSection%2FContactInfoSection.tsx%22%2C%22ids%22%3A%5B%22ContactInfoSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fscreens%2FHomeDesktop%2Fsections%2FHeaderSection%2FHeaderSection.tsx%22%2C%22ids%22%3A%5B%22HeaderSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fscreens%2FHomeDesktop%2Fsections%2FProductShowcaseSection%2FProductShowcaseSection.tsx%22%2C%22ids%22%3A%5B%22ProductShowcaseSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fui%2Favatar.tsx%22%2C%22ids%22%3A%5B%22Avatar%22%2C%22AvatarImage%22%2C%22AvatarFallback%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fui%2Fseparator.tsx%22%2C%22ids%22%3A%5B%22Separator%22%5D%7D&server=false! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(app-pages-browser)/./node_modules/next/dist/client/image-component.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx */ \"(app-pages-browser)/./src/components/screens/HomeDesktop/sections/ContactInfoSection/ContactInfoSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/screens/HomeDesktop/sections/HeaderSection/HeaderSection.tsx */ \"(app-pages-browser)/./src/components/screens/HomeDesktop/sections/HeaderSection/HeaderSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx */ \"(app-pages-browser)/./src/components/screens/HomeDesktop/sections/ProductShowcaseSection/ProductShowcaseSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/avatar.tsx */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/separator.tsx */ \"(app-pages-browser)/./src/components/ui/separator.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fscreens%2FHomeDesktop%2Fsections%2FContactInfoSection%2FContactInfoSection.tsx%22%2C%22ids%22%3A%5B%22ContactInfoSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fscreens%2FHomeDesktop%2Fsections%2FHeaderSection%2FHeaderSection.tsx%22%2C%22ids%22%3A%5B%22HeaderSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fscreens%2FHomeDesktop%2Fsections%2FProductShowcaseSection%2FProductShowcaseSection.tsx%22%2C%22ids%22%3A%5B%22ProductShowcaseSection%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fui%2Favatar.tsx%22%2C%22ids%22%3A%5B%22Avatar%22%2C%22AvatarImage%22%2C%22AvatarFallback%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnishapanchal%2FDocuments%2FGaurav%2FProjects%2Fshakti%2Fsrc%2Fcomponents%2Fui%2Fseparator.tsx%22%2C%22ids%22%3A%5B%22Separator%22%5D%7D&server=false!\n"));

/***/ })

});