"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx":
/*!***************************************************************************************************!*\
  !*** ./src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx ***!
  \***************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavigationBarSection: () => (/* binding */ NavigationBarSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_MenuIcon_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=MenuIcon,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_MenuIcon_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=MenuIcon,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/navigation-menu */ \"(app-pages-browser)/./src/components/ui/navigation-menu.tsx\");\n/* harmony import */ var _components_QuoteModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/QuoteModal */ \"(app-pages-browser)/./src/components/QuoteModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ NavigationBarSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst NavigationBarSection = ()=>{\n    _s();\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const navigationItems = [\n        {\n            name: \"Home\",\n            href: \"/\"\n        },\n        {\n            name: \"Products\",\n            href: \"/products\"\n        },\n        {\n            name: \"About Us\",\n            href: \"/about\"\n        },\n        {\n            name: \"Contact\",\n            href: \"/contact\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"flex flex-col items-center w-full bg-white border-b border-[#01010a26] sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-16 lg:h-[72px] items-center justify-between container-responsive w-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4 lg:gap-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            className: \"w-16 h-auto aspect-auto lg:w-[250px] lg:h-auto\",\n                            alt: \"Spring Solutions Company Logo\",\n                            src: \"/logo.png\",\n                            width: 84,\n                            height: 36,\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_6__.NavigationMenu, {\n                    className: \"hidden lg:flex absolute left-1/2 transform -translate-x-1/2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_6__.NavigationMenuList, {\n                        className: \"flex items-center gap-6 lg:gap-8\",\n                        children: navigationItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_6__.NavigationMenuItem, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        className: \"font-text-regular-normal text-[#01010a] hover:bg-[#01010a0d] transition-colors\",\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 lg:gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden sm:flex items-center gap-2 lg:gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/contact\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        className: \"px-3 lg:px-5 py-2 bg-[#01010a0d] rounded-[100px] border-b-4 border-[#01010a26] hover:bg-[#01010a1a] text-sm lg:text-base transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-text-regular-medium text-[#01010a] text-[length:var(--text-regular-medium-font-size)] tracking-[var(--text-regular-medium-letter-spacing)] leading-[var(--text-regular-medium-line-height)]\",\n                                            children: \"Contact\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QuoteModal__WEBPACK_IMPORTED_MODULE_7__.QuoteModal, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        className: \"px-3 lg:px-5 py-2 bg-[#1717c4] rounded-[100px] border-b-4 border-[#12129c] hover:bg-[#1414a8] text-sm lg:text-base transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-text-regular-medium text-white text-[length:var(--text-regular-medium-font-size)] tracking-[var(--text-regular-medium-letter-spacing)] leading-[var(--text-regular-medium-line-height)]\",\n                                            children: \"Get Quote\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.Sheet, {\n                            open: isMobileMenuOpen,\n                            onOpenChange: setIsMobileMenuOpen,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetTrigger, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"lg:hidden w-10 h-10 p-0 flex items-center justify-center rounded-full hover:bg-[#01010a0d] transition-all duration-300\",\n                                        \"aria-label\": \"Open mobile menu\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative w-6 h-6 flex items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuIcon_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-6 h-6 transition-all duration-300 \".concat(isMobileMenuOpen ? \"opacity-0 rotate-180 scale-0\" : \"opacity-100 rotate-0 scale-100\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MenuIcon_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-6 h-6 absolute transition-all duration-300 \".concat(isMobileMenuOpen ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 rotate-180 scale-0\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetContent, {\n                                    side: \"right\",\n                                    className: \"w-full h-full max-w-none rounded-none border-none shadow-none bg-white fixed inset-0 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetHeader, {\n                                            className: \"sr-only\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetTitle, {\n                                                children: \"Navigation Menu\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex h-16 items-center justify-between container-responsive w-full border-b border-[#01010a26]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/\",\n                                                className: \"flex-shrink-0\",\n                                                onClick: ()=>setIsMobileMenuOpen(false),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    className: \"w-16 h-7\",\n                                                    alt: \"Spring Solutions Company Logo\",\n                                                    src: \"/company-logo.svg\",\n                                                    width: 84,\n                                                    height: 36,\n                                                    priority: true\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col h-[calc(100vh-4rem)] justify-center items-center container-responsive py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center space-y-8 flex-1 justify-center\",\n                                                    children: navigationItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: item.href,\n                                                            className: \"text-center py-4 px-8 text-[#01010a] font-text-large-semi-bold hover:bg-[#01010a0d] rounded-xl transition-all duration-200 transform hover:scale-[1.05] active:scale-[0.95]\",\n                                                            onClick: ()=>setIsMobileMenuOpen(false),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-2xl\",\n                                                                children: item.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, index, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 21\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center gap-4 w-full max-w-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/contact\",\n                                                            className: \"w-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"w-full px-6 py-4 bg-[#01010a0d] rounded-2xl border-2 border-[#01010a26] hover:bg-[#01010a1a] transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]\",\n                                                                onClick: ()=>setIsMobileMenuOpen(false),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-text-regular-medium text-[#01010a] text-lg\",\n                                                                    children: \"Contact Us\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                                    lineNumber: 169,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                                lineNumber: 164,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QuoteModal__WEBPACK_IMPORTED_MODULE_7__.QuoteModal, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                className: \"w-full px-6 py-4 bg-[#1717c4] rounded-2xl border-2 border-[#12129c] hover:bg-[#1414a8] transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]\",\n                                                                onClick: ()=>setIsMobileMenuOpen(false),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-text-regular-medium text-white text-lg\",\n                                                                    children: \"Get Quote\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                                    lineNumber: 180,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                                lineNumber: 176,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/shakti/src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, undefined);\n};\n_s(NavigationBarSection, \"QerECOS75+B7gv+k3q7FrDf39mc=\");\n_c = NavigationBarSection;\nvar _c;\n$RefreshReg$(_c, \"NavigationBarSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/screens/HomeDesktop/sections/NavigationBarSection/NavigationBarSection.tsx\n"));

/***/ })

});